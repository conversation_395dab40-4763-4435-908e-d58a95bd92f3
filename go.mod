module icode.baidu.com/baidu/cov/iUT

go 1.19

require (
	github.com/BurntSushi/toml v1.4.0
	github.com/gabriel-vasile/mimetype v1.4.2
	github.com/google/go-cmp v0.6.0
	github.com/sirupsen/logrus v1.9.3
	github.com/spf13/cobra v1.7.0
	github.com/spf13/viper v1.18.2
	github.com/stretchr/testify v1.9.0
	github.com/toolkits/file v0.0.0-20160325033739-a5b3c5147e07
	github.com/vibrantbyte/go-antpath v1.1.1
	github.com/waigani/diffparser v0.0.0-20190828052634-7391f219313d
	go.mongodb.org/mongo-driver v1.17.0
	golang.org/x/mod v0.17.0
	golang.org/x/term v0.27.0
	google.golang.org/grpc v1.59.0
	google.golang.org/protobuf v1.33.0
	icode.baidu.com/baidu/cov/smartUT-parser v0.0.0-20250603074723-e03e4397262e
	icode.baidu.com/baidu/go-lib/queue v0.1.0
)

require (
	github.com/deckarep/golang-set/v2 v2.6.0 // indirect
	github.com/dlclark/regexp2 v1.11.2 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/klauspost/compress v1.17.0 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/montanaflynn/stats v0.7.1 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/youmark/pkcs8 v0.0.0-20240726163527-a2c0da244d78 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/sync v0.10.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20231120223509-83a465c0220f // indirect

)

require (
	github.com/antlabs/strsim v0.0.3 // indirect
	github.com/baidubce/bce-qianfan-sdk/go/qianfan v0.0.12
	github.com/baidubce/bce-sdk-go v0.9.164 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-enry/go-enry/v2 v2.8.8 // indirect
	github.com/go-enry/go-oniguruma v1.2.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/pelletier/go-toml/v2 v2.1.0 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/smacker/go-tree-sitter v0.0.0-20240625050157-a31a98a7c0f6 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.9.0 // indirect
	golang.org/x/exp v0.0.0-20230905200255-921286631fa9 // indirect
	golang.org/x/net v0.33.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

//replace icode.baidu.com/baidu/cov/smartUT-parser => ../smartUT-parser
