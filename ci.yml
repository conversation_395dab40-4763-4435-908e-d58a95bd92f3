Global:
    version: 2.0
    group_email: <EMAIL>   # <------ 配置团队邮箱地址，用于接收xx.latest软件版本升级通知邮件

Default:
  profile : [build_linux_x86]

Profiles:
  - profile:
    name : build_linux_x86
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - go: 1.20.latest
      resourceType: SMALL
    build:
      command: make -f Makefile
    check:
      - reuse: TASK
        enable: true
    artifacts:
      release: true
      platform:
        arch: X86_64
        os: LINUX

  - profile:
    name: build_darwin
    mode: AGENT
    environment:
      cluster: MAC
      tools:
        - xcode: 15.0
    build:
      command: sh build_for_mac.sh
    check:
      - reuse: TASK
        enable: true
      - sign: APPLE_CERTIFICATE
        keychain: iut-ios
        certFiles:
          - certFile: certificate/developerID_application.p12
            certPwd: 123456
      - sign: APPLE_PROVISION
        provFiles:
          - provFile: certificate/comateagentdistributionoutsideappstore.provisionprofile
    artifacts:
      release: true
      platform:
        arch: X86_64
        os: DARWIN
