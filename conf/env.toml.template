## UTAgent执行全局配置文件，外部执行必须配置
#
## license key，鉴权使用
# License="2222222222xxxxxxxxx"
#
## 生成阶段的模型配置，生成阶段可支持的模型：
## deepcode、ernie-code2-sft
#[LLMModel.Gen]
#Model = "ernie-code2-sft"
#Endpoint="10.11.133.106:8500"
#Ak=""
#Sk=""
#
## 修复阶段的模型配置，修复阶段可支持的模型：
## deepcode、ernie-code2-sft、ERNIE-Bot、ERNIE-4.0-8K
## 注：EB模型需设置ak和sk
#[LLMModel.Repair]
#Model = "ernie-code2-sft"
#Endpoint="10.11.133.106:8500"
#Ak=""
#Sk=""
