package cmd

import (
	"errors"
	"fmt"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config/authentication"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"path/filepath"
)

var diffParam = models.NewDiffParam(inputParam)

// 二级命令 testMateCli diff
var diffCmd = &cobra.Command{
	Use:   "diff -w <workDir>",
	Short: "为给定的代码变更生成单测",
	Long:  "为给定的代码变更生成单测，可进一步通过include，exclude约束单测生成的范围",

	Run: func(cmd *cobra.Command, args []string) {
		if diffParam.InputParam.IsTriggerFromIDEorICODE() {
			// 关闭日志控制台输出
			logger.Discard()
		}

		// 鉴权
		err := authentication.IsAccessValid(diffParam.InputParam.License)
		if err != nil {
			logger.Error("鉴权失败: %v", err)
			sysErrExit(err)
		}
		//// 获取参数
		//diffParam.Init(cmd)

		logger.Init(diffParam.InputParam.LogPath)
		runDiff(args)
	},

	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		logger.Info("程序运行结束")
	},
}

func runDiff(args []string) {
	defer func() {
		e := recover()
		if e != nil {
			logger.Error("程序出现异常: %v", e)
			sysErrExit(fmt.Errorf("程序出现异常: %v", e))
		}
	}()
	diffParam.PrintParams()
	if msg := diffParam.ParamValid(); len(msg) > 0 {
		logger.Info("【存在非法参数】")
		var errorMsg string
		for paramName, errMsg := range msg {
			logger.ErrorT(1, "! "+paramName+": "+errMsg)
			if errMsg != "" {
				errorMsg = errMsg + ", "
			}
		}
		logger.Error("【请修正上述参数后重新发起任务】")
		// 参数校验失败，退出程序
		sysErrExit(errors.New(fmt.Sprintf("信息校验失败, %s请修改后重新发起任务", errorMsg)))
	}
	// 先创建消息通知，再解析diff。避免因解析超时导致报错
	utGenerator := utagent.NewUTGenerator(diffParam)

	utGenerator.NotifyTaskExpectMsg(constant.TaskExpectedTextForDiff)

	codeInfo := models.BuildCodeRepo(diffParam.GetWorkDir(), diffParam.BaseCommitID)
	diffParam.BaseCommitID = codeInfo.BaseCommitID
	_ = diffParam.CollectSrcMethods()

	var srcFileList []string
	for f := range diffParam.SrcFileMethods {
		srcFileList = append(srcFileList, filepath.Base(f))
	}
	utGenerator.NotifySrcFileScanState(srcFileList)

	utGenerator.EnvPrepare(diffParam)
	utGenerator.CodeRepoInfo = codeInfo
	utGenerator.Run()
}
