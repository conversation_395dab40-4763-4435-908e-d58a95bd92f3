package cmd

import (
	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"os"
)

// 一级命令
var rootCmd = &cobra.Command{
	Use:     "UTAgent",
	Short:   "覆盖率牵引的UTAgent",
	Long:    "基于百度大模型能力，拥有自主生成单测并提升单测覆盖率能力的 UT Agent",
	Version: "2.0.10",

	PersistentPreRun: func(cmd *cobra.Command, args []string) {
	},
	Run: func(cmd *cobra.Command, args []string) {
	},
}

var inputParam = &models.InputParam{}
var genParam = &models.GenParam{InputParam: inputParam}

func init() {
	// 加入二级命令
	rootCmd.AddCommand(genCmd)
	rootCmd.AddCommand(diffCmd)
	rootCmd.AddCommand(methodCmd)

	if !httputil.IsBaiduInnerIp() {
		// 仅非内网环境时，加入setup命令
		rootCmd.AddCommand(setUpCmd)
	}

	if err := viper.BindPFlags(rootCmd.PersistentFlags()); err != nil {
		log.Fatal(err)
	}

	genParam.Init(genCmd)
	diffParam.Init(diffCmd)
	methodParam.Init(methodCmd)
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		log.Fatalln(err)
	}
}

// sysErrExit 函数用于处理系统错误并退出程序
//
// 参数：
// err：系统错误信息
//
// 返回值：无
//
// 当程序运行出错时，调用此函数可以打印错误信息并退出程序
// 如果是在IDE或ICODE中触发的，则会通过JsonRpcData的方式打印错误信息
func sysErrExit(err error) {
	if inputParam.IsTriggerFromIDEorICODE() {
		message := models.NewJsonRpcDataWithStatusFailed(err.Error())
		message.Print()
	}
	os.Exit(1)
}
