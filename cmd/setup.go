package cmd

import (
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config/authentication"
	"icode.baidu.com/baidu/cov/iUT/config/conf_parser"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"os"
)

// 二级命令setup
var setUpCmd = &cobra.Command{
	Use:    "setup",
	Short:  "首次使用UTAgent，请使用setup指令更新任务配置",
	Long:   "任务相关配置会持久化到配置文件中，全局配置文件位置：～/.UTAgent/env.toml",
	Hidden: false,

	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		//logger.Info("程序运行结束")
	},
	Run: func(cmd *cobra.Command, args []string) {
		// 用户提供新的license
		if comateLicense != "" {
			// 第一步：校验新的license
			isValid, err := authentication.CheckLicenseValid(comateLicense)
			if isValid {
				// 第二步：更新配置文件
				conf_parser.SetLicenseToConfFile(comateLicense)
			} else {
				logger.Error(err.Error())
			}
		} else {
			oldLicense := conf_parser.GetLicense()
			if oldLicense == "" {
				logger.Error("请输入license")
			} else {
				isValid, _ := authentication.CheckLicenseValid(oldLicense)
				if isValid {
					logger.Info("配置文件中license仍有效，可继续使用：%s", oldLicense)
				} else {
					if !authentication.SkipLicense() {
						logger.Error("配置文件中license已失效，请使用setup -l命令配置新的合法license")
					}

				}
			}
		}
	},
}

var modelSetupForGenCmd = &cobra.Command{
	Use:    "LLMModel.Gen",
	Short:  "配置UTAgent生产阶段所需的模型参数",
	Hidden: false,

	Run: func(cmd *cobra.Command, args []string) {
		// 更新用户模型配置
		old := conf_parser.GetUserConfig()
		if old != nil && old.LLMModelConfig != nil {
			old.LLMModelConfig.Gen = modelConfForGen
			conf_parser.SetUserConfig(old)
		} else if old == nil {
			conf_parser.SetUserConfig(&models.UserConfig{
				LLMModelConfig: &models.ModelConfigs{
					Gen: modelConfForGen,
				},
			})
		} else {
			old.LLMModelConfig = &models.ModelConfigs{
				Gen: modelConfForGen,
			}
			conf_parser.SetUserConfig(old)
		}
		conf_parser.PrintNewConf()
	},
}

var modelSetupForRepairCmd = &cobra.Command{
	Use:    "LLMModel.Repair",
	Short:  "配置UTAgent修复阶段所需的模型参数",
	Hidden: false,

	Run: func(cmd *cobra.Command, args []string) {
		// 更新用户模型配置
		old := conf_parser.GetUserConfig()
		if old != nil && old.LLMModelConfig != nil {
			old.LLMModelConfig.Repair = modelConfForRepair
			conf_parser.SetUserConfig(old)
		} else if old == nil {
			conf_parser.SetUserConfig(&models.UserConfig{
				LLMModelConfig: &models.ModelConfigs{
					Repair: modelConfForRepair,
				},
			})
		} else {
			old.LLMModelConfig = &models.ModelConfigs{
				Repair: modelConfForRepair,
			}
			conf_parser.SetUserConfig(old)
		}
		conf_parser.PrintNewConf()
	},
}

var comateLicense string
var modelConfForGen = new(llm.ModelConf)
var modelConfForRepair = new(llm.ModelConf)

func init() {
	cmdset := setUpCmd.Flags()
	setUpCmd.AddCommand(modelSetupForRepairCmd)
	setUpCmd.AddCommand(modelSetupForGenCmd)
	cmdset.StringVarP(&comateLicense, "comateLicense", "l", "", "Comate账户license，内网用户无需填写")
	initModelValue(modelSetupForGenCmd, modelConfForGen)
	initModelValue(modelSetupForRepairCmd, modelConfForRepair)

	// 配置文件中参数可扩展……
	cmdset.Parse(os.Args[1:])
}

func initModelValue(command *cobra.Command, c *llm.ModelConf) {
	modelSet := command.Flags()
	modelSet.StringVarP(&c.Model, "model", "", "", "模型名称")
	command.MarkFlagRequired("model")
	modelSet.StringVarP(&c.EndPoint, "endpoint", "", "", "模型地址")
	modelSet.StringVarP(&c.AK, "ak", "", "", "模型访问所需的ak")
	modelSet.StringVarP(&c.SK, "sk", "", "", "模型访问所需的sk")
}
