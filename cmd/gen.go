package cmd

import (
	"errors"
	"fmt"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config/authentication"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent"
	"path/filepath"
	"sort"
)

// 二级命令 testMateCli gen
var genCmd = &cobra.Command{
	Use:   "gen -w <workDir>",
	Short: "为指定的目录或文件生成单测",
	Long:  "为指定的目录或文件生成单测，可通过include和exclude进行路径设置，通过增加迭代来生成更多case",

	Run: func(cmd *cobra.Command, args []string) {
		if genParam.InputParam.IsTriggerFromIDEorICODE() {
			// 关闭日志控制台输出
			logger.Discard()
		}

		// 鉴权
		err := authentication.IsAccessValid(genParam.InputParam.License)
		if err != nil {
			logger.Error("鉴权失败: %v", err)
			sysErrExit(err)
		}
		//// 获取参数
		//genParam.Init(cmd)

		logger.Init(genParam.InputParam.LogPath)
		runGen(args)
	},

	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		logger.Info("程序运行结束")
	},
}

func runGen(args []string) {
	defer func() {
		e := recover()
		if e != nil {
			logger.Error("程序出现异常: %v", e)
			sysErrExit(fmt.Errorf("程序出现异常: %v", e)) // 如果需要的话，可以退出程序
		}
	}()
	genParam.PrintParams()
	if msg := genParam.ParamValid(); len(msg) > 0 {
		logger.Info("【存在非法参数】")
		for paramName, errMsg := range msg {
			logger.ErrorT(1, "! "+paramName+": "+errMsg)
		}
		logger.Error("【请修正上述参数后重新发起任务】")
		// 参数校验失败，退出程序
		sysErrExit(errors.New("参数校验失败，请修改后重新发起任务"))
	}

	utGenerator := utagent.NewUTGenerator(genParam)
	utGenerator.NotifyTaskExpectMsg(constant.TaskExpectedTextForGen)
	if err := genParam.CollectSrcMethods(); err != nil {
		logger.Warn("未收集到待测试的文件: %v", err)
		return
	}
	var srcFileList []string
	for f := range genParam.SrcFileMethods {
		srcFileList = append(srcFileList, filepath.Base(f))
	}
	sort.Strings(srcFileList)
	utGenerator.NotifySrcFileScanState(srcFileList)

	utGenerator.EnvPrepare(genParam)
	utGenerator.Run()
}
