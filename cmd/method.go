package cmd

import (
	"errors"
	"fmt"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config/authentication"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"strings"
)

var methodParam = models.NewMethodParam(inputParam)

// 二级命令 testMateCli method
var methodCmd = &cobra.Command{
	Use:   "method -w <workDir> -i <include> --startLine <startLine> --endLine <endLine>",
	Short: "为给定的方法生成单测",
	Long:  "为给定的方法生成单测，仅支持指定单文件",

	Run: func(cmd *cobra.Command, args []string) {
		if methodParam.InputParam.IsTriggerFromIDEorICODE() {
			// 关闭日志控制台输出
			logger.Discard()
		}

		// 鉴权
		err := authentication.IsAccessValid(methodParam.InputParam.License)
		if err != nil {
			logger.Error("鉴权失败: %v", err)
			sysErrExit(err)
		}
		//// 获取参数
		//methodParam.Init(cmd)

		logger.Init(methodParam.InputParam.LogPath)
		runMethod(args)
	},

	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		logger.Info("程序运行结束")
	},
}

func runMethod(args []string) {
	defer func() {
		e := recover()
		if e != nil {

			logger.Error("程序出现异常: %v", e)
			sysErrExit(fmt.Errorf("程序出现异常: %v", e)) // 如果需要的话，可以退出程序
		}
	}()
	methodParam.PrintParams()
	if msg := methodParam.ParamValid(); len(msg) > 0 {
		logger.Info("【存在非法参数】")
		for paramName, errMsg := range msg {
			logger.ErrorT(1, "! "+paramName+": "+errMsg)
		}
		logger.Error("【请修正上述参数后重新发起任务】")
		// 参数校验失败，退出程序
		sysErrExit(errors.New(fmt.Sprintf("请选择公共的%s方法重新发起任务", methodParam.GetLang())))
	}
	utGenerator := utagent.NewUTGenerator(methodParam)
	err := methodParam.CollectSrcMethods()

	if err != nil {
		logger.Error("获取被测方法信息失败: %s", err.Error())
		// 参数校验失败，退出程序
		sysErrExit(errors.New("未获取到有效的被测方法，请修正后重新发起任务"))
	}

	if len(methodParam.SrcFileMethods) == 0 {
		sysErrExit(errors.New("未获取到有效的被测方法，请修正后重新发起任务"))
	}

	utGenerator.NotifyTaskExpectMsg(fmt.Sprintf(constant.TaskExpectedTextForMethod, strings.Join(methodParam.TargetMethodsName, ", ")))

	utGenerator.NotifySrcFileScanState(methodParam.TargetMethodsName)

	utGenerator.EnvPrepare(methodParam)
	utGenerator.Run()
}
