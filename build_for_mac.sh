#!/bin/bash

# for UTAgent在BCloud云端MAC集群的打包和签名
# 参考文档：http://buildcloud.baidu.com/bcloud/5-best-practice#4.-iOS-%E6%9E%84%E5%BB%BA

set -x

HOMEDIR=`pwd`
OUTDIR=$HOMEDIR/output
ROOT=$BUILD_WORK_ROOT

# Bcloud会通过ci.yml中配置的sign参数进行自动签名，任务结束后会自动签出
# 确认签名是否存在
security set-key-partition-list -S apple-tool:,apple: -k 123456 "$HOME/Library/Keychains/iut-ios.keychain-db"
security list-keychain -s "$HOME/Library/Keychains/iut-ios.keychain-db"
security find-identity -v -p codesigning

# 下载go1.19，mac集群自带的go是1.12
cd $ROOT && wget -q http://cov.baidu.com/ftp/install_tools/go1.19.13.darwin-amd64.tar.gz && tar -zxf go1.19.13.darwin-amd64.tar.gz
export PATH=${ROOT}/go/bin:$PATH
export GOROOT=${ROOT}/go
go version
# 设置环境
go env -w GOPRIVATE=\*.baidu.com                        # 厂内 icode 等是私有库，不需要走代理和验证 sumdb
go env -w GOPROXY=https://goproxy.baidu-int.com,direct 	# 可以下载 github 等外部代码
go env -w CGO_ENABLED=1
go env

build_bin() {
  echo "start build $1 $2"
  GOOS=$1
  GOARCH=$2
  export GOOS=$1 && export GOARCH=$2 && go build -o iUT
  exit_code=$?;
  [[ $exit_code -ne 0 ]] && exit $exit_code;
  ls -l
  # 签名
  codesign --options runtime --sign "Beijing Baidu Netcom Science & Technology Co.,Ltd (B83JBVZ6M5)" iUT
  chmod a+x iUT

  # 打包
  mkdir -p $OUTDIR/$GOOS-$GOARCH
  mv iUT $OUTDIR/$GOOS-$GOARCH/UTAgent
}

cd $HOMEDIR
go mod tidy && go mod download

build_bin darwin amd64
build_bin darwin arm64
