"""
for windows 二进制签名
"""

import base64
import json
import os
import sys
import time

import pyotp
import requests
import rsa
from Crypto.PublicKey import RSA
from requests_toolbelt.multipart.encoder import MultipartEncoder


def main():
    """签名入口"""
    cert = '1'  # 证书类型，1-百度网讯 2-百度中国 3-百度中国EV证书 4-百度网讯EV证书
    sha = 'sha256'  # 'sha256'、'sha1&sha256&unstable'，默认sha256
    policy = '1'  # 1-应用签名 2-驱动签名

    # cov的公共uuap账号
    user = 'coverage'
    # 获取 TOTP的secret https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/HXFtYvbMQj/TNoZ2M6o-t/wEgaluv2bxhIJM
    totp_secret = 'WYEASE2WMSLM72K7RD6VPD6MDOXDTREIGJZV5VZZ2UUAVRDAMJJA'

    if len(sys.argv) > 1:
        prepare_app = sys.argv[1]
    else:
        print("The file to be signed is needed!")
        sys.exit(1)

    if not os.path.exists(prepare_app):
        print("The file to be signed is NOT exists!")
        sys.exit(1)

    ### uuap 认证
    s = requests.Session()
    sso = SSO(username=user, totp_secret=totp_secret)
    # 1.预登录获取登录所需login_ticket, public_key
    login_ticket, public_key = sso.pre_login()

    # 2.返回s_token
    ticket = sso.gen_ticket(login_ticket, public_key)
    # print("ticket :\n%s" % ticket)
    # 3.根据s_token置换jsession_id
    jsession_id = sso.gen_jsession_id(ticket)
    # print("jsession_id :\n%s" % jsession_id)

    # 4.调用服务接口，获取服务接口数据
    cookies = {'SHAREJSESSIONID': jsession_id}

    result = s.get(url=sso.login_url, cookies=cookies)
    # print "result :\n%s" % result.content
    if result.text.find("\"status\":0") == -1:
        print("login failed")
        sys.exit(1)

    if result.status_code == 200 and 'Location' in result.headers and len(result.headers['Location']) > 0:
        # print "result.headers:\n%s" % result.headers['Location']
        result = s.get(result.headers['Location'], timeout=10)
        # print "result2 :\n%s" % result.content

    params = {"signType": cert,
              "file": ("UTAgent.exe", open(prepare_app, "rb"), 'application/octet-stream'),
              "sha": sha,
              "policy": policy,
              "signUser": user}

    multipart_encoder = MultipartEncoder(
        fields=params
    )
    s.headers["Content-Type"] = multipart_encoder.content_type
    print("prepare signing file...")
    result = s.post(url=sso.service_url, data=multipart_encoder, cookies=cookies, timeout=300)
    # print("result.text :\n%s" % result.content)
    resultdata = json.loads(result.text)
    if resultdata['status'] != 0:
        print("sign failed, ERROR info:\n%s" % resultdata['msg'])
        sys.exit(1)
    print("file signed succeed")

    print("downloading signed file...")
    # 下载签名后的文件
    r = requests.get(resultdata['data'])
    with open(prepare_app, "wb") as code:
        code.write(r.content)
    print("downloading signed file succeed! ")


class SSO(object):
    """SSO类"""
    def __init__(self, username, totp_secret):
        """
        初始化
        """
        # 线上环境：http://uuap.baidu.com
        # 测试环境：https://itebeta.baidu.com
        # self.host = 'https://itebeta.baidu.com'
        self.host = 'https://uuap.baidu.com'

        # 线下appkey，在uuap管理平台查看
        # self.app_key = 'uuapclient-7-lscRvezHfK2cgtiP0DJs'
        # 线上appkey
        self.app_key = 'uuapclient-1-sny7264iajlvII4U675I'

        # 访问域名，在uuap管理平台查看
        # self.service_url = 'http://pcsign.dev.weiyun.baidu.com:8905/batchSign256'
        # 线上访问地址
        self.service_url = 'http://sign.baidu.com/batchSign256'

        # self.login_url = 'http://pcsign.dev.weiyun.baidu.com:8905/queryHistoryList?curPage=0'
        # 线上访问地址
        self.login_url = 'http://sign.baidu.com/queryHistoryList?curPage=0'

        # self.service_pass_url = 'http://pcsign.dev.weiyun.baidu.com:8905/shiro-cas'
        # 线上地址
        self.service_pass_url = 'http://sign.baidu.com/shiro-cas'

        # 线上环境：自有账号去EAC账号中心申请
        # 测试环境：自由账号和密码一致即可，不用申请
        self.username = username
        # 一次性密码(TOTP)密钥
        self.totp_secret = totp_secret
        # 新增认证类型:固定为100
        self.type = 100

    def pre_login(self):
        """
        Step1: 调用UUAP的接口，获取登陆相关的信息
        """
        pre_login_url = '%s/apiAuth/preLogin' % self.host
        pre_login_data = {
            'username': self.username,
            'serviceUrl': self.service_url
        }
        pre_login_response = requests.post(pre_login_url, pre_login_data)
        try:
            res = eval(pre_login_response.content.decode())
            if res['code'] == 200 and res['msg'] == 'success':
                login_ticket = res['result']['lt']
                public_key = res['result']['rsaPublicKey']
                return login_ticket, public_key
        except Exception as e:
            print("login failed")
            sys.exit(1)

    def gen_ticket(self, login_ticket, public_key):
        """
        Step2: 调用UUAP的接口签发serviceTicket接口，得到ticket
        :param login_ticket: Step1中返回的login_ticket
        :param public_key: Step1中返回的public_key
        :return:
        """
        gen_ticket_url = '%s/apiAuth/genV1ApiServiceTicket' % self.host
        encode_password = self.encode_password(login_ticket, public_key)
        gen_ticket_data = {
            'username': self.username,
            'password': encode_password,
            'lt': login_ticket,
            'appKey': self.app_key,
            'serviceUrl': self.service_url,
            'type': self.type
        }

        gen_ticket_response = requests.post(gen_ticket_url, gen_ticket_data)
        res = eval(gen_ticket_response.content.decode())
        if res['code'] == 200 and res['msg'] == 'success':
            ticket = res['result']['ticket']
            return ticket

    def gen_jsession_id(self, ticket):
        """
        Step3: 带上Step2得到的ticket访问你要访问的服务接口，获取JSESSIONID
        :param ticket: Step2中返回的ticket
        :return:
        """
        gen_s_token_url = '%s?ticket=%s' % (self.service_pass_url, ticket)
        gen_st_response = requests.get(url=gen_s_token_url, allow_redirects=False)
        return gen_st_response.cookies.get('SHAREJSESSIONID')

    def encode_password(self, login_ticket, public_key):
        """
        签名密码
        :param login_ticket: 预登录接口返回的login_ticket
        :param public_key: 预登录接口返回的public_key
        :return:
        """
        timestamp = int(round(time.time() * 1000))
        # 创建一个 TOTP 对象
        totp = pyotp.TOTP(self.totp_secret)
        # 获取当前时间下的 TOTP 一次性密码
        password = totp.now()
        encode_password = '%s,%s,%s' % (login_ticket, timestamp, password)
        public_key_bytes = base64.b64decode(public_key.encode('utf-8'))
        rsa_key = RSA.importKey(public_key_bytes)
        encrypt_password = rsa.encrypt(encode_password.encode('utf-8'), rsa_key)
        return base64.b64encode(encrypt_password).decode('utf-8')


if __name__ == "__main__":
    main()
