import json
import hashlib
import os
import shutil
import tarfile
from datetime import datetime
import stat


def calculate_sha256(file_path):
    """计算文件的SHA256值"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()


def increment_version(version):
    """将版本号第三位自增"""
    major, minor, patch = map(int, version.split('.'))
    return f"{major}.{minor}.{patch + 1}"


def get_version(current_version):
    """获取新版本号，如果环境变量VERSION不为空，则使用环境变量的值"""
    env_version = os.getenv('VERSION')
    if env_version:
        return env_version
    else:
        return increment_version(current_version)


def add_executable_permission(file_path):
    """给文件添加执行权限"""
    os.chmod(file_path, os.stat(file_path).st_mode | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH)


def create_tar_gz(os_name, arch, version, source_file):
    """将UTAgent二进制文件压缩为tar.gz包"""
    tar_file_name = f"UTAgent-{os_name}-{arch}-{version}.tar.gz"

    # 给文件添加执行权限
    add_executable_permission(source_file)

    # 创建tar.gz包
    with tarfile.open(tar_file_name, "w:gz") as tar:
        tar.add(source_file, arcname=os.path.basename(source_file))
    if os_name == "linux" and arch == "x86":
        latest_tar_file_name = f"UTAgent-{os_name}-{arch}-latest.tar.gz"
        shutil.copy(tar_file_name, latest_tar_file_name)
    return tar_file_name


def update_versions_template(json_file_path):
    """更新版本信息并压缩二进制文件"""
    with open(json_file_path, 'r') as file:
        data = json.load(file)

    # 读取当前版本号并生成新的版本号
    current_version = data['versions'][-1]['version']
    new_version = get_version(current_version)
    print(f"Current version is {current_version}, new version is {new_version}")
    # 获取当前日期
    release_date = datetime.now().strftime("%Y-%m-%d")

    # 压缩并计算SHA256值
    sha256_darwin_x64 = calculate_sha256(create_tar_gz('darwin', 'x86', new_version,
                                                       'build_darwin/darwin-amd64/UTAgent'))
    sha256_darwin_arm64 = calculate_sha256(create_tar_gz('darwin', 'arm64', new_version,
                                                         'build_darwin/darwin-arm64/UTAgent'))
    sha256_linux_x86 = calculate_sha256(create_tar_gz('linux', 'x86', new_version,
                                                      'build_linux_x86/linux-amd64/UTAgent'))

    sha256_windows_386 = calculate_sha256(create_tar_gz('win32', 'ia32', new_version,
                                                        'windows-386/UTAgent.exe'))
    sha256_windows_x86_64 = calculate_sha256(create_tar_gz('win32', 'x86', new_version,
                                                           'windows-amd64/UTAgent.exe'))

    # 创建新的版本信息
    new_version_info = {
        "version": new_version,
        "releaseDate": release_date,
        "sha256": {
            "darwin": {
                "arm64": sha256_darwin_arm64,
                "x86": sha256_darwin_x64
            },
            "linux": {
                "x86": sha256_linux_x86
            },
            "win32": {
                "x86": sha256_windows_x86_64,
                "ia32": sha256_windows_386
            }
        },
        "compatibility": {
            "vscode": ">1.70.1"
        },
        "deprecated": False
    }

    # 将新的版本信息追加到versions中
    data['versions'].append(new_version_info)

    # 将更新后的数据写回JSON文件
    with open(json_file_path, 'w') as file:
        json.dump(data, file, indent=4)


# 调用函数更新版本信息并压缩二进制文件
update_versions_template('versions.json')
