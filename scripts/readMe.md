## 环境准备
### 下载bcecmd 
  bcecmd 主要用于上次资料到bos系统中，不同系统需要安装不同的包，具体看下面资料
  1、下载系统对应的bcecmd包，然后解压
  2、赋值可执行权限 chmod 777 bcecmd 
  2、设置环境变量，使其全局可以执行，bcecmd -h 查看运行情况
  参考资料： https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/XlJgy-Ki9w/xt7S40_t30/BB3icLrjeBwiTB#anchor-494ec321-f928-11ed-b32d-692d6c3c175c
### 设置 bos秘钥
  下载秘钥包 wget -q https://baidu-coverage.bj.bcebos.com/tools/bce_token.tar.gz --no-check-certificate
  将其解药到 ~/ 当前目录即可
## 发版
  运行脚本 pack.sh 即可发版mac包

  