#!/bin/bash

SETUP_DIR="$HOME/.testMateCli"
BIN_DIR="$SETUP_DIR/bin"

PACK_DIR="./pack"

DARWIN_64_NAME="darwin-aarch64"
DARWIN_86_NAME="darwin-x86"
LINUX_86_NAME="linux-x86"

DARWIN_64_PATH="$PACK_DIR/darwin-aarch64"
DARWIN_86_PATH="$PACK_DIR/darwin-x86"
LINUX_86_PATH="$PACK_DIR/linux-x86"

VERSION_PATH="./VERSION"
VERSION_NAME="VERSION"
TESTMATECLI_PATH="testMateCli"
TESTMATECLI_NAME="testMateCli"

INSTALL_SHELL_PATH="scripts/install_testMateCli.sh"

#FTP_PATH="http://cov.baidu.com/ftp/"
FTP_PATH="http://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT"

# 确定要将环境变量的配置写入哪个文件，通常是写入用户的 bashrc 或者 bash_profile。
PROFILE=$HOME/.bashrc
[ -e $HOME/.bash_profile ] && PROFILE=$HOME/.bash_profile

OK=0
ERR=1

pack() {
  # check $HOME/.testMateCli
  if [ ! -e $PACK_DIR ]; then # 如果$SETUP_DIR目录不存在，则创建
    mkdir -p $PACK_DIR
    if [ $? -ne 0 ]; then
      echo "'mkdir -p $PACK_DIR' for packages error!"
      return $ERR
    fi
  fi

  cd ../
  pwd

  # download iUT package
  mkdir -p ${DARWIN_64_PATH}
  mkdir -p ${DARWIN_86_PATH}
  mkdir -p ${LINUX_86_PATH}
  # 环境清理
  rm -rf $DARWIN_64_PATH/**
  rm -rf $DARWIN_86_PATH/**
  rm -rf $LINUX_86_PATH/**
  # VERSION copy
  cp $VERSION_PATH $DARWIN_64_PATH
  cp $VERSION_PATH $DARWIN_86_PATH
  cp $VERSION_PATH $LINUX_86_PATH
  # bin mkdir
  mkdir -p "${DARWIN_64_PATH}/bin"
  mkdir -p "${DARWIN_86_PATH}/bin"
  mkdir -p "${LINUX_86_PATH}/bin"

  VERSION=$(cat $VERSION_PATH)
  echo "当前版本是："$VERSION

  # build darwin-aarch64
  rm -rf $TESTMATECLI_PATH
  export GOOS=darwin && export GOARCH=arm64 && export CGO_ENABLED=1 && go build -o $TESTMATECLI_NAME
  # 检查编译是否成功
  while [ $? -ne 0 ]; do
    sleep 1
  done
  # 编译成功后去移动
  mv $TESTMATECLI_PATH ${DARWIN_64_PATH}/bin
  if [ $? -eq 0 ]; then
    echo "【darwin-aarch64 build成功】已将darwin-aarch64的产物复制到${DARWIN_64_PATH}/bin路径下"
  else
    echo "【darwin-aarch64 build失败】复制aarch64失败，请检查错误信息。"
  fi
  # 打包 tar -cvf testMateCli-darwin-aarch64-1.0.0.4.tar.gz *
  cd ${DARWIN_64_PATH}
  DARWIN_64_TAR="${TESTMATECLI_NAME}-${DARWIN_64_NAME}-${VERSION}.tar.gz"
  DARWIN_64_LATEST_TAR="${TESTMATECLI_NAME}-${DARWIN_64_NAME}-latest.tar.gz"
  DARWIN_64_LATEST="${TESTMATECLI_NAME}-${DARWIN_64_NAME}-latest"
  echo "tar -cvf ${DARWIN_64_TAR} *"
  echo "tar -cvf ${DARWIN_64_LATEST_TAR} *"
  tar -cvf ${DARWIN_64_TAR} *
  tar -cvf ${DARWIN_64_LATEST_TAR} *
  if [ $? -eq 0 ]; then
    echo "【打包成功】"${DARWIN_64_TAR}
    echo "【打包成功】"${DARWIN_64_LATEST_TAR}
  else
    echo "【打包失败】"${DARWIN_64_TAR}
    echo "【打包失败】"${DARWIN_64_LATEST_TAR}
  fi
  cp bin/${TESTMATECLI_NAME} ${DARWIN_64_LATEST}
  cd ../../

  # build darwin-x86
  rm -rf $TESTMATECLI_PATH
  export GOOS=darwin && export GOARCH=amd64 && export CGO_ENABLED=1 && go build -o $TESTMATECLI_NAME
  # 检查编译是否成功
  while [ $? -ne 0 ]; do
    sleep 1
  done
  # 编译成功后去移动
  mv $TESTMATECLI_PATH ${DARWIN_86_PATH}/bin
  if [ $? -eq 0 ]; then
    echo "【build成功】已将darwin-x86的产物复制到${DARWIN_84_PATH}/bin路径下"
  else
    echo "【build失败】复制darwin-x86失败，请检查错误信息。"
  fi
  # 打包 tar -cvf testMateCli-darwin-aarch64-1.0.0.4.tar.gz *
  cd ${DARWIN_86_PATH}
  DARWIN_86_TAR="${TESTMATECLI_NAME}-${DARWIN_86_NAME}-${VERSION}.tar.gz"
  DARWIN_86_LATEST_TAR="${TESTMATECLI_NAME}-${DARWIN_86_NAME}-latest.tar.gz"
  DARWIN_86_LATEST="${TESTMATECLI_NAME}-${DARWIN_86_NAME}-latest"
  echo "tar -cvf ${DARWIN_86_TAR} *"
  echo "tar -cvf ${DARWIN_86_LATEST_TAR} *"
  tar -cvf ${DARWIN_86_TAR} *
  tar -cvf ${DARWIN_86_LATEST_TAR} *
  if [ $? -eq 0 ]; then
    echo "【打包成功】"${DARWIN_86_TAR}
    echo "【打包成功】"${DARWIN_86_LATEST_TAR}
  else
    echo "【打包失败】"${DARWIN_86_TAR}
    echo "【打包失败】"${DARWIN_86_LATEST_TAR}
  fi
  cp bin/${TESTMATECLI_NAME} ${DARWIN_86_LATEST}
  cd ../../

  # bos上传
  PACK_ABS_Path=$(pwd)
  DARWIN_64_LATEST_TAR_PATH=${PACK_ABS_Path}${iUTPath}/pack/${DARWIN_64_NAME}/${DARWIN_64_LATEST_TAR}
  DARWIN_64_TAR_PATH=${PACK_ABS_Path}${iUTPath}/pack/${DARWIN_64_NAME}/${DARWIN_64_TAR}
  DARWIN_64_LATEST_PATH=${PACK_ABS_Path}${iUTPath}/pack/${DARWIN_64_NAME}/${DARWIN_64_LATEST}
  DARWIN_86_LATEST_TAR_PATH=${PACK_ABS_Path}${iUTPath}/pack/${DARWIN_86_NAME}/${DARWIN_86_LATEST_TAR}
  DARWIN_86_TAR_PATH=${PACK_ABS_Path}${iUTPath}/pack/${DARWIN_86_NAME}/${DARWIN_86_TAR}
  DARWIN_86_LATEST_PATH=${PACK_ABS_Path}${iUTPath}/pack/${DARWIN_86_NAME}/${DARWIN_86_LATEST}


  cd  ${PACK_ABS_Path}/scripts
  echo "【上传darwin-64包】"${DARWIN_64_TAR_PATH}
  ./bcecmd bos cp ${DARWIN_64_TAR_PATH} bos:/baidu-coverage/ftp/install_tools/iUT/
  ./bcecmd bos cp ${DARWIN_64_LATEST_TAR_PATH} bos:/baidu-coverage/ftp/install_tools/iUT/
  ./bcecmd bos cp ${DARWIN_64_LATEST_PATH} bos:/baidu-coverage/ftp/install_tools/iUT/

  echo "【上传darwin-86包】"${DARWIN_86_TAR_PATH}
  ./bcecmd bos cp ${DARWIN_86_TAR_PATH} bos:/baidu-coverage/ftp/install_tools/iUT/
  ./bcecmd bos cp ${DARWIN_86_LATEST_TAR_PATH} bos:/baidu-coverage/ftp/install_tools/iUT/
  ./bcecmd bos cp ${DARWIN_86_LATEST_PATH} bos:/baidu-coverage/ftp/install_tools/iUT/


  return $OK
}

pack
