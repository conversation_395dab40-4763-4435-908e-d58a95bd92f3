#!/bin/bash

SETUP_DIR="$HOME/.testMateCli"
BIN_DIR="$SETUP_DIR/bin"

#FTP_PATH="http://cov.baidu.com/ftp/"
FTP_PATH="http://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT"

# 确定要将环境变量的配置写入哪个文件，通常是写入用户的 bashrc 或者 bash_profile。
PROFILE=$HOME/.bashrc
[ -e $HOME/.bash_profile ] && PROFILE=$HOME/.bash_profile

OK=0
ERR=1

install_testMateCli() {
  # check $HOME/.testMateCli
  if [ ! -e $SETUP_DIR ]; then # 如果$SETUP_DIR目录不存在，则创建
    mkdir -p $SETUP_DIR
    if [ $? -ne 0 ]; then
      echo "'mkdir -p $SETUP_DIR' for testMateCli Tool error!"
      return $ERR
    fi
  fi

  # download iUT package
  echo "setup testMateCli ..."

  OS_TYPE=$(uname | tr '[:upper:]' '[:lower:]')  #获取当前操作系统的类型，并将其转换为小写字母

  if [ $? -ne 0 ]; then
    # 在rd本地运行的工具，默认的下载包应该为Darwin
    OS_TYPE="darwin"
    echo "WARN: fail to run 'uname' to check you os type, it will use default value[Darwin]"
  fi

  iUT_NAME="testMateCli-$OS_TYPE"

  OS_ARCH=$(uname -m)
  echo "INFO: detect os_arch is "$OS_TYPE $OS_ARCH

  if [ "$OS_ARCH" = "aarch64" ] || [ "$OS_ARCH" = "arm64" ]; then
    iUT_NAME=$iUT_NAME"-aarch64"
  else
    iUT_NAME=$iUT_NAME"-x86"
  fi
  #

  VERSION=`curl http://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT/VERSION`
  echo $VERSION
  iUT_TAR="${iUT_NAME}-${VERSION}.tar.gz"
  iUT_URL="${FTP_PATH}/${iUT_TAR}"
  #echo "INFO: download tar is ${iUT_TAR}"
  #echo "INFO: download url is ${iUT_URL}"

  cd $SETUP_DIR && rm -rf ./**; wget -q $iUT_URL # 安静模式下载工具包，首次安装时SETUP_DIR路径下没有内容，会导致rm -rf ./**报错，无法wget
  if [ ! -e $SETUP_DIR/$iUT_TAR ]; then
    echo -e "\033[31m ERROR: Fail to download testMateCli package, the cmd is: wget $iUT_URL \033[0m"
    return $ERR
  fi

  # 解压下载的压缩包，删除已经被解压的压缩文件包。并将解压后的文件递归复制到当前文件夹下，删除复制的源目录
  tar -xvf $iUT_TAR && rm -rf $iUT_TAR

  # 赋予 $BIN_DIR 目录下的 icov 文件可执行权限
  chmod +x $BIN_DIR/testMateCli

  add_environment_var $BIN_DIR

  if [ $? -ne 0 ]; then
      echo -e "\033[;31m===> Please run :\033[;32;4m source $PROFILE \033[24m to complete testMateCli install! Then you can start by 'testMateCli -h' \033[0m"
  else
      echo -e "\033[32m===> Setup testMateCli complete. Then you can start by 'testMateCli -h' \033[0m"
  fi

  return $OK
}


# 添加环境变量
add_environment_var() {
  # check PATH configuration
  grep -v "^#" $PROFILE | grep "PATH=$1:\$PATH" >/dev/null 2>&1
  [ $? -eq 0 ] && return $OK

  # if PATH is not set
  echo "PATH=$1:\$PATH" >>$PROFILE
  echo "export PATH" >>$PROFILE
  return $ERR
}

install_testMateCli
