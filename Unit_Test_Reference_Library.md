# 单测参考代码库索引

## Java语言参考代码库

### 1. 基础单测场景

#### 1.1 简单方法测试
**关键词**: `simple method`, `basic test`, `return value`
**代码库路径**: `java/basic/SimpleMethodTest.java`
**适用场景**:
- 纯函数测试（无副作用）
- 简单计算逻辑
- 字符串处理方法
- 数值转换方法

#### 1.2 带参数验证的方法测试
**关键词**: `parameter validation`, `input check`, `IllegalArgumentException`
**代码库路径**: `java/validation/ParameterValidationTest.java`
**适用场景**:
- 参数非空检查
- 参数范围验证
- 参数类型验证
- 业务规则验证

#### 1.3 异常处理测试
**关键词**: `exception handling`, `try-catch`, `throws`, `expected exception`
**代码库路径**: `java/exception/ExceptionHandlingTest.java`
**适用场景**:
- 异常抛出验证
- 异常消息验证
- 异常类型验证
- 异常恢复逻辑

### 2. 对象和集合测试

#### 2.1 POJO对象测试
**关键词**: `POJO`, `getter setter`, `equals hashCode`, `toString`
**代码库路径**: `java/pojo/POJOTest.java`
**适用场景**:
- 实体类属性测试
- equals和hashCode方法测试
- toString方法测试
- 构造函数测试

#### 2.2 集合操作测试
**关键词**: `List`, `Map`, `Set`, `collection`, `stream`
**代码库路径**: `java/collection/CollectionTest.java`
**适用场景**:
- 列表增删改查
- 映射操作测试
- 集合过滤和转换
- Stream API操作

#### 2.3 Builder模式测试
**关键词**: `Builder pattern`, `fluent interface`, `method chaining`
**代码库路径**: `java/builder/BuilderPatternTest.java`
**适用场景**:
- 复杂对象构建
- 链式调用测试
- 可选参数处理
- 不可变对象创建

### 3. 依赖注入和Mock测试

#### 3.1 Spring依赖注入测试
**关键词**: `Spring`, `@Autowired`, `@MockBean`, `@TestConfiguration`
**代码库路径**: `java/spring/SpringDependencyTest.java`
**适用场景**:
- Spring Bean测试
- 依赖注入验证
- 配置类测试
- 条件装配测试

#### 3.2 Mockito Mock测试
**关键词**: `Mockito`, `@Mock`, `when().thenReturn()`, `verify()`
**代码库路径**: `java/mock/MockitoTest.java`
**适用场景**:
- 外部依赖Mock
- 方法调用验证
- 参数捕获验证
- 异常Mock

#### 3.3 数据库访问测试
**关键词**: `JPA`, `Repository`, `@DataJpaTest`, `TestEntityManager`
**代码库路径**: `java/database/DatabaseTest.java`
**适用场景**:
- JPA Repository测试
- 数据库查询测试
- 事务处理测试
- 数据持久化验证

### 4. Web层测试

#### 4.1 Controller测试
**关键词**: `@WebMvcTest`, `MockMvc`, `@RestController`, `HTTP`
**代码库路径**: `java/web/ControllerTest.java`
**适用场景**:
- REST API测试
- 请求参数验证
- 响应格式验证
- HTTP状态码验证

#### 4.2 Service层测试
**关键词**: `@Service`, `business logic`, `transaction`, `@Transactional`
**代码库路径**: `java/service/ServiceTest.java`
**适用场景**:
- 业务逻辑测试
- 事务处理测试
- 服务间调用测试
- 业务异常处理

#### 4.3 过滤器和拦截器测试
**关键词**: `Filter`, `Interceptor`, `@WebFilter`, `HandlerInterceptor`
**代码库路径**: `java/web/FilterInterceptorTest.java`
**适用场景**:
- 请求过滤逻辑
- 权限拦截验证
- 日志记录测试
- 跨域处理测试

### 5. 并发和异步测试

#### 5.1 多线程测试
**关键词**: `Thread`, `ExecutorService`, `CountDownLatch`, `concurrent`
**代码库路径**: `java/concurrent/ThreadTest.java`
**适用场景**:
- 线程安全测试
- 并发执行验证
- 线程池测试
- 同步机制测试

#### 5.2 异步方法测试
**关键词**: `@Async`, `CompletableFuture`, `@EnableAsync`, `async`
**代码库路径**: `java/async/AsyncTest.java`
**适用场景**:
- 异步方法执行
- Future结果验证
- 异步异常处理
- 回调函数测试

## Go语言参考代码库

### 1. 基础函数测试

#### 1.1 纯函数测试
**关键词**: `pure function`, `table driven`, `t.Run`, `basic test`
**代码库路径**: `go/basic/pure_function_test.go`
**适用场景**:
- 数学计算函数
- 字符串处理函数
- 数据转换函数
- 工具函数测试

#### 1.2 错误处理测试
**关键词**: `error handling`, `errors.New`, `fmt.Errorf`, `error interface`
**代码库路径**: `go/error/error_handling_test.go`
**适用场景**:
- 错误返回验证
- 错误类型检查
- 错误消息验证
- 错误包装测试

#### 1.3 表格驱动测试
**关键词**: `table driven test`, `test cases`, `subtests`, `t.Run`
**代码库路径**: `go/table/table_driven_test.go`
**适用场景**:
- 多组输入输出测试
- 边界值测试
- 参数化测试
- 批量场景验证

### 2. 结构体和接口测试

#### 2.1 结构体方法测试
**关键词**: `struct methods`, `receiver`, `pointer receiver`, `value receiver`
**代码库路径**: `go/struct/struct_method_test.go`
**适用场景**:
- 结构体方法测试
- 接收者类型验证
- 状态变更测试
- 方法链调用

#### 2.2 接口实现测试
**关键词**: `interface`, `implementation`, `type assertion`, `interface compliance`
**代码库路径**: `go/interface/interface_test.go`
**适用场景**:
- 接口实现验证
- 多态行为测试
- 接口组合测试
- 类型断言测试

#### 2.3 嵌入和组合测试
**关键词**: `embedding`, `composition`, `anonymous fields`, `method promotion`
**代码库路径**: `go/embed/embedding_test.go`
**适用场景**:
- 结构体嵌入测试
- 方法提升验证
- 组合模式测试
- 匿名字段访问

### 3. 并发测试

#### 3.1 Goroutine测试
**关键词**: `goroutine`, `go keyword`, `WaitGroup`, `channel`
**代码库路径**: `go/goroutine/goroutine_test.go`
**适用场景**:
- 并发执行测试
- Goroutine同步
- 并发安全验证
- 资源竞争检测

#### 3.2 Channel测试
**关键词**: `channel`, `buffered channel`, `select`, `close channel`
**代码库路径**: `go/channel/channel_test.go`
**适用场景**:
- 通道通信测试
- 缓冲通道测试
- Select语句测试
- 通道关闭处理

#### 3.3 同步原语测试
**关键词**: `sync.Mutex`, `sync.RWMutex`, `sync.Once`, `atomic`
**代码库路径**: `go/sync/sync_test.go`
**适用场景**:
- 互斥锁测试
- 读写锁测试
- 单次执行测试
- 原子操作测试

### 4. 包和模块测试

#### 4.1 包级别测试
**关键词**: `package test`, `init function`, `package variables`, `exported`
**代码库路径**: `go/package/package_test.go`
**适用场景**:
- 包初始化测试
- 导出函数测试
- 包级变量测试
- 包间依赖测试

#### 4.2 内部测试vs外部测试
**关键词**: `internal test`, `external test`, `_test package`, `white box`
**代码库路径**: `go/internal/internal_test.go`, `go/external/external_test.go`
**适用场景**:
- 内部实现测试
- 外部接口测试
- 私有函数测试
- 公共API测试

### 5. 高级测试场景

#### 5.1 基准测试
**关键词**: `benchmark`, `b.N`, `b.ResetTimer`, `performance`
**代码库路径**: `go/benchmark/benchmark_test.go`
**适用场景**:
- 性能基准测试
- 内存分配测试
- 算法效率比较
- 优化效果验证

#### 5.2 示例测试
**关键词**: `example test`, `ExampleFunction`, `Output:`, `documentation`
**代码库路径**: `go/example/example_test.go`
**适用场景**:
- 文档示例测试
- 使用方法演示
- 输出格式验证
- API使用指南

#### 5.3 模糊测试
**关键词**: `fuzz test`, `FuzzFunction`, `f.Add`, `random input`
**代码库路径**: `go/fuzz/fuzz_test.go`
**适用场景**:
- 随机输入测试
- 边界条件发现
- 异常输入处理
- 安全漏洞检测

## 测试工具和框架参考

### Java测试工具
- **JUnit 5**: `java/junit5/`
- **Mockito**: `java/mockito/`
- **AssertJ**: `java/assertj/`
- **TestContainers**: `java/testcontainers/`
- **WireMock**: `java/wiremock/`

### Go测试工具
- **Testify**: `go/testify/`
- **GoMock**: `go/gomock/`
- **Ginkgo**: `go/ginkgo/`
- **GoConvey**: `go/goconvey/`
- **Counterfeiter**: `go/counterfeiter/`

## 使用指南

### 检索策略
1. **按场景检索**: 根据测试场景选择对应的代码库路径
2. **按关键词检索**: 使用关键词快速定位相关测试模式
3. **按复杂度检索**: 从基础到高级逐步参考
4. **按工具检索**: 根据使用的测试框架选择对应示例

### 参考原则
1. **结构参考**: 学习测试代码的组织结构
2. **模式参考**: 复用常见的测试模式和最佳实践
3. **断言参考**: 参考合适的断言方法和验证逻辑
4. **数据参考**: 学习测试数据的准备和管理方式
