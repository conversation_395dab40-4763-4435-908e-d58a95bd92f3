package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/toolkits/file"
)

var log = logrus.New()
var logWriter *os.File

func Init(logPath string) {
	// 设置日志输出为多目标
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02T15:04:05",
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			return "", ""
		},
	})

	if logPath != "" {
		if !file.IsExist(logPath) {
			_ = os.MkdirAll(logPath, os.ModePerm)
		}

		logfile := filepath.Join(logPath, fmt.Sprintf("utagent_%s.log", time.Now().Format("2006-01-02")))
		// 创建日志文件
		openFile, err := os.OpenFile(logfile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			panic(err)
		}
		logWriter = openFile
		log.AddHook(&fileHook{
			writer: openFile,
		})
	}
}

type fileHook struct {
	writer io.Writer
}

func (h *fileHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (h *fileHook) Fire(e *logrus.Entry) error {
	level := strings.ToUpper(e.Level.String())
	if len(level) > 5 {
		level = level[:4]
	}
	msg := strings.TrimSuffix(e.Message, " ")
	if strings.HasSuffix(msg, reset) {
		msg = strings.Replace(msg, reset, "", -1)[5:]
	}
	_, err := fmt.Fprintf(h.writer, "%-5s[%s] %s\n", level, e.Time.Format("2006-01-02 15:04:05"), msg)
	return err
}

func DebugMode() {
	log.SetReportCaller(true)
	log.SetLevel(logrus.DebugLevel)
}

func IsDebugMode() bool {
	return log.GetLevel() == logrus.DebugLevel
}

// SetLoggerHook 供utagent调用使用
// 功能：借助外部模块的日志打印，放弃inc内部的打印
func SetLoggerHook(logHook logrus.Hook) {
	log.AddHook(logHook)
	log.SetOutput(io.Discard)
}

func Discard() {
	log.SetOutput(io.Discard)
	logrus.SetOutput(io.Discard)
}

func handleMsg(msg string) string {
	if IsDebugMode() {
		return msg
	}
	maxLen := getOutputWidth()
	if len(msg) < getOutputWidth() {
		// 补齐空格，为了覆盖掉进度打印的全部日志
		msg += strings.Repeat(" ", maxLen-len(msg)+3)
	}
	return msg
}

func Info(arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Info(handleMsg(m))
	}
}

func InfoT(tabNum int, arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Info(handleMsg(strings.Repeat("    ", tabNum) + m))
	}
}

func InfoTGreen(tabNum int, arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Info(handleMsg(PrintGreen(strings.Repeat("    ", tabNum) + m)))
	}
}

func Panic(arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Panic(m)
	}
}

func Fatal(arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Fatal(m)
	}
}

func Warn(arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Warn(handleMsg(m))
	}
}

func WarnT(tabNum int, arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Warn(handleMsg(strings.Repeat("    ", tabNum) + m))
	}
}

func Debug(arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Debug(m)
	}
}

func DebugT(tabNum int, arg0 any, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Debug(strings.Repeat("    ", tabNum) + m)
	}
}

func Error(arg0 string, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Error(m)
	}
}

func ErrorT(tabNum int, arg0 string, args ...any) {
	for _, m := range strings.Split(fmt.Sprintf(fmt.Sprint(arg0), args...), "\n") {
		log.Error(strings.Repeat("    ", tabNum) + m)
	}
}
