package logger

import (
	"fmt"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.org/x/term"
	"icode.baidu.com/baidu/go-lib/queue/queue"
)

const (
	reset   = "\x1b[0m"
	red     = "\x1b[31m"
	green   = "\x1b[32m"
	blue    = "\x1b[34m"
	cyan    = "\x1b[36m"
	yellow  = "\x1b[33m"
	magenta = "\x1b[35m"
	gray    = "\u001B[37m"
)

const (
	Gen    = 0
	Exec   = 1
	Repair = 2
)

var cacheProcessLogger = &sync.Map{}

type ProcessLogger struct {
	uuid     string
	done     chan bool
	messages queue.Queue
	start    bool
	debug    bool
	maxWidth int
}

type message struct {
	msg        string
	insertTime int64
	iter       int
}

func getOutputWidth() int {
	width, _, err := term.GetSize(0)
	if err != nil || width == 0 {
		return 120
	}
	return width - 30
}

// NewProcessLogger 创建一个新的 ProcessLogger 实例
//
// 返回值为一个指向新创建的 ProcessLogger 实例的指针
// Note:
//
//	用于日志的进度轮播打印，debug模式下不进行轮播，顺序打印
func NewProcessLogger() *ProcessLogger {
	l := &ProcessLogger{
		uuid:     strconv.FormatInt(time.Now().UnixMilli(), 10),
		done:     make(chan bool),
		messages: queue.Queue{},
	}
	l.messages.Init()
	if IsDebugMode() {
		l.debug = true
	}
	l.maxWidth = getOutputWidth()
	cacheProcessLogger.Store(l.uuid, l)
	return l
}

func (l *ProcessLogger) startProcess() {
	l.start = true
	c := []string{"\\", "-", "/"}
	maxWidth := l.maxWidth
	go func() {
		l.start = true
		i := 0
		for ; ; i++ {
			select {
			case <-l.done:
				l.start = false
				return
			default:
				if l.messages.Len() == 1 {
					// 只剩1个消息时，进度打印
					data := l.messages.Remove().(*message)
					msg := data.msg
					l.messages.TaskDone()
					if l.debug {
						l.out(msg)
					} else {
						duration := formatDuration(time.Now().UnixMilli() - data.insertTime)
						if len(msg+duration) > maxWidth {
							msg = msg[:maxWidth-20] + "..."
						} else if len(msg+duration) < maxWidth {
							msg += strings.Repeat(" ", maxWidth-len(msg)-len(duration))
						}

						l.out(msg + duration + " " + c[i%len(c)])
						_ = l.messages.Append(data)
					}
				} else if l.messages.Len() > 1 {
					// 多个消息时，顺序打印，打印完出队
					msg := l.messages.Remove().(*message)
					l.out(msg.msg)
					l.messages.TaskDone()
				}
				time.Sleep(200 * time.Millisecond)
			}
		}
	}()
}

func (l *ProcessLogger) out(msg string) {
	if l.debug {
		fmt.Printf("%25s %v\n", " ", msg)
	} else {
		fmt.Printf("%25s %v\r", " ", msg)
	}
}

func (l *ProcessLogger) writeFile(msg string) {
	if logWriter != nil {
		fmt.Fprintf(logWriter, "INFO [%s] %s\n", time.Now().Format("2006-01-02 15:04:05"), msg)
	}
}

func (l *ProcessLogger) Info(arg0 any, args ...any) {
	msg := fmt.Sprintf(fmt.Sprint(arg0), args...)
	l.writeFile(msg)
	if log.Out == io.Discard {
		return
	}
	_ = l.messages.Append(&message{
		msg:        msg,
		insertTime: time.Now().UnixMilli(),
	})
	if !l.start {
		l.startProcess()
	}
}

func (l *ProcessLogger) InfoT(tabNum int, arg0 any, args ...any) {
	l.Info(strings.Repeat(" ", tabNum*4) + fmt.Sprintf(fmt.Sprint(arg0), args...))
}

func (l *ProcessLogger) End(color string, arg0 any, args ...any) {
	if l.start {
		// 防止多次打印
		l.done <- true
		l.start = false
		close(l.done)
	}
	msg := fmt.Sprintf(fmt.Sprint(arg0), args...)
	// 写入日志文件
	l.writeFile(msg)

	if log.Out != io.Discard {
		// 控制台日志打印
		if len(msg) < l.maxWidth {
			msg += strings.Repeat(" ", l.maxWidth-len(msg))
		}
		if color != "" {
			msg = fmt.Sprintf("%s%s%s", color, msg, reset)
		}
		fmt.Println(fmt.Sprintf("%sINFO%s[%s] %s %-20s", cyan, reset,
			time.Now().Format("2006-01-02T15:04:05"),
			msg, " "))
	}
	cacheProcessLogger.Delete(l.uuid)
}

func (l *ProcessLogger) EndT(tabNum int, arg0 any, args ...any) {
	msg := strings.Repeat(" ", tabNum*4) + fmt.Sprintf(fmt.Sprint(arg0), args...)
	l.End("", msg)
}

func (l *ProcessLogger) EndWithColor(level int, tabNum int, arg0 any, args ...any) {
	msg := strings.Repeat(" ", tabNum*4) + fmt.Sprintf(fmt.Sprint(arg0), args...)
	color := ""
	if level == Repair {
		color = yellow
	} else if level == Exec {
		color = blue
	}
	l.End(color, msg)
}

func (l *ProcessLogger) Close() {
	if !l.start {
		// 没有日志进程，不需要close
		return
	}
	l.done <- true
	for l.messages.Len() > 0 {
		msg := l.messages.Remove().(*message)
		fmt.Printf("%25s %v\n", " ", msg.msg)
		l.messages.TaskDone()
		l.writeFile(msg.msg)
	}
}

func formatDuration(ms int64) string {
	s := ms / 1000
	m := s / 60
	h := m / 60
	return fmt.Sprintf("耗时：%d:%d:%d", h, m-h*60, s-m*60)
}

func PrintGreen(str string) string {
	return fmt.Sprintf("%s%s%s", green, str, reset)
}

func Close() {
	// 关闭所有process日志
	cacheProcessLogger.Range(func(key, value any) bool {
		Warn("程序退出，关闭异常进程日志。id：%v", key)
		l := value.(*ProcessLogger)
		l.Close()
		return true
	})
}
