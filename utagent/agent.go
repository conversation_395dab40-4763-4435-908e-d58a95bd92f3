package utagent

import (
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type UTAgent interface {
	ValidEnv() (bool, error)                                                    // 验证环境是否可用
	RunValid(caseInfo *models.CaseInfo) *models.RunResult                       // 执行验证
	Repair(repairInfo *models.RepairInfo) (*models.RepairResult, string, error) // 修复

	CollectCoverageAndIsUp(newCoverage map[string]*coverage.CoverageInfo, cls string) (bool, *coverage.CoverageInfo) // 判断覆盖率是否提升
	GetFileCoverage(classFullPath string) *coverage.CoverageInfo                                                     // 获取文件覆盖率
	GetRangeCoverage(classFullPath string, startLine int, endLine int) *coverage.CoverageInfo                        // 获取范围覆盖率
	Call(genQueue chan<- any)

	GetFileTimer(filePath string) *helper.Timer

	CallLLMGen(queue chan<- any, underTest models.UnderTestInfo, testMethodName string) bool

	GetMemory() *base.Memory

	GetRunCmd() string

	FirstGenerateForMethod() *base.FirstGeneratedInfoForMethod
	GetTestFileContentOnlySuccess(result *models.UtFileResult, underTest models.UnderTestInfo) string

	GetFileCoverageByDiff(cls string) *coverage.CoverageInfo

	PrepareAgentClient()

	GetTestMethod(caseInfo *models.CaseInfo) (interface{}, []*models.Method)

	DetectSrcBug(caseInfo models.CaseInfo, results *models.RunResult) (bool, string)

	MergeLLMResponseToUserTestFile(fileContext interface{}, method *models.Method, caseInfo *models.CaseInfo, testFile *models.TestFile) (result string, err error)
	GetFullClsNameByFilePath(filePath string) string

	AutoDetectAndModifyBuildScript() *models.FrameworkCheck
}
