package gen

import (
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
)

const (
	FirstGen        = "first-gen"         // 首次生成
	CoverageRegen   = "coverage-regen"    // 覆盖率提升生成
	GenWithScenario = "gen-with-scenario" // 场景生成
)

type UtGen interface {
	Predict() (string, error)
	GetName() string
}

type Input struct {
	Language        string
	LanguageVersion string // JDK版本号
	MethodId        string
	TestFilePath    string
	TargetMethod    interface{}
	FileContext     interface{}
	TestFileContext interface{}
	CoverageInfo    *coverage.CoverageInfo
	Framework       string
	TestMethodName  string
}

func (i *Input) GetJavaMethod() *java.Method {
	return i.TargetMethod.(*java.Method)
}

func (i *Input) GetJavaFileContext() (src *java.FileContext, test *java.FileContext) {
	if i.FileContext != nil {
		src = i.FileContext.(*java.FileContext)
	}
	if i.TestFileContext != nil {
		test = i.TestFileContext.(*java.FileContext)
	}
	return
}
