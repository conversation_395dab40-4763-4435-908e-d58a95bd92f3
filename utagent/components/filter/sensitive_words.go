package filter

import (
	"icode.baidu.com/baidu/cov/iUT/logger"
	"os"
	"path/filepath"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
)

type SensitiveWordsChecker struct {
	Words []string
}

// NewSensitiveWordsChecker 函数用于创建一个新的敏感词检查器实例
//
// 参数：
// language string：指定语言类型，用于初始化敏感词列表
// workDir string：指定工作目录，用于查找用户自定义的敏感词忽略文件
//
// 返回值：
// *SensitiveWordsChecker：返回创建好的敏感词检查器实例
func NewSensitiveWordsChecker(language string, workDir string) *SensitiveWordsChecker {
	s := &SensitiveWordsChecker{}
	// 初始化敏感词列表
	s.initialize(language)
	userIgnore := filepath.Join(workDir, ".UTignore")
	if helper.IsFileExist(userIgnore) {
		// 读取用户自定义的忽略文件，将内容解析后追加到敏感词列表中
		content, _ := os.ReadFile(userIgnore)
		s.Words = helper.Union(s.Words, strings.Split(string(content), "\n"))
	}
	return s
}

// initialize 方法用于初始化敏感词列表
// 参数 language 表示语言类型
// 方法内部通过调用配置中的API获取敏感词列表，并将结果解析后赋值给 s.Words
func (s *SensitiveWordsChecker) initialize(language string) {
	api := config.GetHost() + config.GetSensitiveWordsAPIPath
	result, err := httputil.HTTPGet(api, map[string]interface{}{
		"language": language,
	})
	if err != nil {
		logger.Warn("获取敏感词列表失败: %s", err)
		return
	}
	type Response struct {
		Data   []string `json:"data"`
		Result string   `json:"result"`
	}
	var response Response
	_ = helper.MarshalToObject(result, &response)
	if len(response.Data) > 0 {
		s.Words = response.Data
	}
}

// Filter 方法用于检查输入字符串是否包含任何敏感词
// 如果包含敏感词，则返回 true；否则返回 false
func (s *SensitiveWordsChecker) Filter(str string) bool {
	if len(s.Words) == 0 {
		return false
	}
	for _, word := range s.Words {
		if strings.Contains(str, word) {
			logger.Debug("发现内容命中敏感词: %s", word)
			return true
		}
	}
	return false
}
