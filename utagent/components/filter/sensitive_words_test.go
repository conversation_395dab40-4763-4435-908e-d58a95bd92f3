package filter

import (
	"testing"
)

func TestNewSensitive<PERSON>ords<PERSON><PERSON><PERSON>(t *testing.T) {
	testCases := []struct {
		language string
		workDir  string
	}{
		{
			language: "java",
			workDir:  "testdata",
		},
	}
	for _, tc := range testCases {
		checker := NewSensitiveWordsChecker(tc.language, tc.workDir)
		if len(checker.Words) == 0 {
			t.<PERSON><PERSON>("NewSensitiveWordsChecker() = %v, want non-empty", checker.Words)
		}
	}
}

func TestSensitiveWordsChecker_Filter(t *testing.T) {
	testCases := []struct {
		language string
		workDir  string
		input    string
		want     bool
	}{
		{
			language: "java",
			workDir:  "testdata",
			input:    "Files.delete(\"tmp.txt\")",
			want:     true,
		},
		{
			language: "java",
			workDir:  "testdata",
			input:    "This is a test string without sensitive words.",
			want:     false,
		},
	}
	for _, tc := range testCases {
		checker := NewSensitiveWordsChecker(tc.language, tc.workDir)
		got := checker.Filter(tc.input)
		if got != tc.want {
			t.<PERSON><PERSON><PERSON>("SensitiveWordsChecker.Filter(%q) = %v, want %v", tc.input, got, tc.want)
		}
	}
}
