package file_processor

import (
	"fmt"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/cov/smartUT-parser/base"
	"icode.baidu.com/baidu/cov/smartUT-parser/merge"
	"icode.baidu.com/baidu/cov/smartUT-parser/merge/types"
)

func Merge(srcMethodName string, srcPackageName string, llmFileContext interface{},
	methodsNeedMerge []string, userTestFilePath, userTestFileContent string) *types.Output {
	parseOptions := &base.CodeFormatOptions{
		MethodNameToCamelCase:       true,
		AddGeneratedByComateComment: true,
	}

	mergeParams, paramCheckErr := merge.NewMergeParams(srcMethodName, srcPackageName, userTestFilePath, userTestFileContent, methodsNeedMerge, "", llmFileContext, parseOptions)
	if paramCheckErr != nil {
		// 如果合并参数报错，先panic，重点关注此处错误
		panic(paramCheckErr)
	}
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("Recovered from panic: %v\n", r)
			fmt.Printf("Merge panic: Input: %+v\n", mergeParams)
		}
	}()

	output := merge.ExecuteMerge(mergeParams)
	return output
}

func Save(fileContent, filePath string) error {
	// Check if the file exists
	_, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
			return err
		}
		// File does not exist, create the file
		file, err := os.Create(filePath)
		if err != nil {
			return err
		}
		defer file.Close()

		// Write the content to the file
		_, err = file.WriteString(fileContent)
		if err != nil {
			return err
		}
	} else if err == nil {
		// File exists, open it with write and truncate mode
		file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			return err
		}
		defer file.Close()

		// Write the content to the file
		_, err = file.WriteString(fileContent)
		if err != nil {
			return err
		}
	} else {
		// An unexpected error occurred
		return err
	}

	return nil
}
