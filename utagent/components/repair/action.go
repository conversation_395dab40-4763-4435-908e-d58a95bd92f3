package repair

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/utagent/golang/gorepair"
	"path/filepath"
	"strconv"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/javarepair"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type Action interface {
	SetInput(input *models.RepairInput)
	Call() (string, bool, string, error)
	GetName() string
}

type Agent struct {
	actions  []Action
	Symbols  map[string]string
	language string
}

func NewAgent(language string, symbols map[string]string, llmModel *llm.ModelConf) *Agent {
	return &Agent{
		actions:  getRepairChains(language, symbols, llmModel),
		Symbols:  symbols,
		language: language,
	}
}

// Execute 是一个函数，用于修复失败的测试用例
//
// 参数：
// input: *models.RepairInfo - 包含测试用例修复所需的输入数据
//
// 返回值：
// string - 修复后的测试用例内容
// error - 如果在修复过程中发生错误，则返回非零的错误码；否则返回nil
func (a *Agent) Execute(input models.RepairInput) (*models.RepairResult, string, error) {
	var output = input.TestFileContent
	var err error
	if len(a.actions) == 0 {
		return nil, "", fmt.Errorf("no repair actions")
	}
	exitChain := false
	input.RepairId = strconv.FormatInt(time.Now().UnixMicro(), 10)
	// 分析错误类型，并设置到输入中
	input.ErrorType = a.analyzeErrorType(input)
	var mongoID string
	// debug,上线后删除
	helper.SaveTmpFile(filepath.Join(input.ErrorType.Value(), fmt.Sprintf("%s-repair-%s-before.txt", input.TestMethodName, input.RepairId)), output)
	for index, action := range a.actions {
		action.SetInput(&input)
		//logger.InfoT(2, "【用例修复】步骤%d: 修复规则 [%s]，开始修复 ...", index+1, action.GetName())
		output, exitChain, mongoID, err = action.Call()
		// debug,上线后删除
		helper.SaveTmpFile(filepath.Join(input.ErrorType.Value(), "result",
			fmt.Sprintf("repair-%s-step-%d-result.txt", input.RepairId, index+1)), output)
		if err != nil {
			//logger.ErrorT(2, "【用例修复】步骤%d： 修复规则 [%s], 修复失败, 失败原因：%s", index+1, action.GetName(), err)
			continue
		}
		if exitChain {
			// 标识是否提前退出修复操作链
			return &models.RepairResult{RepairContent: output, RepairID: input.RepairId,
				RepairTool: action.GetName(), ErrorType: input.ErrorType}, "", err
		}
		input.TestFileContent = output
	}

	return &models.RepairResult{RepairContent: output, RepairID: input.RepairId,
		RepairTool: a.actions[len(a.actions)-1].GetName(), ErrorType: input.ErrorType}, mongoID, err
}

// getRepairChains 根据语言获取修复链的切片
//
// 参数：
//
//	language string - 指定修复链适用的编程语言
//
// 返回值：
//
//	[]Action - 包含修复动作的切片
func getRepairChains(language string, symbols map[string]string, llmModel *llm.ModelConf) []Action {
	var actions []Action
	if language == constant.LangJava {
		if symbols != nil && len(symbols) > 0 {
			// 如果有符号信息，则先使用规则修复
			actions = append(actions, javarepair.NewSymbolRepairAction(symbols))
		}
		repairAction := javarepair.NewLLMRepairAction(llmModel)
		actions = append(actions, repairAction)
	} else if language == constant.LangGo {
		actions = append(actions, gorepair.NewLLMRepairAction(llmModel))
	} else {
		// TODO: support other languages
		panic("unsupported language")
	}
	//logger.Info("【用例修复组件】初始化完成，修复模型使用：%s", repairAction.LLMModel.Model)
	return actions
}

func (a *Agent) analyzeErrorType(input models.RepairInput) models.ErrorType {
	if a.language == constant.LangJava {
		for _, ce := range input.RunResult.CompileErrors {
			if ce.ErrorType != "ERROR" {
				continue
			}

			errorType := javarepair.GetErrorType(ce.ErrorMessage)
			if errorType != "" {
				return errorType
			}
		}

		for _, re := range input.RunResult.RunResults {
			if re.FailureMessage != "" {
				errorType := javarepair.GetErrorType(re.Exception)
				if errorType != "" {
					return errorType
				}
			}
		}

	}
	return "UNKNOWN"
}
