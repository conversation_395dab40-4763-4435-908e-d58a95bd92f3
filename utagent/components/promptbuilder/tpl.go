package promptbuilder

import "embed"

//go:embed tpl/*
var promptFS embed.FS

// GetPromptTpl 是一个用于从文件系统中读取并返回指定模板内容的函数。
// 如果读取文件时出现错误，则该函数会引发 panic。
//
// 参数：
//
//	name string - 指定要读取的模板文件名（不包含扩展名，扩展名应为.txt），例如："example"。
//
// 返回值：
//
//	string - 返回读取到的模板内容（字符串类型）。
func GetPromptTpl(name string) string {
	content, err := promptFS.ReadFile("tpl/" + name + ".txt")
	if err != nil {
		panic(err)
	}
	return string(content)
}
