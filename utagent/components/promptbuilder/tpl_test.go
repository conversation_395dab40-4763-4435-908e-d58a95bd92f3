package promptbuilder

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetPromptTpl(t *testing.T) {

	tests := []struct {
		name        string
		expected    string
		shouldPanic bool
	}{
		{
			name:        "example",
			expected:    "Hello, {{.Name}}!\n",
			shouldPanic: false,
		},
		{
			name:        "unknown",
			expected:    "",
			shouldPanic: true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			defer func() {
				r := recover()
				if test.shouldPanic {
					assert.NotNil(t, r)
				} else {
					assert.Nil(t, r)
				}
			}()
			assert.Equal(t, test.expected, GetPromptTpl(test.name))
		})
	}
}
