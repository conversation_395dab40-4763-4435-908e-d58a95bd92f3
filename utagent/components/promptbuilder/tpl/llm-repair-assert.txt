你是一位专业的Java语言软件开发人员，非常擅长根据业务代码，用例代码和用例运行的断言报错来修复问题用例。
下面我将提供你业务代码，用例代码，及用例代码运行后的断言错误。请认真阅读，按照我的要求完成任务。

## 业务代码
{{.SrcFileContent}}

## 用例代码
以下为用例文件 `{{.TestFilePath}}` 的第1行到第{{.LineNo}}行代码，为了便于理解，我添加了行号信息。
{{.TestFileContent}}

## 用例执行后断言报错
```
{{.ErrorLog}}
```

## 分析步骤
1. 假定业务代码没有问题，用例执行后断言报错是用例的断言书写有误
2. 结合用例代码执行后的断言错误和用例的断言书写方式，修正用例代码中的断言书写，保证用例执行pass

请基于上述信息和分析步骤，修正问题用例中的断言错误，并完整给出修正后的正确代码。要求如下：
- 删除行号信息
- 完整输出修正后的代码，不能省略原始代码中的任何行
- 仅输出代码，不包含任何介绍文本或解释文本，格式示例：
```java
...
```