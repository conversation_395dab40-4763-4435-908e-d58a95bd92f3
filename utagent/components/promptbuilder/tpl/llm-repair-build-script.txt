请为我给出的{{.ScriptType}}文件内容，添加如下测试依赖：
- org.junit.jupiter:junit-jupiter:5.10.0:test
- org.mockito:mockito-core:5.5.0
- org.mockito:mockito-inline:5.2.0
针对test任务配置junit5执行

## 原始的{{.ScriptType}}文件内容
```
{{.ScriptContent}}
```

请帮我添加要求的依赖，并给出完整的{{.ScriptType}}文件输出。注意：
- 输出必须为``` ```形式
- 在检测到原有{{.ScriptType}}文件中存在springboot的依赖，需要同时添加sprint-test，不存在不添加
- 必须给出完整的{{.ScriptType}}内容，不能对原有的内容做省略
- 在新添加的内容前加入 `generated by Comate` 的注释
- 在添加`surefire`插件时必须考虑java语言版本的兼容性
