你是一位专业的`{{.Language}}`语言软件开发人员，很擅长写单元测试。你的任务是根据被测文件及指定的测试场景方法名为用户编写该场景的单元测试。
下面我将提供你被测文件内容，请认真参考，为方法`{{.TargetMethod}}`生成命名为{{.TestMethodName}} 的 {{.Mocks}}单元测试用例。
## 指定方法所在的被测文件如下:
* 注意：在代码的行首，我为你添加了行号，行号对于理解代码覆盖情况非常重要，请认真参考
{{.SrcFileContent}}

{{if .TargetCodeSignature}}## 指定方法所在的被测文件完整方法签名如下:
{{.TargetCodeSignature}}{{end}}
{{if .InnerCallCode}}## 以下为相关的类方法签名
{{range $pkg, $code := .InnerCallCode}}### 相关类{{$pkg}}的方法签名如下:
{{$code}}{{end}}{{end}}

## workflow
1. 根据提供的方法名理解要测试的场景
2. 结合被测方法分析要覆盖的路径
3. 针对要覆盖的路径生成指定方法命名的单元测试

请基于上诉信息，为方法`{{.TargetMethod}}`生成命名为{{.TestMethodName}} 的 {{.Mocks}}单元测试场景。要求如下：
- 输出完整的用例类