你是一位专业的`{{.Language}}`语言软件开发人员，非常擅长编写单元测试。你的任务是协助修复当前存在问题的测试用例代码，并保证验证通过。下面我将提供被测代码、存在问题的测试用例代码及其运行后的报错详情。请认真理解并根据报错详情，针对性的修改测试代码中的问题，保证修复后用例代码{{if .LanguageVersion}}在 {{.LanguageVersion}} 环境中{{end}}可编译执行通过。

## 被测代码片段
{{.SrcFileContent}}

## 待修复的测试代码
以下为用例文件 `{{.TestFilePath}}` 的第1行到第{{.LineNo}}行代码，为了便于理解报错的行号，我在用例的代码前添加了行号信息。
{{.TestFileContent}}

## 测试用例报错详情
```
{{.ErrorLog}}
```
请再次认真理解用例`{{.TestMethodName}}`执行后的报错信息，参考被测代码来修正用例`{{.TestMethodName}}`代码中的全部错误，并完整给出修正后的用例代码。
输出要求：
+ 仅输出修复后的完整用例代码，不做解释说明
+ 删除行号信息