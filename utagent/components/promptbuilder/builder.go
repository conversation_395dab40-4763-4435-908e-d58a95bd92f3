package promptbuilder

import (
	"bytes"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"text/template"
)

type PromptName string

func (p PromptName) String() string {
	return string(p)
}

const LLMReGen PromptName = "llm-regen"
const LLMGenWithScenario PromptName = "llm-gen-with-scenario"
const LLMRepair PromptName = "llm-repair"

const LLMRepairBuildScript PromptName = "llm-repair-build-script"
const LLMBugDetect PromptName = "llm-bug-detect"
const LLMRepairAssert = "llm-repair-assert"

type Data struct {
	HandleTokenLimit func(tpl *template.Template, originPrompt string, data any, tokenLimit int) string
}

type InputData struct {
	Language            string
	LanguageVersion     string
	TargetMethod        string
	SrcFileContent      string
	SrcFilePath         string
	TestFileContent     string
	TestFilePath        string
	TestMethodName      string
	TargetCodeSignature string
	InnerCallCode       map[string]string
	LineNo              int
	ErrorLog            string

	Mocks string

	CoverageInfo    *coverage.CoverageInfo
	CoveredLines    string
	MissedLines     string
	CoveredBranches string
	MissedBranches  string

	HandleTokenLimit func(tpl *template.Template, originPrompt string, data any, tokenLimit int) string
}

type BuildScriptData struct {
	BuildType    string
	BuildContent string
}

func BuildPrompt(promptType PromptName, data any, tokenLimit int) (string, error) {
	taskTemplate := GetPromptTpl(promptType.String())
	tmpl, err := template.New("prompt").Parse(taskTemplate)
	if err != nil {
		return "", err
	}
	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return "", err
	}

	prompt := buf.String()
	// todo: 暂时不限制token长度
	//if tokenLimit > 0 && len(prompt) > tokenLimit && data.HandleTokenLimit != nil {
	//	return data.HandleTokenLimit(tmpl, prompt, data, tokenLimit), nil
	//}
	return prompt, nil
}
