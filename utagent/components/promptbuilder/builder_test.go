package promptbuilder

import "testing"

func TestBuildPrompt(t *testing.T) {
	tests := []struct {
		name       string
		promptType PromptName
		data       *InputData
		wantErr    bool
	}{
		{
			name:       "simple_prompt",
			promptType: LLMRepair,
			data: &InputData{
				SrcFileContent: `
package com.xxl.job.admin.core.util;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static com.xxl.job.admin.core.util.JacksonUtil.writeValueAsString;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class JacksonUtilTest {

    @Test
    public void shouldWriteValueAsString() {
        //given
        Map<String, String> map = new HashMap<>();
        map.put("aaa", "111");
        map.put("bbb", "222");
        map.put("ccc", value);

        //when
        String json = writeValueAsString(map);

        //then
        assertEquals(json, "{\"aaa\":\"111\",\"bbb\":\"222\"}");
    }
}`,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := BuildPrompt(tt.promptType, tt.data, 0)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildPrompt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}
