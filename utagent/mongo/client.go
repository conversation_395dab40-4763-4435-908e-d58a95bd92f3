package mongo

import (
	"context"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"os"
	"sync"

	"log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var client *mongo.Client
var once sync.Once

func GetClient() *mongo.Client {
	if client != nil {
		return client
	}
	once.Do(func() {
		ctx := context.Background()
		mongoAddress := os.Getenv("mongoAddress")
		if mongoAddress == "" {
			mongoAddress = "mongodb://root:Cov2022!@*************:8888/comateUT"
		}
		//连接到mongodb
		c, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoAddress))
		if err != nil {
			logger.Error("mongodb连接失败")
			return
		}
		//检查连接
		err = c.Ping(ctx, nil)
		if err != nil {
			logger.Error("mongodb连接成功，ping失败")
			return
		}
		client = c
		logger.Info("mongodb连接成功")
	})
	return client
}

type MongoDB[T any] struct {
	database   string
	collection string
}

func NewMGDB[T any](database string, collection string) *MongoDB[T] {
	client = GetClient()
	if client == nil {
		return nil
	}
	return &MongoDB[T]{
		database,
		collection,
	}
}

// 新增一条记录
func (mg *MongoDB[T]) InsertOne(ctx context.Context, value T) *mongo.InsertOneResult {
	result, err := mg.getCollection().InsertOne(ctx, value)
	if err != nil {
		return nil
	}
	return result
}

// 新增多条记录
func (mg *MongoDB[T]) InsertMultiple(ctx context.Context, data []T) *mongo.InsertManyResult {
	var array []interface{}
	for i := 0; i < len(data); i++ {
		array = append(array, data[i])
	}
	result, err := mg.getCollection().InsertMany(ctx, array)
	if err != nil {
		panic(err)
	}
	return result
}

// 根据字段名和值查询一条记录
func (mg *MongoDB[T]) FindOne(ctx context.Context, filter filter) (T, error) {
	var t T
	err := mg.getCollection().FindOne(ctx, filter).Decode(&t)
	if err != nil {
		return t, err
	}
	return t, nil
}

// 根据条件查询多条记录
func (mg *MongoDB[T]) Find(ctx context.Context, filter filter, limit int64) ([]T, error) {
	findOpts := options.Find()
	findOpts.SetLimit(limit)
	cursor, err := mg.getCollection().Find(ctx, filter, findOpts)
	var ts []T
	if err != nil {
		return ts, err
	}
	for cursor.Next(ctx) {
		var t T
		err := cursor.Decode(&t)
		if err != nil {
			return ts, err
		}
		ts = append(ts, t)
	}
	cursor.Close(ctx)
	return ts, nil
}

// 根据条件更新
func (mg *MongoDB[T]) UpdateOne(ctx context.Context, filter filter, update interface{}) (int64, error) {
	result, err := mg.getCollection().UpdateOne(ctx, filter, bson.M{"$set": update})
	return result.ModifiedCount, err
}

// 根据id更新
func (mg *MongoDB[T]) UpdateOneByID(ctx context.Context, id string, update interface{}) (int64, error) {
	result, err := mg.getCollection().UpdateOne(ctx, bson.M{"_id": mg.ObjectID(id)}, update)
	if result != nil {
		return result.ModifiedCount, err
	}

	return 0, err
}

// 更新多个
func (mg *MongoDB[T]) UpdateMany(ctx context.Context, filter filter, update interface{}) (int64, error) {
	result, err := mg.getCollection().UpdateMany(ctx, filter, bson.D{{Key: "$set", Value: update}})
	if result != nil {
		return result.ModifiedCount, err
	}
	return result.ModifiedCount, err
}

// 获取表
func (mg *MongoDB[T]) getCollection() *mongo.Collection {
	return GetClient().Database(mg.database).Collection(mg.collection)
}

// 删除一条记录
func (mg *MongoDB[T]) DeleteOne(ctx context.Context, filter filter) (int64, error) {
	result, err := mg.getCollection().DeleteOne(ctx, filter)
	if result != nil {
		return result.DeletedCount, err
	}
	return result.DeletedCount, err
}

// 根据id删除一条记录
func (mg *MongoDB[T]) DeleteOneByID(ctx context.Context, id string) (int64, error) {
	result, err := mg.getCollection().DeleteOne(ctx, filter{{Key: "_id", Value: mg.ObjectID(id)}})
	if result != nil {
		return result.DeletedCount, err
	}
	return result.DeletedCount, err
}

// 删除多条记录
func (mg *MongoDB[T]) DeleteMany(ctx context.Context, filter filter) (int64, error) {
	result, err := mg.getCollection().DeleteMany(ctx, filter)
	if result != nil {
		return result.DeletedCount, err
	}
	return result.DeletedCount, err
}

// objcetid
func (mg *MongoDB[T]) ObjectID(id string) primitive.ObjectID {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		log.Fatal(err)
	}
	return objectID
}

// 定义过滤器
type filter bson.D

// 匹配字段值大于指定值的文档
func (f filter) GT(key string, value interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$gt", Value: value}}})
	return f
}

// 匹配字段值大于等于指定值的文档
func (f filter) GTE(key string, value interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$gte", Value: value}}})
	return f
}

// 匹配字段值等于指定值的文档
func (f filter) EQ(key string, value interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$eq", Value: value}}})
	return f
}

// 匹配字段值小于指定值的文档
func (f filter) LT(key string, value interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$lt", Value: value}}})
	return f
}

// 匹配字段值小于等于指定值的文档
func (f filter) LET(key string, value interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$let", Value: value}}})
	return f
}

// 匹配字段值不等于指定值的文档，包括没有这个字段的文档
func (f filter) NE(key string, value interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$ne", Value: value}}})
	return f
}

// 匹配字段值等于指定数组中的任何值
func (f filter) IN(key string, value ...interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$in", Value: value}}})
	return f
}

// 字段值不在指定数组或者不存在
func (f filter) NIN(key string, value ...interface{}) filter {
	f = append(f, bson.E{Key: key, Value: bson.D{{Key: "$nin", Value: value}}})
	return f
}

// 创建一个条件查询对象
func Newfilter() filter {
	return filter{}
}
