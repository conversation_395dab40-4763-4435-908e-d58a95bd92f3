package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"strconv"
	"strings"
	"time"
)

type LLMResCase struct {
	ID               primitive.ObjectID `bson:"_id"`
	TaskID           string             `bson:"task_id"`            // 任务ID
	Repo             string             `bson:"repo"`               // 仓库
	Language         string             `bson:"lang"`               // 语言
	MethodName       string             `bson:"method_name"`        // 方法名
	TestFilePath     string             `bson:"test_file_path"`     // 测试路径
	TestFileContent  string             `bson:"test_file_content"`  // 单侧内容
	RunError         string             `bson:"run_error"`          // 运行错误信息
	ErrorType        string             `bson:"error_type"`         // 错误类型
	RepairedPrompt   string             `bson:"repair_prompt"`      // 修复提示
	RepairedTestCase string             `bson:"repaired_test_case"` // 修复后的测试用例
	RepairedTime     int64              `bson:"repair_timestamp"`
	Round            int                `bson:"round"` // 修复轮次
}

var mgdb *MongoDB[LLMResCase]

func Init() {
	if httputil.IsBaiduInnerIp() {
		mgdb = NewMGDB[LLMResCase]("comateUT", "utagent_case_new")
	}
}

func RepairCase(ctx context.Context, filePath, content, methodName, prompt, LLMRes, errorType, runError, mongoId string) string {
	if mgdb == nil {
		return ""
	}
	index := 1
	if strings.Contains(mongoId, "#") {
		parts := strings.Split(mongoId, "#")
		mongoId = parts[0]
		index, _ = strconv.Atoi(parts[1])
		index++
	}
	language := "JAVA"
	if strings.HasSuffix(filePath, "go") {
		language = "GO"
	}
	if constant.WorkDir != "" && strings.HasPrefix(filePath, constant.WorkDir) {
		filePath = filePath[len(constant.WorkDir)+1:]
	}
	cacheCase := LLMResCase{
		ID:              primitive.NewObjectID(),
		TaskID:          constant.TaskID,
		Language:        language,
		Repo:            constant.RepoName,
		TestFileContent: content,
		TestFilePath:    filePath,
		MethodName:      methodName,

		RunError:  runError,
		ErrorType: errorType,

		RepairedPrompt:   prompt,
		RepairedTestCase: LLMRes,

		RepairedTime: time.Now().Unix(),
		Round:        index,
	}
	mgdb.InsertOne(ctx, cacheCase)
	mongoId = cacheCase.ID.Hex()
	logger.Debug("save llm case[%s]  mongo id[%s]", filePath, cacheCase.ID.Hex())
	return mongoId + "#" + strconv.Itoa(index)
}

func MergeFailCase(ctx context.Context, mongoId string, result string) {
	if mgdb == nil || mongoId == "" {
		return
	}
	doc := bson.M{}
	if strings.Contains(mongoId, "#") {
		parts := strings.Split(mongoId, "#")
		mongoId = parts[0]
	}
	doc["repair_merge_fail"] = result
	update := bson.M{"$set": doc}
	mgdb.UpdateOneByID(ctx, mongoId, update)
	logger.Debug("merge fail case  mongo id[%s]", mongoId)
}
func UpdateStatus(ctx context.Context, mongoId string, success bool) {
	if mgdb == nil || mongoId == "" {
		return
	}
	doc := bson.M{}
	if strings.Contains(mongoId, "#") {
		parts := strings.Split(mongoId, "#")
		mongoId = parts[0]
	}
	doc["repair_status"] = success
	doc["repair_valid_timestamp"] = time.Now().Unix()
	update := bson.M{"$set": doc}
	mgdb.UpdateOneByID(ctx, mongoId, update)
	logger.Debug("update case status success[%d], mongo id[%s]", success, mongoId)
}
