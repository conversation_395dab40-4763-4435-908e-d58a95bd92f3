package utagent

import (
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"os"
	"testing"
)

func TestUTGenerator_Run(t *testing.T) {
	os.Setenv("UTAgent_DEBUG", "true")
	logger.Init(".")
	//paths := []string{
	//	"mall-admin/src/main/java/com/macro/mall/controller/UmsAdminController.java",
	//	"mall-admin/src/main/java/com/macro/mall/service/impl/PmsProductServiceImpl.java",
	//	"mall-portal/src/main/java/com/macro/mall/portal/service/impl/AlipayServiceImpl.java",
	//	"mall-admin/src/main/java/com/macro/mall/controller/UmsRoleController.java",
	//	"mall-portal/src/main/java/com/macro/mall/portal/service/impl/UmsMemberServiceImpl.java",
	//	//"mall-portal/src/main/java/com/macro/mall/portal/service/impl/MemberAttentionServiceImpl.java",
	//}

	tests := []struct {
		name          string
		param         *models.GenParam
		wantCall      chan<- interface{}
		wantCallTimes int
		want          interface{}
	}{

		//{
		//	name: "test-go",
		//	param: models.NewGenParam(&models.InputParam{
		//		//WorkDir: "/Users/<USER>/workspace/github_code/xxl-job",
		//		//WorkDir:         "/Users/<USER>/baidu/cov/java-evaluate-data/mall",
		//		//Language:        "java",
		//		//Include:         "mall-admin/src/main/java/com/macro/mall/controller",
		//
		//		WorkDir: "/Users/<USER>/test/baidu/cov/gotest",
		//		//WorkDir:  "/Users/<USER>/workspace/comate-ut/code/intellij-plugin",
		//		Language: "go",
		//		Include:  "util/*.go",
		//
		//		DesiredCoverage: 100,
		//		IterationMax:    1,
		//	}),
		//	wantCall:      make(chan<- interface{}, 1),
		//	wantCallTimes: 1,
		//	want:          nil,
		//},
		{
			name: "test-java",
			param: models.NewGenParam(&models.InputParam{
				//WorkDir: "/Users/<USER>/workspace/github_code/xxl-job",
				//WorkDir:         "/Users/<USER>/baidu/cov/java-evaluate-data/mall",
				//Language:        "java",
				//Include:         "mall-admin/src/main/java/com/macro/mall/controller",

				WorkDir: "/Users/<USER>/workspace/test/crtest",
				//Include: strings.Join(paths, ","),
				Include: "src/main/java/com/baidu/wktest/BubbleSort.java",

				Language:        "java",
				DesiredCoverage: 100,
				IterationMax:    1,
				RepairIterMax:   1,
			}),
			wantCall:      make(chan<- interface{}, 1),
			wantCallTimes: 1,
			want:          nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			generator := NewUTGenerator(tt.param)
			generator.EnvPrepare(tt.param)
			generator.Run()
		})
	}
}

func TestUTGenerator_Diff(t *testing.T) {
	logger.Init(".")
	logger.Discard()
	//t.Setenv("JAVA_HOME", "/Library/Java/JavaVirtualMachines/jdk-11.jdk/Contents/Home")
	//t.Setenv("PATH", "/Users/<USER>/software/gradle-6.8.3/bin"+":"+os.Getenv("PATH"))
	tests := []struct {
		name          string
		param         *models.DiffParam
		wantCall      chan<- interface{}
		wantCallTimes int
		want          interface{}
	}{
		{
			name: "test-java",
			param: models.NewDiffParam(&models.InputParam{
				WorkDir: "/Users/<USER>/workspace/comate-ut/evaluate/java-evaluate-data/xxl-job",
				//Include:         "**/FileUtil.java",
				Language:        "java",
				DesiredCoverage: 100,
				IterationMax:    1,
				Trigger:         "IDE",
				UserName:        "comate-eval",
			}),
			wantCall:      make(chan<- interface{}, 1),
			wantCallTimes: 1,
			want:          nil,
		},
	}

	for _, tt := range tests {
		tt.param.CollectSrcMethods()
		t.Run(tt.name, func(t *testing.T) {
			generator := NewUTGenerator(tt.param)
			generator.EnvPrepare(tt.param)
			generator.Run()
		})
	}
}

func TestUTGenerator_Diff_Go(t *testing.T) {
	logger.Init(".")
	//logger.Discard()
	tests := []struct {
		name          string
		param         *models.DiffParam
		wantCall      chan<- interface{}
		wantCallTimes int
		want          interface{}
	}{
		{
			name: "test-go",
			param: models.NewDiffParam(&models.InputParam{
				WorkDir:         "/Users/<USER>/workspace/comate-ut/code/smartUT-parser",
				Language:        "go",
				DesiredCoverage: 100,
				Include:         "./utils/*.go",
				IterationMax:    1,
				//Trigger:         "ICODE",
				UserName: "comate-eval",
			}),
			wantCall:      make(chan<- interface{}, 1),
			wantCallTimes: 1,
			want:          nil,
		},
	}

	for _, tt := range tests {
		tt.param.CollectSrcMethods()
		t.Run(tt.name, func(t *testing.T) {
			generator := NewUTGenerator(tt.param)
			generator.EnvPrepare(tt.param)
			generator.Run()
		})
	}
}

func TestUTGenerator_Method(t *testing.T) {
	logger.Init(".")
	//os.Setenv("UTAgent_DEBUG", "true")
	//logger.Discard()
	tests := []struct {
		name          string
		param         *models.MethodParam
		wantCall      chan<- interface{}
		wantCallTimes int
		want          interface{}
		startLine     int
		endLine       int
	}{
		{
			name: "test-java",
			param: models.NewMethodParam(&models.InputParam{
				WorkDir:         "/Users/<USER>/workspace/comate-ut/evaluate/java-evaluate-data/xxl-job",
				Language:        "java",
				DesiredCoverage: 100,
				IterationMax:    2,
				RepairIterMax:   2,
				Include:         "xxl-job-core/src/main/java/com/xxl/job/core/util/FileUtil.java",
				//Trigger:         "IDE",
				UserName: "comate-eval",
			}),
			wantCall:      make(chan<- interface{}, 1),
			wantCallTimes: 1,
			want:          nil,
			startLine:     51,
			endLine:       51,
		},
	}

	for _, tt := range tests {
		tt.param.StartLine = tt.startLine
		tt.param.EndLine = tt.endLine
		tt.param.ParamValid()
		if err := tt.param.CollectSrcMethods(); err != nil {
			logger.Warn("未收集到待测试的文件: %v", err)
			return
		}
		t.Run(tt.name, func(t *testing.T) {
			generator := NewUTGenerator(tt.param)
			generator.NotifyTaskExpectMsg("sssssssssss")
			generator.EnvPrepare(tt.param)
			generator.Run()
		})
	}
}

func TestUTGenerator_Method_go(t *testing.T) {
	logger.Init(".")
	//os.Setenv("UTAgent_DEBUG", "true")
	//logger.Discard()
	tests := []struct {
		name          string
		param         *models.MethodParam
		wantCall      chan<- interface{}
		wantCallTimes int
		want          interface{}
		startLine     int
		endLine       int
	}{
		{
			name: "test-go",
			param: models.NewMethodParam(&models.InputParam{
				WorkDir:         "/Users/<USER>/git/baidu/cov/smartUT-parser",
				Language:        "go",
				DesiredCoverage: 100,
				IterationMax:    3,
				RepairIterMax:   3,
				Include:         "utils/common.go",
				//Trigger:         "IDE",
				UserName: "comate-eval",
			}),
			wantCall:      make(chan<- interface{}, 1),
			wantCallTimes: 1,
			want:          nil,
			startLine:     265,
			endLine:       265,
		},
	}

	for _, tt := range tests {
		tt.param.StartLine = tt.startLine
		tt.param.EndLine = tt.endLine
		tt.param.ParamValid()
		if err := tt.param.CollectSrcMethods(); err != nil {
			logger.Warn("未收集到待测试的文件: %v", err)
			return
		}
		t.Run(tt.name, func(t *testing.T) {
			generator := NewUTGenerator(tt.param)
			generator.NotifyTaskExpectMsg("sssssssssss")
			generator.EnvPrepare(tt.param)
			generator.Run()
		})
	}
}

//
//func TestUTGenerator_Run_ForIDE(t *testing.T) {
//	logger.Discard()
//	logger.Init(".")
//	tests := []struct {
//		name          string
//		param         *models.GenParam
//		wantCall      chan<- interface{}
//		wantCallTimes int
//		want          interface{}
//	}{
//		{
//			name: "test-java",
//			param: &models.GenParam{
//				InputParam: &models.InputParam{
//					WorkDir:         "/Users/<USER>/workspace/comate-ut/code/intellij-plugin",
//					Language:        "java",
//					Include:         "src/main/java/com/baidu/comate/intellij/util/MDUtils.java",
//					DesiredCoverage: 100,
//					IterationMax:    1,
//					Trigger:         "IDE",
//				},
//			},
//			wantCall:      make(chan<- interface{}, 1),
//			wantCallTimes: 1,
//			want:          nil,
//		},
//		{
//			name: "test-java-icode",
//			param: &models.GenParam{
//				InputParam: &models.InputParam{
//					WorkDir:         "/Users/<USER>/workspace/comate-ut/evaluate/java-evaluate-data/xxl-job",
//					Language:        "java",
//					Include:         "xxl-job-core/src/main/java/com/xxl/job/core/util/FileUtil.java",
//					DesiredCoverage: 100,
//					IterationMax:    1,
//					Trigger:         "IDE",
//				},
//			},
//			wantCall:      make(chan<- interface{}, 1),
//			wantCallTimes: 1,
//			want:          nil,
//		},
//	}
//
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			generator := NewUTGenerator(tt.param)
//			generator.NotifyTaskExpectMsg("start")
//			generator.EnvPrepare(tt.param)
//			generator.Run()
//		})
//	}
//}
