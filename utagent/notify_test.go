package utagent

import (
	"testing"

	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

// TestNotifyFrameworkResultWithNilNotifier 是用于测试 NotifyFrameworkResultWithNilNotifier
// generated by Comate
func TestNotifyFrameworkResultWithNilNotifier(t *testing.T) {
	generator := &UTGenerator{}
	check := &models.FrameworkCheck{
		State: "success",
		Tools: []models.Tool{
			{Name: "tool1", Version: "1.0.0"},
			{Name: "tool2", Version: "2.0.0"},
		},
		AdjustedFiles: []models.FileDetail{
			{FilePath: "file1.go", FileContent: "file content 1"},
			{FilePath: "file2.go", FileContent: "file content 2"},
		},
	}
	generator.notifyFrameworkResult(check)
	if generator.ideNotifier != nil {
		t.Errorf("Expected ideNotifier to be nil, but it was not")
	}
}
