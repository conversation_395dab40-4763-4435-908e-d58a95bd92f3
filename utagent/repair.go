package utagent

import (
	"context"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

// 用例修复
func (generator *UTGenerator) repairInBackground(ctx context.Context) {
	defer generator.wg.Done()

	timer := helper.NewTimer()
	for {
		select {
		case msg := <-generator.QueueForRepair:
			logger.Info("【修复】队列长度%v", len(generator.QueueForLLMCall))

			func() {
				if msg == nil {
					logger.ErrorT(1, "【修复 】收到空消息")
					return
				}

				repairInfo, ok := msg.(models.RepairInfo)
				if !ok {
					logger.Error("Received invalid data from QueueForRepair, skipping...")
					return
				}

				defer func() {
					if r := recover(); r != nil {
						logger.ErrorT(1, "【修复】处理消息时发生异常：%v", r)
						logger.Error("堆栈信息：\n%s", debug.Stack())
						generator.handleError(repairInfo.CaseInfo.UnderTest, repairInfo.CaseInfo.TestMethodName)
					}
				}()

				underTest := repairInfo.CaseInfo.UnderTest
				if !underTest.Process.IsNeedRepair(underTest.FuncID, repairInfo.CaseInfo.TestMethodName) {
					logger.InfoT(2, "【修复 %s】针对%s的用例%s已无需修复，跳过修复",
						underTest.Process.GetRepairRound(underTest.FuncID, repairInfo.CaseInfo.TestMethodName),
						fmt.Sprintf("%s#%s", underTest.ClassFullName, underTest.GetMethodName()),
						fmt.Sprintf("%s#%s", repairInfo.CaseInfo.TestClassName, repairInfo.CaseInfo.TestMethodName))
					return
				}

				if generator.checkCoverageAndExit(underTest) {
					return
				}

				timer.Start()

				logger.InfoT(2, "【修复 %s】针对%s的用例%s进行修复",
					underTest.Process.GetRepairRound(underTest.FuncID, repairInfo.CaseInfo.TestMethodName),
					fmt.Sprintf("%s#%s", underTest.ClassFullName, underTest.GetMethodName()),
					fmt.Sprintf("%s#%s", repairInfo.CaseInfo.TestClassName, repairInfo.CaseInfo.TestMethodName))

				if !generator.repair(&repairInfo) {
					generator.handleError(repairInfo.CaseInfo.UnderTest, repairInfo.CaseInfo.TestMethodName)
				}
			}()
		case <-ctx.Done():
			logger.Info("【修复】退出修复模块...")
			return
		default:
			time.Sleep(1 * time.Second)
			//logger.Info("[修复队列]剩余长度：%d", len(generator.QueueForRepair))
		}
	}
}

func (generator *UTGenerator) repair(repairInfo *models.RepairInfo) bool {
	l := logger.NewProcessLogger()
	defer l.Close()
	underTest := repairInfo.CaseInfo.UnderTest
	round := underTest.Process.GetRepairRound(underTest.FuncID, repairInfo.CaseInfo.TestMethodName)
	// 修复操作
	if !repairInfo.RunResult.CanRepair() {
		l.InfoT(2, "【修复 %s】执行异常或者错误数目超过阈值！跳过修复！", round)
		return false
	}

	if repairInfo.RunResult.IsCompileSucceeded && !repairInfo.RunResult.IsRunWithoutError {
		logger.WarnT(1, "【修复 %s】执行异常，报错信息：%s",
			round, repairInfo.RunResult.RunErrorMsg)
		return false
	}

	t := helper.NewTimer()
	t.Start()
	generator.UTAgent.GetMemory().Summary.RepairCaseNum.Add(1)
	logger.Info("开始修复: %s", underTest.FuncID)

	repairResult, mongoID, err := generator.UTAgent.Repair(repairInfo)
	t.Pause()

	if repairResult == nil || repairResult.RepairContent == "" || err != nil ||
		!strings.Contains(repairResult.RepairContent, repairInfo.CaseInfo.TestMethodName) { // 修复后未提取到任何代码的情况
		logger.WarnT(2, "【修复 %s】针对%s的用例%s采用%s修复发生异常，耗时 %s",
			round,
			fmt.Sprintf("%s#%s", underTest.ClassFullName, underTest.GetMethodName()),
			fmt.Sprintf("%s#%s", repairInfo.CaseInfo.TestClassName, repairInfo.CaseInfo.TestMethodName),
			"",
			t.ElapsedSecond(),
		)
		return false
	}

	generator.UTAgent.GetMemory().Summary.UpdateRepair(repairResult.RepairTool, false)

	l.InfoT(2, "【修复 %s】针对%s的用例%s采用%s修复完成，耗时 %s，重新发起验证",
		round,
		fmt.Sprintf("%s#%s", underTest.ClassFullName, underTest.GetMethodName()),
		fmt.Sprintf("%s#%s", repairInfo.CaseInfo.TestClassName, repairInfo.CaseInfo.TestMethodName),
		repairResult.RepairTool,
		t.ElapsedSecond(),
	)

	// 将生成的UT内容放入验证队列中进行验证
	caseInfo := models.CaseInfo{
		TestFileContent: repairResult.RepairContent,
		MongoID:         mongoID,
		UnderTest:       underTest,
		RepairResult:    *repairResult,
		TestMethodName:  repairInfo.CaseInfo.TestMethodName,
		TestClassName:   repairInfo.CaseInfo.TestClassName,
		TestFilePath:    repairInfo.CaseInfo.TestFilePath,
	}
	l.EndT(2, "【修复 %s】修复完成，进入验证: %s#%s", round, caseInfo.UnderTest.FuncID, caseInfo.TestMethodName)
	go func() {
		generator.QueueForValidation <- caseInfo
	}()
	return true
}

func (generator *UTGenerator) handleError(underTest models.UnderTestInfo, testMethodName string) {
	logger.InfoT(1, "【修复】针对%s的用例%s修复有误，重新发起生成", underTest.FuncID, testMethodName)
	underTest.Process.DeleteTestMethodIteration(underTest.FuncID, testMethodName)
	generator.checkEnterNextIteration(underTest)
}

//func (generator *UTGenerator) handleRepair(repairInfo *models.RepairInfo, caseInfo models.CaseInfo, testMethodName string) {
//	logger.InfoT(1, "【修复 】针对%s的用例%s进行修复", caseInfo.TestClassName, caseInfo.TestMethodName)
//	res := generator.cache.Dec(caseInfo.UnderTest.FuncID)
//
//	if res || caseInfo.UnderTest.Process.IsInteractionFinished(caseInfo.UnderTest.FuncID) {
//		generator.deleteFromCache(caseInfo.UnderTest.FilePath, caseInfo.UnderTest, models.ReasonMaxIterationReached)
//		return
//	}
//
//	if caseInfo.UnderTest.Process.GetIterNum(caseInfo.UnderTest.FuncID, testMethodName) >= caseInfo.UnderTest.Process.MaxIterNum {
//		return
//	}
//
//	caseInfo.UnderTest.Process.IncrRepairIteration(caseInfo.UnderTest.FuncID, testMethodName)
//	generator.cache.Inc(repairInfo.CaseInfo.UnderTest.FuncID)
//	go func() {
//		generator.QueueForRepair <- *repairInfo
//	}()
//}

// 检查覆盖率是否达标，并退出迭代，删除缓冲
func (generator *UTGenerator) checkCoverageAndExit(underTest models.UnderTestInfo) bool {
	if generator.IsCoverageReached(underTest, float64(generator.param.GetDesiredCoverage())) {
		// 终止迭代
		logger.Info("【覆盖率检查达标】%s", underTest.FuncID)
		underTest.Process.EndProcess()
		//underTest.Process.Cache.Delete(underTest.FuncID)
		generator.deleteFromCache(underTest.FilePath, underTest, models.ReasonCoverageReached)
		return true
	}

	return false
}
