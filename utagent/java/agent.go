package java

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	sitter "github.com/smacker/go-tree-sitter"

	"icode.baidu.com/baidu/cov/iUT/config/conf_parser"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/tools/statistics"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/file_processor"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/bug_detect"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/merge/types"

	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/repair"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/environment"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	parserBase "icode.baidu.com/baidu/cov/smartUT-parser/base"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
	javaparser "icode.baidu.com/baidu/cov/smartUT-parser/java"
)

const LANG = "java"

var junitVersionMap = make(map[string]string)

type UTAgent struct {
	Parser *java.Parser

	Environment *environment.Environment
	Memory      *base.Memory
	JavaMemory  *Memory
	RepairAct   *repair.Agent

	WorkDir       string
	RunCmd        string
	FileMethods   map[string]map[int]int
	FileDiffLines map[string][]int

	MaxIterations   int
	RepairMaxIters  int
	DesiredCoverage int

	llmModelForInfer   *llm.ModelConf
	cache              *helper.SafeMap
	resultPath         string
	isParsedCoverage   bool
	genType            string
	trigger            string
	codeRepoInfo       *models.CodeRepo
	userParams         *models.Params
	totalFunctionCount int
}

func NewUTAgent(params models.Params, cache *helper.SafeMap, codeRepoInfo *models.CodeRepo) (*UTAgent, error) {
	utAgent := UTAgent{
		Parser:          java.NewParser(params.GetWorkDir()),
		Environment:     environment.NewEnvironment(params.GetWorkDir(), params.GetRunCmd(), params.GetUnitFramework()),
		WorkDir:         params.GetWorkDir(),
		RunCmd:          params.GetRunCmd(),
		FileMethods:     params.GetSrcMethods(),
		MaxIterations:   params.GetIterationMax(),
		RepairMaxIters:  params.GetRepairIterMax(),
		DesiredCoverage: params.GetDesiredCoverage(),
		Memory:          base.NewMemory(),
		cache:           cache,
		resultPath:      params.GetResultPath(),
		genType:         params.GetType(),
		codeRepoInfo:    codeRepoInfo,
		trigger:         params.GetTrigger(),
		userParams:      &params,
		JavaMemory:      NewJavaMemory(),
	}

	if len(params.GetSrcMethods()) == 0 {
		return nil, fmt.Errorf("未收集到任何待生成用例的java源文件")
	}

	if params.GetType() == constant.GenerateForDiff {
		utAgent.Memory.Diffs = params.GetDiffs()
		logger.Info("【Diff解析完成】 ✅ ")
		logger.InfoT(1, "Diff变更行范围信息如下：")
		for k, v := range utAgent.Memory.Diffs {
			logger.InfoT(2, "- %s: %v", k, v)
		}
	}

	utAgent.Memory.TotalTimer.Start()
	Symbols := make(map[string]string)
	for k, _ := range utAgent.Parser.Project.Classes {
		Symbols[k[strings.LastIndex(k, ".")+1:len(k)]] = k
	}

	userConfig := conf_parser.GetUserConfig()
	repairLLMModel := params.GetRepairLLMModel()
	if userConfig != nil && userConfig.LLMModelConfig != nil {
		if userConfig.LLMModelConfig.Repair != nil {
			repairLLMModel = userConfig.LLMModelConfig.Repair
		}
		if userConfig.LLMModelConfig.Gen != nil {
			utAgent.llmModelForInfer = userConfig.LLMModelConfig.Gen
		}
	} else {
		utAgent.llmModelForInfer = llm.ModelConfForInnerUtInfer(llm.GetDefaultGenModel())
	}

	extraParams := &llm.ExtraParams{
		UserName:      params.GetUserName(),
		License:       params.GetLicense(),
		RequestSource: "UTAgent_" + params.GetTrigger(),
	}

	utAgent.llmModelForInfer.ExtraPrams = extraParams
	if repairLLMModel == nil {
		repairLLMModel = llm.ModelConfForInnerUtInfer(llm.GetDefaultRepairModel())
	}
	repairLLMModel.ExtraPrams = extraParams

	logger.Debug("== 生成模型：%s 修复模型：%s", utAgent.llmModelForInfer.Model, repairLLMModel.Model)
	utAgent.RepairAct = repair.NewAgent("java", Symbols, repairLLMModel)

	if params.GetCovFilePath() != "" {
		// 解析已有覆盖率文件，获取初始覆盖率
		coverages, err := coverage.ParseCovFile(params.GetCovFilePath())
		if err != nil {
			logger.Warn("解析已有覆盖率文件失败，错误信息为: %s", err)
			logger.Warn("开始执行已有用例来获取初始覆盖率 ~")
		} else {
			logger.Info("初始覆盖率解析成功，原始文件路径: %s", params.GetCovFilePath())
			utAgent.Memory.InitOriginalCoverage(coverages)
			utAgent.isParsedCoverage = true
		}
	}

	return &utAgent, nil
}

func (u *UTAgent) GetParams() models.Params {
	return *u.userParams
}

func (u *UTAgent) AutoDetectAndModifyBuildScript() *models.FrameworkCheck {
	logger.Info("单测工具检查：")
	frameworks := u.Environment.DetectGlobalUnitFramework()

	var modifyFileList []models.FileDetail
	var frameworkCheck = new(models.FrameworkCheck)

	unitFramework := ""
	missingModels := 0
	for path, v := range frameworks {
		logger.InfoT(1, "- %s: %s", path, v)
		if v == "" {
			missingModels++
			if !u.GetParams().IsAutoModifyBuildScript() {
				continue
			}
			logger.WarnT(1, "未检测到 %s 的单元测试框架，开始尝试修复构建脚本...", path)
			adjusted, content, scriptPath := u.Environment.AutoConfigTestEnvironment(path)
			if adjusted {
				logger.InfoT(1, "【更新构建脚本】为文件%s 成功添加junit依赖配置", scriptPath)
				modifyFileList = append(modifyFileList, models.FileDetail{
					FilePath:    scriptPath,
					FileContent: "",
				})
				report := &statistics.ReportGenerationParam{
					CodeRepo:   u.codeRepoInfo,
					Trigger:    u.trigger,
					SrcContent: content,
					UtAbsPath:  scriptPath,
					UtContent:  content,
				}
				err := report.ReportGenerationForFile()

				if err != nil {
					logger.Error("上报生成失败，请联系Comate为您解决问题：", err)
				}
			}
		} else if unitFramework == constant.JUNIT5 {
			// 取高版本junit
			continue
		} else {
			unitFramework = v
		}
	}
	logger.Info("完成自动构建脚本的修复")
	frameworkCheck.Tools = append(frameworkCheck.Tools, models.Tool{
		Name:    "Java",
		Version: u.Environment.JavaVersion,
	})

	if len(modifyFileList) > 0 {
		// 有修复pom文件
		frameworkCheck.State = constant.FIXED
		frameworkCheck.Tools = append(frameworkCheck.Tools, models.Tool{
			Name:    "Junit",
			Version: "",
		})
		frameworkCheck.AdjustedFiles = modifyFileList
	} else if missingModels == len(frameworks) {
		// 所有的模块都缺失junit配置，直接返回失败
		frameworkCheck.State = constant.FAILED
		frameworkCheck.Tools = append(frameworkCheck.Tools, models.Tool{
			Name:    "Junit",
			Version: "",
		})
	} else {
		frameworkCheck.State = constant.SUCCEED
		if unitFramework == "" {
			// 默认情况
			unitFramework = constant.JUNIT5
		}
		frameworkCheck.Tools = append(frameworkCheck.Tools, models.Tool{
			Name:    "Junit",
			Version: unitFramework,
		})
	}

	return frameworkCheck
}

func (u *UTAgent) Call(genQueue chan<- any) {
	u.PrintSrcMethods()
	logger.Info("【开始 => 目标覆盖率%.2f%%】", float64(u.DesiredCoverage))
	// 遍历文件,发起单测的首次生成
	totalFileNum := len(u.FileMethods)

	curFileNum := 0
	for f, methods := range u.FileMethods {
		curFileNum++
		u.Memory.FileProcessInQueue = float64(curFileNum) / float64(totalFileNum)
		junitVersion := u.Environment.GetUnitFramework(f)
		u.Memory.FileTimerStart(f)
		fileContext := u.getFileContext(f)
		if fileContext == nil || fileContext.MainClass == nil {
			logger.DebugT(1, "【文件解析】 %s解析失败，跳过该文件生成\n", f)
			continue
		}

		if fileContext.MainClass.Type == "interface" && len(fileContext.MainClass.Annotation) == 0 {
			logger.WarnT(1, "【跳过生成】文件%s 不能独立测试，跳过该文件生成\n", f)
			continue
		}

		// 构建被测信息
		underTest := models.UnderTestInfo{
			Language:          constant.LangJava,
			PackageName:       fileContext.Pkg,
			ClassName:         fileContext.MainClass.Identifier,
			ClassFullName:     fmt.Sprintf("%s.%s", fileContext.Pkg, fileContext.MainClass.Identifier),
			FilePath:          f,
			RelativeFilePath:  helper.GetRelativePath(f, u.WorkDir),
			TargetFileContext: fileContext,
			TestFramework:     junitVersion,
		}

		process := models.Process{
			TotalNum:   totalFileNum,
			CurrentNum: curFileNum,
		}

		if !u.isParsedCoverage {
			// 未解析初始覆盖率，通过执行已有用例，刷新当前覆盖情况
			testFile := GetTestFile(fileContext.Path, "")
			if testFile != nil {
				if testFile.TestFilePath == f {
					continue
				}
				underTest.TestFilePath = testFile.TestFilePath
				u.RunValidAndUpdateCoverage(&models.CaseInfo{
					TestFilePath:    testFile.TestFilePath,
					TestFileContent: testFile.TestFileContent,
					TestClassName:   testFile.TestClassName,
					UnderTest:       underTest,
				})
			}
		}

		logger.InfoT(1, "【覆盖率检查】%s: %s，当前覆盖率为%.2f%%", process.GetFileProcess(),
			underTest.RelativeFilePath, u.Memory.GetFileOriginCoverage(underTest.ClassFullName).GetCoveragePercent())
		var includeMethodStartLn []int
		for start, _ := range methods {
			includeMethodStartLn = append(includeMethodStartLn, start)
		}
		genFuncNum := u.genForFile(fileContext, underTest, includeMethodStartLn, process, genQueue)
		u.Memory.GetFileProcess(underTest.FilePath).TotalFunc = genFuncNum
		u.Memory.FileTimerPause(f)
	}
}

func (u *UTAgent) genForFile(fileContext *java.FileContext, underTest models.UnderTestInfo, includeMethods []int,
	process models.Process, queue chan<- any) int {
	var testFileContext *java.FileContext
	if underTest.TestFilePath != "" {
		testFileContext, _ = u.Parser.ParseFilePath(underTest.TestFilePath)
	}

	totalFunc := 0
	if fileContext != nil && fileContext.MainClass != nil {
		underTest.TestFileContext = testFileContext
		for _, method := range fileContext.MainClass.Methods {
			if method.IsPrivateOrProtected() {
				// 跳过私有和受保护方法
				continue
			}

			if method.Identifier == fileContext.MainClass.Identifier {
				continue
			}

			if strings.Contains(method.Modifier, "@Test") || strings.Contains(method.Modifier, "@Before") ||
				strings.Contains(method.Modifier, "@After") {
				return 0
			}

			if len(includeMethods) != 0 && !helper.InSlice(includeMethods, method.StartLine) {
				continue
			}

			underTest.TargetMethod = method
			underTest.ID = fmt.Sprintf("%d#%s#%s_%d", time.Now().UnixNano(), underTest.ClassFullName, method.Identifier, method.StartLine)
			underTest.FuncName = method.Identifier
			underTest.FuncID = fmt.Sprintf("%d_%s_%d", u.totalFunctionCount+1, method.Identifier, method.StartLine)
			underTest.Process = &models.Process{
				TotalNum:   process.TotalNum,
				CurrentNum: process.CurrentNum,

				MaxIterNum:   u.MaxIterations,
				MaxRepairNum: u.RepairMaxIters,
				//IterationInfos: new(sync.Map),
				Cache: u.cache,
			}
			if u.CallLLMGen(queue, underTest, "") {
				totalFunc++
				u.totalFunctionCount++
				u.cache.Inc(underTest.FuncID)
			}
		}
	}

	return totalFunc
}

func (u *UTAgent) getFileContext(filePath string) *java.FileContext {
	fileContext, _ := u.Parser.ParseFilePath(filePath)
	if fileContext != nil && fileContext.MainClass != nil {
		u.JavaMemory.FileContexts[strings.Join([]string{fileContext.Pkg, fileContext.MainClass.Identifier}, ".")] = fileContext
	}

	return fileContext
}
func (u *UTAgent) PrepareAgentClient() {
	u.Environment.JavaVersion = environment.PrepareAgentClient()
}

func (u *UTAgent) ValidEnv() (bool, error) {
	if err := u.Environment.CheckEnv(); err != nil {
		return false, err
	}

	return true, nil
}

// GetFileTimer 函数返回给定文件路径对应的计时器对象指针
// 如果不存在则返回nil
// 参数：
//
//	u *UTAgent - 指向UTAgent结构体的指针
//	filePath string - 文件路径
//
// 返回值：
//
//	*helper.Timer - 指向计时器对象的指针
func (u *UTAgent) GetFileTimer(filePath string) *helper.Timer {
	return u.Memory.GetFileTimer(filePath)
}

func (u *UTAgent) RunValid(caseInfo *models.CaseInfo) *models.RunResult {
	// 针对返回的单测进行处理，逐个进行验证，并写回文件
	// 这里先省略文件的处理，假定只有一个用例
	return u.Environment.RunValid(caseInfo, true)
}

// RunValidAndUpdateCoverage 首次运行，刷新当前用例的代码覆盖情况，并做记忆
func (u *UTAgent) RunValidAndUpdateCoverage(caseInfo *models.CaseInfo) {
	runResult := u.Environment.RunValid(caseInfo, false)
	if runResult == nil {
		return
	}
	u.Memory.InitOriginalCoverage(runResult.ClassCoverageInfo)
}

func (u *UTAgent) Repair(repairInfo *models.RepairInfo) (*models.RepairResult, string, error) {
	caseInfo := repairInfo.CaseInfo
	targetCode := java.BuildTargetCode(caseInfo.UnderTest.TargetFileContext.(*java.FileContext),
		[]*java.Method{caseInfo.UnderTest.GetJavaMethod()}, true)

	input := models.RepairInput{
		SrcFileContent:  targetCode,
		TestFileContent: repairInfo.CaseInfo.TestFileContent,
		TestFileName:    fmt.Sprintf("%s.java", repairInfo.CaseInfo.TestClassName),
		TestFilePath:    repairInfo.CaseInfo.TestFilePath,
		TestMethodName:  repairInfo.CaseInfo.TestMethodName,

		RunResult:       repairInfo.RunResult,
		TestFramework:   repairInfo.CaseInfo.UnderTest.TestFramework,
		MethodId:        caseInfo.UnderTest.ID,
		MongoID:         caseInfo.MongoID,
		LanguageVersion: u.GetParams().GetLanguageVersion(),
	}
	if input.TestFramework == "" {
		input.TestFramework = u.Environment.GetUnitFramework(input.TestFilePath)
	}
	return u.RepairAct.Execute(input)
}

func (u *UTAgent) GetMemory() *base.Memory {
	return u.Memory
}

// CallLLMGen 调用LLMGen方法生成测试用例
// 参数：
//
//	queue chan<- any - 用于将生成的测试用例写入队列的通道
//	underTest models.UnderTestInfo - 待生成测试用例的方法信息
//
// 返回值：
//
//	bool - 是否需要继续生成测试用例，返回false说明无需再生成
func (u *UTAgent) CallLLMGen(queue chan<- any, underTest models.UnderTestInfo, testMethodName string) bool {
	targetMethod := underTest.GetJavaMethod()
	coverageInfo := u.GetRangeCoverage(underTest.ClassFullName, targetMethod.StartLine+1, targetMethod.EndLine+1)
	var actionName string

	round := underTest.Process.IncrIteration(underTest.FuncID)

	//if testMethodName != "" {
	//	actionName = basegen.GenWithScenario
	//	u.llmModelForInfer.Model = llm.DefaultRegenModel
	//	logger.DebugT(2, "【用例生成 round#%d】%s: 为方法%s生成场景用例",
	//		round,
	//		underTest.Process.GetFileProcess(),
	//		underTest.ID)
	//} else

	if len(underTest.SucceedTestMethods) != 0 || coverageInfo.GetCoverage() > 0 {
		if len(coverageInfo.MissedLines) == 0 {
			// 方法级别覆盖率已100%
			logger.InfoT(2, "【用例生成 round#%d】%s: %s 100%%覆盖，跳过生成", round,
				underTest.Process.GetFileProcess(),
				underTest.ID)
			return false
		}
		// 以覆盖率为牵引，重新生成
		actionName = basegen.CoverageRegen
		u.llmModelForInfer.Model = llm.GetDefaultRegenModel()
		logger.DebugT(2, "【用例生成 round#%d】%s: 以覆盖率为牵引为方法%s生成用例，剩余 %d 行未被覆盖",
			round,
			underTest.Process.GetFileProcess(),
			underTest.ID,
			len(coverageInfo.MissedLines))
	} else {
		// 从0开始生成
		actionName = basegen.FirstGen
		logger.DebugT(2, "【用例生成 round#%d】%s: 为方法%s生成用例",
			round,
			underTest.Process.GetFileProcess(),
			underTest.ID)
	}

	// 不指定模型，由实例内部默认
	underTest.LLMGenAction = gen.NewUtGenAction(u.llmModelForInfer, u.Parser, &basegen.Input{
		TargetMethod:    underTest.TargetMethod.(*java.Method),
		FileContext:     underTest.TargetFileContext.(*java.FileContext),
		TestFileContext: underTest.TestFileContext.(*java.FileContext),
		CoverageInfo:    coverageInfo,
		Framework:       underTest.TestFramework,
		MethodId:        underTest.ID,
		TestMethodName:  testMethodName,
		TestFilePath:    underTest.TestFilePath,
		LanguageVersion: u.GetParams().GetLanguageVersion(),
	}, actionName)

	go func() {
		queue <- underTest
	}()
	return true
}

func (u *UTAgent) CollectCoverageAndIsUp(newCoverage map[string]*coverage.CoverageInfo, cls string) (bool, *coverage.CoverageInfo) {
	oldCoverage := u.Memory.GetFileCoverage(cls)
	if newCoverage == nil || newCoverage[cls] == nil || len(newCoverage[cls].CoveredLines) == 0 {
		return false, oldCoverage
	}

	if oldCoverage == nil || coverage.IsCoverageUp(newCoverage[cls], oldCoverage) {
		u.Memory.UpdateCoverage(newCoverage)
		return true, u.Memory.GetFileCoverage(cls)
	}

	return false, oldCoverage
}

func (u *UTAgent) GetRunCmd() string {
	return u.Environment.RunCmd
}

func (u *UTAgent) GetFileCoverage(cls string) *coverage.CoverageInfo {
	cov := u.Memory.GetFileCoverage(cls)
	if cov == nil {
		return &coverage.CoverageInfo{}
	}
	return cov
}

func (u *UTAgent) GetFileCoverageByDiff(cls string) *coverage.CoverageInfo {
	if diffs, ok := u.Memory.Diffs[cls]; ok && len(diffs) > 0 {
		cov := u.Memory.GetIncFileCov(cls, diffs, constant.CurrentCoverage)
		if cov != nil {
			return cov
		}
	}
	return &coverage.CoverageInfo{}
}

func (u *UTAgent) GetRangeCoverage(classFullPath string, startLine int, endLine int) *coverage.CoverageInfo {
	return u.Memory.GetRangeCoverage(classFullPath, startLine, endLine)
}

func (u *UTAgent) PrintSrcMethods() {
	logger.Info("【待测文件列表】")
	for f, m := range u.FileMethods {
		if len(m) == 0 {
			logger.InfoT(1, "文件：%s", f)
		} else {
			logger.InfoT(1, "文件：%s，待测方法数：%d", f, len(m))
		}
	}
}

func parseMethods(content string) []*models.Method {
	var err error
	var tree *parserBase.AstTree
	if tree, err = parserBase.NewASTreeWithContent(context.Background(), []byte(content), "java"); err != nil {
		return nil
	}

	rootNode := tree.GetRootNode()
	methodNodes := make([]*sitter.Node, 0)
	parserBase.TraverseType(rootNode, &methodNodes, "method_declaration")
	var result []*models.Method

	for _, node := range methodNodes {
		method := new(models.Method)
		method.StartLine = int(node.StartPoint().Row)
		method.EndLine = int(node.EndPoint().Row)
		method.Body = node.Content([]byte(content))
		if strings.Contains(method.Body, "@Test") {
			method.IsTestCase = true
		}
		var declarators []*sitter.Node
		parserBase.TraverseType(node, &declarators, node.Type())

		for i := 0; i < int(declarators[0].ChildCount()); i++ {
			child := declarators[0].Child(i)
			if child.Type() == "identifier" {
				oldName := child.Content([]byte(content))
				newName := helper.ToCamelCase(oldName)
				if oldName != newName {
					method.Body = strings.Replace(method.Body, oldName, newName, 1)
					method.Identifier = newName
				} else {
					method.Identifier = oldName
				}
				break
			}
		}

		result = append(result, method)
	}

	return result
}

func (u *UTAgent) GetTestMethod(caseInfo *models.CaseInfo) (interface{}, []*models.Method) {
	// 解析新生成的UT，获取用例类名及用例方法列表
	if !strings.HasPrefix(caseInfo.TestFileContent, "package ") {
		caseInfo.TestFileContent = fmt.Sprintf("package %s;\n%s", caseInfo.UnderTest.PackageName, caseInfo.TestFileContent)
	}
	fileParser, err := java.NewFileParserWithContent(caseInfo.TestFileContent)
	if err != nil {
		return nil, nil
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse(&parserBase.CodeFormatOptions{
		MethodNameToCamelCase: true,
	})
	if llmTestFileContext == nil || llmTestFileContext.MainClass == nil {
		methods := parseMethods(caseInfo.TestFileContent)
		if len(methods) == 0 {
			return nil, nil
		}

		return nil, methods
	}

	methods := llmTestFileContext.MainClass.Methods
	var newMethods []*models.Method
	className := llmTestFileContext.MainClass.Identifier
	for _, m := range methods {
		newMethods = append(newMethods, &models.Method{
			IsTestCase: m.IsTestCase, //是否是测试用例
			Body:       m.Body,

			Identifier: m.Identifier, //方法名
			ClassName:  className,
		})
	}
	return llmTestFileContext, newMethods
}

// AppendContent 在文件内容的最后一个 } 前插入新内容，或直接追加到末尾
func AppendContent(fileContent string, newContent string) string {
	// 去除文件内容的空白字符
	trimmedContent := strings.TrimSpace(fileContent)

	// 检查文件内容是否以 } 结尾
	if strings.HasSuffix(trimmedContent, "}") {
		// 找到最后一个 } 的位置
		lastBraceIndex := strings.LastIndex(fileContent, "}")
		// 在最后一个 } 前插入新内容
		result := fileContent[:lastBraceIndex] + "\n" + newContent + "\n" + fileContent[lastBraceIndex:]
		return result
	} else {
		// 如果不以 } 结尾，则直接追加新内容
		return fileContent + "\n" + newContent
	}
}

func (u *UTAgent) MergeLLMResponseToUserTestFile(llmTestFileContext interface{},
	m *models.Method, caseInfo *models.CaseInfo, testFile *models.TestFile) (result string, err error) {

	if llmTestFileContext == nil && m != nil {
		content := helper.ReadFile(caseInfo.TestFilePath)
		testFileContent := AppendContent(content, m.Body)
		// 新生成单测解析时，增加解析条件
		if fileParser, err := javaparser.NewFileParserWithContent(testFileContent); err != nil {
			return "", err
		} else {
			c := fileParser.Parse(&parserBase.CodeFormatOptions{
				MethodNameToCamelCase: true,
			})
			m.ClassName = c.MainClass.Identifier
			llmTestFileContext = c
		}
	}

	if testFile == nil {
		testFile = &models.TestFile{}
	}

	var className string
	var methods []string

	if fileContext, ok := llmTestFileContext.(*javaparser.FileContext); ok && fileContext.MainClass != nil {
		className = fileContext.MainClass.Identifier
		if m != nil {
			methods = []string{m.Identifier}
		} else {
			for _, clsMethod := range fileContext.MainClass.Methods {
				methods = append(methods, clsMethod.Identifier)
			}
		}
	} else {
		return "", fmt.Errorf("llmTestFileContext 类型错误")
	}

	if testFile.TestFilePath == "" {
		testFile.TestFilePath = GetTestFilePathByClsName(caseInfo.UnderTest.FilePath, m.ClassName)
	}

	if testFile.TestFileContent == "" {
		testFile.TestFileContent = helper.ReadFile(testFile.TestFilePath)
	}

	if testFile.TestClassName == "" {
		if caseInfo.TestClassName == "" {
			testFile.TestClassName = className
			caseInfo.TestClassName = className
		} else {
			testFile.TestClassName = caseInfo.TestClassName
		}
	} else {
		caseInfo.TestClassName = testFile.TestClassName
	}

	// 合并指定用例到用户已有用例文件中
	//llmTestFileContext.MainClass.Identifier = testFile.TestClassName
	//llmTestFileContext.MainClass.Methods = llmTestFileContext.MainClass.Methods
	mergeResult := file_processor.Merge(caseInfo.UnderTest.FuncName, caseInfo.UnderTest.PackageName, llmTestFileContext,
		methods, testFile.TestFilePath, testFile.TestFileContent)

	if mergeResult != nil && mergeResult.MergeStatus.Status == types.SUCCESS {
		result = mergeResult.MergeStatus.SubStatus
		switch mergeResult.MergeStatus.SubStatus {
		case types.SubStatusNoChange:
			err = fmt.Errorf("重复用例，跳过验证")
		case types.SubStatusChanged:
			caseInfo.TestFileContent = mergeResult.MergedResult
		case types.SubStatusNewFile:
			// 需要新文件保存，因此代码中的类信息相应需做更新
			newTestClassName := rebuildClassName(filepath.Dir(testFile.TestFilePath), caseInfo.UnderTest.ClassName+"_"+caseInfo.UnderTest.FuncName, 0) + "Test"
			newContent := strings.Replace(mergeResult.MergedResult,
				fmt.Sprintf("class %s", caseInfo.TestClassName),
				fmt.Sprintf("class %s", newTestClassName), 1)
			caseInfo.TestClassName = newTestClassName
			caseInfo.TestFileContent = newContent
			caseInfo.TestFilePath = filepath.Join(filepath.Dir(testFile.TestFilePath), newTestClassName+".java")
			testFile.TestClassName = newTestClassName
			testFile.TestFilePath = caseInfo.TestFilePath
			testFile.TestFileContent = "" // 清空内容, 因为新文件没有成功的用例，新创建的
		default:
			err = fmt.Errorf("未知的合并状态[%s]，请分析", result)
			return
		}
	} else {
		err = fmt.Errorf("合并失败，其他错误，请分析")
	}
	return
}

func (u *UTAgent) saveCase(testFile *models.TestFile, caseInfo models.CaseInfo, oldCoverage float64) {
	// IDE场景下不写入用户文件，写入缓存中进行记录
	u.GetMemory().UpdateTmpValidTestFile(caseInfo.UnderTest.FilePath, testFile)
	if u.trigger == constant.IDETrigger {
		return
	}
	if err := file_processor.Save(testFile.TestFileContent, testFile.TestFilePath); err == nil {
		// 有写入时，记录写入的单测文件路径
		u.GetMemory().SaveTestFilePath(caseInfo.UnderTest.FilePath, testFile.TestFilePath)

		// ICODE场景下，有写入时通知调用方
		//generator.notifyUtResult(caseInfo.UnderTest, testFile.TestFilePath, "")
		logger.InfoTGreen(1, "【Round#%d - 存储】覆盖率提升，写入文件%s, 覆盖率%0.2f%% -> %0.2f%% 📈",
			caseInfo.UnderTest.Process.GetIterNum(caseInfo.UnderTest.FuncID, caseInfo.TestMethodName),
			helper.GetRelativePath(testFile.TestFilePath, u.WorkDir),
			oldCoverage,
			u.GetFileCoverage(caseInfo.UnderTest.ClassFullName).GetCoveragePercent())
	} else {
		logger.ErrorT(1, "【写入文件错误】", err)
	}
}

func (u *UTAgent) DetectSrcBug(caseInfo models.CaseInfo, results *models.RunResult) (bool, string) {
	codeInfo, _ := u.Parser.ParseMethod(caseInfo.UnderTest.TargetFileContext.(*java.FileContext),
		[]*java.Method{caseInfo.UnderTest.TargetMethod.(*java.Method)})

	return bug_detect.IsSrcBug(&models.RepairInput{
		SrcFileContent:  codeInfo.TargetCode,
		TestFileContent: caseInfo.TestFileContent,
		TestFilePath:    fmt.Sprintf("%s.java", caseInfo.TestClassName),
		RunResult:       results,
		TestFramework:   caseInfo.UnderTest.TestFramework,
	})
}

func (u *UTAgent) GetTestFileContentOnlySuccess(result *models.UtFileResult, underTest models.UnderTestInfo) string {
	memoryGenUtCases := u.Memory.GetGeneratedUnitTest(result.TestFilePath)

	var succMethods []string
	for _, c := range memoryGenUtCases {
		if c.Bug != nil {
			continue
		}
		succMethods = append(succMethods, c.CaseName)
	}

	testFileContent := result.TestFileContent
	if testFileContent == "" {
		return DelBugMethodFromContent(result.TestFilePath, succMethods)
	}

	userExistFile := GetTestFile(underTest.FilePath, underTest.FuncName)
	fileParser, err := javaparser.NewFileParserWithContent(result.TestFileContent)
	if err != nil {
		return ""
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse(&parserBase.CodeFormatOptions{
		MethodNameToCamelCase: true,
	})
	if llmTestFileContext == nil || llmTestFileContext.MainClass == nil {
		return ""
	}
	if userExistFile != nil {
		mergeResult := file_processor.Merge(underTest.FuncName, underTest.PackageName, llmTestFileContext,
			succMethods, userExistFile.TestFilePath, userExistFile.TestFileContent)
		return mergeResult.MergedResult
	} else {
		testFilePath := GetTestFilePath(underTest.FilePath)
		mergeResult := file_processor.Merge(underTest.FuncName, underTest.PackageName, llmTestFileContext,
			succMethods, testFilePath, "")
		return mergeResult.MergedResult
	}
}

func (u *UTAgent) GetFullClsNameByFilePath(filePath string) string {
	return GetFullClsNameByFilePath(filePath)
}

func rebuildClassName(dir, clsName string, suffix int) string {
	var name string
	if suffix > 0 {
		name = fmt.Sprintf("%s_%d", clsName, suffix)
	} else {
		name = clsName
	}

	if !helper.IsFileExist(filepath.Join(dir, name+"Test.java")) {
		return name
	} else {
		return rebuildClassName(dir, clsName, suffix+1)
	}
}
