package java

import (
	"testing"

	"icode.baidu.com/baidu/cov/iUT/utagent/java/environment"
)

// TestPrepareAgentClient 是用于测试 PrepareAgentClient
// generated by Comate
func TestPrepareAgentClient(t *testing.T) {
	// 创建一个UTAgent实例
	utAgent := &UTAgent{
		Environment: &environment.Environment{},
	}

	// 调用PrepareAgentClient方法
	utAgent.PrepareAgentClient()

	// 验证Java版本是否被正确设置
	if utAgent.Environment.JavaVersion == "" {
		t.Errorf("Java version was not set correctly")
	}
}
