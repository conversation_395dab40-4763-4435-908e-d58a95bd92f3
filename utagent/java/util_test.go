package java

import (
	"fmt"
	"testing"
)

func TestGetTestFilePathByClsName(t *testing.T) {
	type args struct {
		srcFile     string
		testClsName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "1",
			args: args{
				srcFile:     "src/main/java/com/example/MyClass.java",
				testClsName: "MyClassTest",
			},
			want: "src/test/java/com/example/MyClassTest.java",
		},
		{
			name: "3",
			args: args{
				srcFile:     "src/main/java/com/example/MyClass.java",
				testClsName: "",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetTestFilePathByClsName(tt.args.srcFile, tt.args.testClsName); got != tt.want {
				t.<PERSON>rrorf("GetTestFilePathByClsName() = %v, want %v", got, tt.want)
			}
		})
	}
}

type M struct {
	Num int
}

func TestA(t *testing.T) {
	m := &M{
		Num: 1,
	}
	m3 := *m
	m3.Num = 3
	m2 := *m
	m2.Num = 2
	//newM := &m2
	//newM.Num = 2
	fmt.Println(m.Num, m2.Num, m3.Num)
}

func TestGetFullClsNameByFilePath(t *testing.T) {
	type args struct {
		filePath string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test-1",
			args: args{
				filePath: "src/test/java/com/example/MyClassTest.java",
			},
			want: "com.example.MyClassTest",
		},
		{
			name: "test-2",
			args: args{
				filePath: "src/main/java/com/example/MyClass.java",
			},
			want: "com.example.MyClass",
		},
		{
			name: "test-4",
			args: args{
				filePath: "",
			},
			want: "",
		},

		{
			name: "test-5",
			args: args{
				filePath: "src/test/java/com/example/MyClass.java",
			},
			want: "com.example.MyClass",
		},
		{
			name: "test-6",
			args: args{
				filePath: "src/main/java/com/example/MyClassTest.java",
			},
			want: "com.example.MyClassTest",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetFullClsNameByFilePath(tt.args.filePath); got != tt.want {
				t.Errorf("GetFullClsNameByFilePath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetTestFilePathByClsName_1(t *testing.T) {
	type args struct {
		srcFile     string
		testClsName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "1",
			args: args{
				srcFile:     "src/main/java/com/example/MyClass.java",
				testClsName: "MyClassTest",
			},
			want: "src/test/java/com/example/MyClassTest.java",
		},
		{
			name: "2",
			args: args{
				srcFile:     "src/main/java/com/example/MyClass.java",
				testClsName: "",
			},
			want: "",
		},
		{
			name: "3",
			args: args{
				srcFile:     "",
				testClsName: "MyClassTest",
			},
			want: "",
		},
		{
			name: "4",
			args: args{
				srcFile:     "src/main/java/com/example/MyClass.java",
				testClsName: "MyClassTest",
			},
			want: "src/test/java/com/example/MyClassTest.java",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetTestFilePathByClsName(tt.args.srcFile, tt.args.testClsName); got != tt.want {
				t.Errorf("GetTestFilePathByClsName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDelBugMethodFromContent(t *testing.T) {
	type args struct {
		testFilePath string
		succMethods  []string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			"test1", args{
				testFilePath: "/Users/<USER>/workspace/comate-ut/evaluate/java-evaluate-data/xxl-job/xxl-job-core/src/test/java/com/xxl/job/core/util/FileUtilTest.java",
				succMethods:  []string{"testDeleteRecursivelyWithEmptyDirectory", "testDeleteRecursivelyWithNestedFiles"},
			},
			//want: "src/test/java/com/example/MyClassTest.java",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			content := DelBugMethodFromContent(tt.args.testFilePath, tt.args.succMethods)
			t.Log(content)
		})
	}
}
