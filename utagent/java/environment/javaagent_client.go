package environment

import (
	"encoding/json"
	"fmt"
	"github.com/toolkits/file"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
)

const (
	// https://github.com/jacoco/jacoco/releases 版本支持说明
	JacocoAgent      = "https://baidu-ide.bj.bcebos.com/comate/bin/UTAgent/jacocoagent.jar"
	JacocoAgent_8_11 = "https://baidu-ide.bj.bcebos.com/comate/bin/UTAgent/jacoco/8.11/jacocoagent.jar"

	JacocoCli         = "https://baidu-ide.bj.bcebos.com/comate/bin/UTAgent/jacococli.jar"
	JacocoCli_8_11    = "https://baidu-ide.bj.bcebos.com/comate/bin/UTAgent/jacoco/8.11/jacococli.jar"
	CovJavaClientName = "javaagent-client-latest"
	RunUTCmd          = `java -jar %s --jacocoAgentPath "%s" --jacocoExecOutput "%s" --projectRoot "%s" --srcFullPath "%s" --utFullPath "%s" --utClassName %s --package "%s" --shareFile "%s"`

	// JacocoCovCmd 注意：%s外面不可以加双引号，windows会报找不到路径
	JacocoCovCmd = `java -jar %s report %s --classfiles %s --sourcefiles %s --xml %s`

	// JunitVersionCmd 注意：jar包的路径外面不可以加双引号，会报找不到jar包路径
	JunitVersionCmd   = `java -jar %s --op checkJunitVersion --projectRoot "%s" --shareFile "%s"`
	CovCollectTimeout = 30
	UTRunTimeout      = 2 * 60
)

type JavaAgentRunnerClient struct {
	ProjectRoot  string // 项目根目录
	SrcFilePath  string // 源文件路径
	SrcClassName string // 源类名

	UTFullFilePath string // 测试文件的完整路径
	PackageName    string // 包名
	UTClassName    string // 测试类名
	UTMethodName   string // 测试方法名
	JUnitVersion   string // JUnit版本
	LocalRepoPath  string // 本地仓库路径
}

var jacocoAgent = ""
var jacocoCli = ""
var runAgent = ""

var srcDirClasss = make([]string, 0)
var mu sync.Mutex

var bos = "baidu-ide"

func getToolPath() string {
	if os.Getenv("UTAGENT_HOME") != "" {
		return os.Getenv("UTAGENT_HOME")
	}
	return filepath.Join(helper.GetUserHome(), ".UTAgent")
}
func getJavaVersion() string {
	shellCmdInfo := tools.GetShellStatusOutputErrorTimeOut([]string{"java", "-version"}, "./", false, 10)
	if shellCmdInfo.CmdState == 0 {
		output := shellCmdInfo.Output
		if len(output) == 0 {
			output = shellCmdInfo.ErrorInfo
		}
		if len(output) > 0 {
			return abstractJavaVersion(output)
		}

	}
	return ""

}
func abstractJavaVersion(output string) string {
	re := regexp.MustCompile(`version\s+"(.+).*?"`)
	for _, line := range strings.Split(output, "\n") {
		line = strings.TrimSpace(line)
		if len(line) > 0 {
			match := re.FindStringSubmatch(line)
			if len(match) > 1 {
				version := match[1]
				if strings.Contains(version, "_") {
					return strings.Split(version, "_")[0]
				}
				return version
			}
		}
	}
	return ""
}
func PrepareAgentClient() string {
	// 创建工具目录，默认在用户目录的.UTAgent目录下
	toolPath := getToolPath()
	if !utils.IsFileExisted(toolPath) {
		if err := os.MkdirAll(toolPath, 0755); err != nil {
			logger.ErrorT(1, "create tool path failed: %s", err)
			os.Exit(1)
		}
	}
	version := getJavaVersion()
	logger.InfoT(1, fmt.Sprintf("java version %d", version))
	jacocoAgentURL := JacocoAgent
	jacocoCliURL := JacocoCli

	versionInt, _ := strconv.Atoi(strings.Split(version, ".")[0])
	if versionInt >= 21 {
		jacocoAgentURL = JacocoAgent_8_11
		jacocoCliURL = JacocoCli_8_11
	}
	//logger.InfoT(1, fmt.Sprintf("jacocoAgentURL %s", jacocoAgentURL))
	// 下载jacocoagent.jar
	jacocoAgent = filepath.Join(toolPath, "jacocoagent.jar")
	if !helper.IsFileExist(jacocoAgent) {
		err := helper.DownloadFile(jacocoAgentURL, toolPath, "jacocoagent.jar")
		if err != nil {
			logger.ErrorT(1, "down jacocoagent.jar error")
			os.Exit(1)

		}
	}

	// 下载jacoco-cli.jar
	jacocoCli = filepath.Join(toolPath, "jacococli.jar")
	if !helper.IsFileExist(jacocoCli) {
		err := helper.DownloadFile(jacocoCliURL, toolPath, "jacococli.jar")
		if err != nil {
			logger.Info("down jacococli.jar error")
			os.Exit(1)
		}
	}

	// 下载runAgent.jar
	runAgent = checkAndUpdate(CovJavaClientName, toolPath)

	return version

}

type JunitVersions struct {
	JunitVersionMap map[string]string `json:"junitVersionMap"`
}

func (runner *JavaAgentRunnerClient) JudgeJunitVersion() map[string]string {
	workDir := filepath.Join(os.TempDir(), helper.StringMd5(runner.ProjectRoot))
	// java与go程序通信，共享文件
	if !helper.IsFileExist(workDir) {
		if err := os.MkdirAll(workDir, 0755); err != nil {
			logger.Error("create workdir %s failed: %s", workDir, err)
		}
	}
	//defer os.RemoveAll(workDir)
	shareFile := filepath.Join(workDir, "JunitVersionShareFile.json")
	defer os.Remove(shareFile)

	// 拼接命令
	params := []string{runAgent, runner.ProjectRoot, shareFile}
	cmdList, _ := tools.AssembleCmdList(JunitVersionCmd, params)
	cmd := strings.Join(cmdList, " ")
	logger.DebugT(1, " 查找单测框架指令：%s", cmd)
	shellCmdInfo := tools.GetShellStatusOutputErrorTimeOut(cmdList, runner.ProjectRoot, false, UTRunTimeout)
	if shellCmdInfo.CmdState != 0 {
		shellCmdInfo.Print(cmd)
	}

	if helper.IsFileExist(shareFile) {
		jsonStr := helper.ReadFile(shareFile)
		junitVersion := new(JunitVersions)
		err := json.Unmarshal([]byte(jsonStr), junitVersion)
		if err == nil {
			return junitVersion.JunitVersionMap
		}
	}
	return nil
}
func (runner *JavaAgentRunnerClient) RunTestWithJacocoAgent(runCheck bool) *models.RunResult {
	tag := fmt.Sprintf("%s.%s_%s", runner.PackageName, runner.UTClassName, runner.UTMethodName)
	workDir := filepath.Join(os.TempDir(), tag)
	defer func() {
		if helper.IsFileExist(workDir) {
			os.RemoveAll(workDir)
		}
	}()
	shareFile := filepath.Join(workDir, "shareFile.json")
	jacocoExec := filepath.Join(workDir, "jacoco.exec")
	os.Remove(jacocoExec)
	os.Remove(shareFile)
	cmd := RunUTCmd
	params := []string{runAgent, jacocoAgent, jacocoExec, runner.ProjectRoot,
		runner.SrcFilePath, runner.UTFullFilePath, runner.UTClassName,
		runner.PackageName, shareFile}
	//cmd := fmt.Sprintf(RunUTCmd, runAgent, jacocoAgent, jacocoExec, runner.ProjectRoot,
	//	runner.SrcFilePath, runner.UTFullFilePath, runner.UTClassName,
	//	runner.PackageName, shareFile)

	if runner.JUnitVersion != "" {
		cmd += ` --junitVersion %s`
		params = append(params, runner.JUnitVersion)
	}
	if runner.UTMethodName != "" {
		cmd += ` --methodName %s`
		params = append(params, runner.UTMethodName)
	}
	if runner.LocalRepoPath != "" {
		cmd += ` --localRepoPath "%s"`
		params = append(params, fmt.Sprintf(`"%s"`, runner.LocalRepoPath))
	}

	cmdList, _ := tools.AssembleCmdList(cmd, params)
	ps := []any{}
	for _, p := range params {
		ps = append(ps, p)
	}
	logger.DebugT(1, " 验证指令：%s", fmt.Sprintf(cmd, ps...))

	shellCmdInfo := tools.GetShellStatusOutputErrorTimeOut(cmdList, os.TempDir(), false, UTRunTimeout)
	if shellCmdInfo.CmdState != 0 {
		logger.WarnT(2, "run test with jacoco agent failed: %s", cmd)
		//shellCmdInfo.Print()
	}

	var javaAgentRunResult *models.RunResult
	if helper.IsFileExist(shareFile) {
		var err error
		var jsonStr string
		err, jsonStr, javaAgentRunResult = readShareFile(shareFile)
		if err == nil && (!runCheck || javaAgentRunResult.IsCompileSucceeded) { // runCheck导致用例运行有错时还会继续执行
			if !file.IsExist(jacocoExec) {
				logger.ErrorT(1, "【jacocoExec文件不存在】: %s, runCheck：%b, IsCompileSucceeded:%b", jacocoExec, runCheck, javaAgentRunResult.IsCompileSucceeded)
				shellCmdInfo.Print(jsonStr)
			}
			javaAgentRunResult.ClassCoverageInfo = collectCoverage(runner.ProjectRoot, workDir, jacocoExec, runner.SrcFilePath, runner.PackageName)
			_, _, newJavaAgentRunResult := readShareFile(shareFile)
			logger.InfoT(1, "【shareFile content】compileState:%n, runState:%b", newJavaAgentRunResult.IsCompileSucceeded, newJavaAgentRunResult.IsRunWithoutError)
		} else {
			shellCmdInfo.Print(jsonStr)
		}
	}
	return javaAgentRunResult
}

func readShareFile(shareFile string) (error, string, *models.RunResult) {
	jsonStr := helper.ReadFile(shareFile)
	javaAgentRunResult := &models.RunResult{}
	err := json.Unmarshal([]byte(jsonStr), javaAgentRunResult)
	return err, jsonStr, javaAgentRunResult
}

func srcBuildClassDir(projectRoot string, srcFullPath, packageName string) string {

	index := strings.Index(srcFullPath, filepath.Join("src", "main", "java", packageName))
	if index == -1 {
		return ""
	}
	// 这个maven 默认路径
	classfiles := filepath.Join(srcFullPath[:index], "target", "classes")
	if !helper.IsFileExist(classfiles) { // 这个是gradle默认路径
		classfiles = filepath.Join(srcFullPath[:index], "build", "classes")
		if !helper.IsFileExist(classfiles) {
			classfiles = filepath.Join(srcFullPath[:index], "output", "classes")
		}
	}
	if helper.IsFileExist(classfiles) {
		return classfiles
	}
	//  都不存在，通过查找机制寻找
	if len(srcDirClasss) == 0 {
		srcDirClasss = helper.FindDirBySuffix(projectRoot, string(filepath.Separator)+filepath.Join("classes", "java", "main"))
	}
	class := helper.FindLongestCommonPrefix(srcFullPath, srcDirClasss)
	return class
}

func collectCoverage(projectRoot, workDir, jacocoExecFile string, srcFullPath, packageName string) map[string]*coverage.CoverageInfo {
	jacocoXML := strings.TrimSuffix(jacocoExecFile, ".exec") + ".xml"
	os.Remove(jacocoXML)
	packageName = strings.Replace(packageName, ".", string(filepath.Separator), -1)
	index := strings.Index(srcFullPath, packageName)
	sourcefiles := srcFullPath[:index]
	index = strings.Index(srcFullPath, filepath.Join("src", "main", "java", packageName))
	classfiles := srcBuildClassDir(projectRoot, srcFullPath, packageName)

	cmdList, _ := tools.AssembleCmdList(JacocoCovCmd, []string{jacocoCli, jacocoExecFile, classfiles, sourcefiles, jacocoXML})
	shellCmdInfo := tools.GetShellStatusOutputErrorTimeOut(cmdList, workDir, true, CovCollectTimeout)
	if shellCmdInfo.CmdState == 0 {
		if coverageInfo, err := coverage.ParseCovFile(jacocoXML); err != nil {
			logger.ErrorT(1, "parse jacoco xml failed: %s", err)
		} else {
			return coverageInfo
		}
	} else {
		logger.ErrorT(1, "collect coverage failed: output: %s, error: %s", shellCmdInfo.Output,
			shellCmdInfo.ErrorInfo)
	}
	return nil
}
