package environment

import (
	"encoding/xml"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"os"
	"strings"
)

type Project struct {
	Dependencies Dependencies `xml:"dependencies"`
}

type Dependencies struct {
	DependencyList []Dependency `xml:"dependency"`
}

type Dependency struct {
	GroupId    string `xml:"groupId"`
	ArtifactId string `xml:"artifactId"`
	Version    string `xml:"version"`
}

// parseDependenciesByPom 从指定的pom.xml文件中解析依赖项列表
//
// 参数:
// pomFile string: pom.xml文件的路径
//
// 返回值:
// []Dependency: 解析得到的依赖项列表
func parseDependenciesByPom(pomFile string) []Dependency {
	data, err := os.ReadFile(pomFile)
	if err != nil {
		logger.Warn("error opening file: %v", err)
	}

	// 解析XML数据
	var project Project
	err = xml.Unmarshal(data, &project)
	if err != nil {
		logger.Warn("error parsing file: %v", err)
		return []Dependency{}
	}

	return project.Dependencies.DependencyList
}

// GetJunitVersionFromPom 从给定的pom.xml文件中获取JUnit的版本
//
// 参数:
// pomFile string: 指定pom.xml文件的路径
//
// 返回值:
// string: 返回JUnit的版本字符串，如果pom.xml文件中包含junit 4.x版本，则返回"4"；
//
//	如果包含org.junit.jupiter（JUnit 5），则返回"5"；
//	如果两者都不存在，则返回一个空字符串。
func GetJunitVersionFromPom(pomFile string) string {
	dependencies := parseDependenciesByPom(pomFile)
	junitVersion := ""
	for _, dependency := range dependencies {
		if dependency.GroupId == "junit" && dependency.ArtifactId == "junit" && strings.HasPrefix(dependency.Version, "4") {
			junitVersion = constant.JUNIT4
			break
		}
	}
	// 处理同时存在junit4和junit5的情况
	for _, dependency := range dependencies {
		if dependency.GroupId == "org.junit.jupiter" {
			junitVersion = constant.JUNIT5
			break
		}
	}
	return junitVersion
}

// GetJunitVersionFromGradle 从指定的Gradle文件中获取JUnit的版本
//
// 参数:
// gradleFile string: Gradle文件的路径
//
// 返回值:
// string: 返回JUnit的版本字符串。如果文件中包含JUnit 4的依赖或注解，则返回"4"；
//
//	如果包含JUnit 5的依赖，则返回"5"；如果两者都不存在，则返回一个空字符串。
func GetJunitVersionFromGradle(gradleFile string) string {
	content, _ := os.ReadFile(gradleFile)
	if strings.Contains(string(content), "junit:junit:4") ||
		strings.Contains(string(content), "org.junit.Test") {
		return constant.JUNIT4
	} else if strings.Contains(string(content), "org.junit.jupiter") {
		return constant.JUNIT5
	}
	return ""
}
