package environment

import (
	"testing"
)

func TestGetJunitVersionFromPom(t *testing.T) {

	testCases := []struct {
		name     string
		pomFile  string
		expected string
	}{
		{
			name:     "Junit4",
			pomFile:  "../../../testdata/pom.xml",
			expected: "junit5",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := GetJunitVersionFromPom(tc.pomFile)
			if result != tc.expected {
				t.<PERSON><PERSON><PERSON>("Expected %s, got %s", tc.expected, result)
			}
		})
	}
}
