package environment

import (
	"fmt"
	"regexp"
	"strings"
	"testing"
)

func TestJavaAgentRunnerClient_JudgeJunitVersion(t *testing.T) {
	type fields struct {
		ProjectRoot string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "test1",
			fields: fields{
				ProjectRoot: "/Users/<USER>/git/baidu/cov/runUTAgent",
			},
			want: "junit5",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			runner := &JavaAgentRunnerClient{
				ProjectRoot: tt.fields.ProjectRoot,
			}

			runAgent = "/Users/<USER>/.UTAgent/javaagent-client-latest.jar"
			got := runner.JudgeJunitVersion()
			fmt.Println(got)
		})
	}
}

func Test_getJavaVersion(t *testing.T) {
	version := "21.0.5"
	strings.HasPrefix(version, "21")
	output := "                                                                                                                           \n    openjdk version \"21\" 2023-09-19                                                                                       \n    OpenJDK Runtime Environment (build 21+35-2513)                                                                        \n    OpenJDK 64-Bit Server VM (build 21+35-2513, mixed mode, sharing)   "
	re := regexp.MustCompile(`version\s+"(\d+).*?"`)
	for _, line := range strings.Split(output, "\n") {
		line = strings.TrimSpace(line)
		if len(line) > 0 {
			match := re.FindStringSubmatch(line)
			if len(match) > 1 {
				versions := strings.Split(match[0], "version")
				if len(versions) == 2 {
					fmt.Println("openjdk version " + strings.TrimSpace(versions[1]))
				}
			}
			break
		}
	}
	tests := []struct {
		name string
		want string
	}{
		{
			name: "test1",
			want: "openjdk version \"1.8.0_202\"",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getJavaVersion(); got == "" {
				t.Errorf("getJavaVersion() = %v, want %v", got, tt.want)
			} else {
				fmt.Println(got)
			}
		})
	}
}

func Test_abstractJavaVersion(t *testing.T) {
	type args struct {
		output string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{
				output: "  \n    openjdk version \"21\" 2023-09-19                                                                                       \n    OpenJDK Runtime Environment (build 21+35-2513)                                                                        \n    OpenJDK 64-Bit Server VM (build 21+35-2513, mixed mode, sharing)   ",
			},
			want: "21",
		},
		{
			name: "test2",
			args: args{
				output: "    \n    openjdk version \"1a.8.0_202\" 2023-09-19                                                                                       \n    OpenJDK Runtime Environment (build 21+35-2513)                                                                        \n    OpenJDK 64-Bit Server VM (build 21+35-2513, mixed mode, sharing)   ",
			},
			want: "1.8.0_202",
		},
		{
			name: "test3",
			args: args{
				output: "java version \"11.0.18\" 2023-01-17 LTS\nJava(TM) SE Runtime Environment 18.9 (build 11.0.18+9-LTS-195)\nJava HotSpot(TM) 64-Bit Server VM 18.9 (build 11.0.18+9-LTS-195, mixed mode)",
			},
			want: "1.8.0_202",
		},
		{
			name: "test4",
			args: args{
				output: "                                                                                                                           \n    openjdk version \"1.8.0_202\" 2023-09-19                                                                                       \n    OpenJDK Runtime Environment (build 21+35-2513)                                                                        \n    OpenJDK 64-Bit Server VM (build 21+35-2513, mixed mode, sharing)   ",
			},
			want: "1.8.0_202",
		},
		{
			name: "test5",
			args: args{
				output: "java version \"21.0.5\" 2024-10-15 LTS\nJava(TM) SE Runtime Environment (build 21.0.5+9-LTS-239)\nJava HotSpot(TM) 64-Bit Server VM (build 21.0.5+9-LTS-239, mixed mode, sharing)\n",
			},
			want: "21.0.5",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := abstractJavaVersion(tt.args.output); got == "" {
				t.Errorf("abstractJavaVersion() = %v, want %v", got, tt.want)
			} else {
				fmt.Println(got)
			}
		})
	}
}
