package environment

import (
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"os"
	"strconv"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type JavaAgentRunner struct {
	ProjectRoot  string // 项目根目录
	SrcFilePath  string // 源文件路径
	SrcClassName string // 源类名

	UTFullFilePath string // 测试文件的完整路径
	PackageName    string // 包名
	UTClassName    string // 测试类名
	UTMethodName   string // 测试方法名
	JUnitVersion   string // JUnit版本
}

var port int

const CovJavaJar = "javaagent-1.1-SNAPSHOT"

var isServerOk = false

func prepareAgentServer() bool {
	toolPath := getToolPath()
	covJavaAgent := checkAndUpdate(CovJavaJar, toolPath)
	var err error
	port, err = helper.FindAvailablePort()
	if err != nil {
		logger.ErrorT(1, "find available port failed")
		os.Exit(1)
	}

	cmdList, _ := tools.AssembleCmdList(`java -jar "%s" --server.port=%d > /dev/null 2>&1 &`, []string{covJavaAgent, strconv.Itoa(port)})
	shellCmdInfo := tools.GetShellStatusOutputErrorTimeOut(cmdList, toolPath, true, CovCollectTimeout)
	shellCmdInfo.Print("")
	return healthCheck()
}

func healthCheck() bool {
	_, err := httputil.Get(fmt.Sprintf("http://127.0.0.1:%d", port))
	if err != nil {
		return healthCheck()
	}
	return true
}

// RunTestWithJacocoAgent 运行带有Jacoco代理的Java单元测试
//
// jacocoExecFile: Jacoco执行文件路径
//
// 返回值:
// *JavaAgentRunResult: JavaAgent运行结果的指针
func (runner *JavaAgentRunner) RunTestWithJacocoAgent() *models.RunResult {
	if !isServerOk {
		prepareAgentServer()
		isServerOk = true
	}
	requestURL := fmt.Sprintf("http://127.0.0.1:%d/runTest", port)
	bodyJSON := map[string]string{
		"projectRoot":  runner.ProjectRoot,
		"utFullPath":   runner.UTFullFilePath,
		"srcFullPath":  runner.SrcFilePath,
		"utClassName":  runner.UTClassName,
		"srcClassName": runner.SrcClassName,
		"methodName":   runner.UTMethodName,
		"packageName":  runner.PackageName,
		"junitVersion": runner.JUnitVersion,
	}
	res, err := httputil.HTTPPostBody(requestURL, bodyJSON,
		httputil.WithTimeout(600*time.Second),
		httputil.WithHeader("Content-Type", "application/json"))
	if err != nil {
		logger.DebugT(1, "http post body error")
	}

	var javaAgentRunResult *models.RunResult

	data, err := json.Marshal(res)
	if err != nil {
		logger.ErrorT(1, "json marshal error")
	}
	err = json.Unmarshal(data, &javaAgentRunResult)
	if err != nil {
		logger.ErrorT(1, "json unmarshal error")
	}

	return javaAgentRunResult
}
