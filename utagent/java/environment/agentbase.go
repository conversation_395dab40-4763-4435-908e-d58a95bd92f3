package environment

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
)

var downLoadAgentMd5 = "https://%s.bj.bcebos.com/comate/bin/UTAgent/%s.md5"
var downLoadAgentZip = "https://%s.bj.bcebos.com/comate/bin/UTAgent/%s.zip"

func checkAndUpdate(jarFormatName string, toolPath string) string {
	if config.IsDebugEnv() {
		bos = "baidu-coverage"
	}

	// 本地当前的版本信息
	localMD5FileName := fmt.Sprintf("%s.md5", jarFormatName)
	localMD5Path := filepath.Join(toolPath, localMD5FileName)

	// 如果本地不存在版本文件，则直接下载
	if !helper.IsFileExist(localMD5Path) {
		return downloadAgent(jarFormatName, toolPath)
	}

	// 下载远端最新版本信息
	remoteMD5FileName := fmt.Sprintf("%sremote.md5", jarFormatName)
	remoteMD5Path := filepath.Join(toolPath, remoteMD5FileName)
	os.Remove(remoteMD5Path)
	if err := helper.DownloadFile(fmt.Sprintf(downLoadAgentMd5, bos, jarFormatName),
		toolPath, remoteMD5FileName); err != nil {
		logger.ErrorT(1, "Failed to download remote MD5 file: %s, error: %v", remoteMD5FileName, err)
		return ""
	}
	defer os.Remove(remoteMD5Path)

	localMD5Version := strings.TrimSpace(helper.ReadFile(localMD5Path))
	remoteMD5Version := strings.TrimSpace(helper.ReadFile(remoteMD5Path))
	if localMD5Version != remoteMD5Version {
		return downloadAgent(jarFormatName, toolPath)
	}

	return filepath.Join(toolPath, fmt.Sprintf("%s.jar", jarFormatName))
}

func downloadAgent(jarFormatName string, toolPath string) string {
	// 远端程序包为zip文件
	javaAgentZipName := fmt.Sprintf("%s.zip", jarFormatName)
	javaAgentZipPath := filepath.Join(toolPath, javaAgentZipName)
	javaAgentJarPath := filepath.Join(toolPath, fmt.Sprintf("%s.jar", jarFormatName))

	// 删除本地旧文件（如果存在）
	if helper.IsFileExist(javaAgentZipPath) {
		if err := os.Remove(javaAgentZipPath); err != nil {
			logger.ErrorT(1, "Failed to remove old ZIP file: %v", err)
		}
	}

	// 下载并解压远程的ZIP文件
	if err := helper.DownloadFile(fmt.Sprintf(downLoadAgentZip, bos, jarFormatName), toolPath, javaAgentZipName); err != nil {
		logger.ErrorT(1, "Failed to download ZIP file: %v", err)
		return ""
	}
	defer os.Remove(javaAgentZipPath)

	if err := helper.Unzip(javaAgentZipPath, toolPath); err != nil {
		logger.ErrorT(1, "Failed to unzip ZIP file: %v", err)
		return ""
	}

	// 确认解压后的JAR文件存在
	if helper.IsFileExist(javaAgentJarPath) {
		return javaAgentJarPath
	}

	// 解压失败则尝试重新下载
	logger.ErrorT(1, "JAR file not found after unzipping. Retrying...")
	time.Sleep(10 * time.Second)
	return checkAndUpdate(jarFormatName, toolPath)
}
