package environment

import (
	"errors"
	"fmt"
	"github.com/toolkits/file"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/javarepair"
	"icode.baidu.com/baidu/cov/smartUT-parser/base"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	javaparser "icode.baidu.com/baidu/cov/smartUT-parser/java"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
)

const (
	DefaultMvnRunCmd    = `mvn clean install -DskipTests -Dmaven.test.skip=true -DfailIfNoTests=false -Dmaven.javadoc.skip=true`
	DefaultGradleRunCmd = "%s compileJava"
	Junit5Version       = "junit5"
	Junit4Version       = "junit4"
)

type Environment struct {
	LocalRepoPath  string
	UnitFrameworks map[string]string
	UnitFramework  string
	MockFramework  []string
	JavaVersion    string

	BuildTool    string
	WorkDir      string
	RunCmd       string
	OutputParser func(string) (*models.CaseInfo, error)
}

type JunitVersion struct {
	Junit5 bool `json:"junit5Flag"`
	Junit4 bool `json:"junit4Flag"`
}

func NewEnvironment(workDir string, runCmd string, unitFramework string) *Environment {
	env := &Environment{WorkDir: workDir, RunCmd: runCmd, UnitFramework: unitFramework}

	if runCmd == "" {
		if utils.IsFileExisted(filepath.Join(workDir, "pom.xml")) {
			env.RunCmd = DefaultMvnRunCmd
			env.BuildTool = "mvn"
		} else if utils.IsFileExisted(filepath.Join(workDir, "build.gradle")) ||
			utils.IsFileExisted(filepath.Join(workDir, "settings.gradle")) ||
			utils.IsFileExisted(filepath.Join(workDir, "build.gradle.kts")) ||
			utils.IsFileExisted(filepath.Join(workDir, "settings.gradle.kts")) {
			env.BuildTool = "gradle"
			if utils.IsFileExisted(filepath.Join(workDir, "gradlew")) {
				env.BuildTool = "./gradlew"
			}
			env.RunCmd = fmt.Sprintf(DefaultGradleRunCmd, env.BuildTool)
		}
	}

	return env
}

// AutoConfigTestEnvironment 补齐环境配置，可借助模型
func (e *Environment) AutoConfigTestEnvironment(scriptPath string) (bool, string, string) {
	if scriptPath == "" || !file.IsExist(scriptPath) {
		return false, "", ""
	} else if !file.IsFile(scriptPath) {
		sPath := filepath.Join(scriptPath, "build.gradle")
		if file.IsExist(sPath) {
			scriptPath = sPath
		} else {
			sPath = filepath.Join(scriptPath, "build.gradle.kts")
			if file.IsExist(sPath) {
				scriptPath = sPath
			}
		}
	}

	if strings.HasSuffix(scriptPath, "pom.xml") {
		if strings.Contains(helper.ReadFile(scriptPath), "<packaging>pom</packaging>") {
			logger.Warn("当前pom配置为聚合项目，无需修复")
			return false, "", ""
		}
	}

	builder := javarepair.NewLLMRepairBuildScript(
		"",
		"",
		scriptPath,
	)

	defer func() {
		if err := recover(); err != nil {
			file.WriteString(scriptPath, builder.ScriptContent)
		}
	}()

	result, err := builder.Call()
	if err != nil {
		return false, "", ""
	}

	result = helper.GetMarkdownBlockFromContent(result)
	file.WriteString(scriptPath, result)
	cmdList, _ := tools.AssembleCmdList(e.RunCmd, nil)
	status, output := tools.GetShellStatusOutput(cmdList, e.WorkDir, false)
	if status != 0 {
		file.WriteString(scriptPath, builder.ScriptContent)
		logger.DebugT(1, "自动修复脚本失败，请手动修复。 修复后报错信息如下:", output)
		return false, "", ""
	}

	e.UnitFrameworks[scriptPath] = "junit5"

	return true, result, scriptPath
}

func (e *Environment) CheckBuildTool() bool {
	if e.BuildTool == "./gradlew" {
		return true
	}
	exitCode, _ := tools.GetShellStatusOutput([]string{e.BuildTool, "-version"}, e.WorkDir, false)
	return exitCode == 0
}

// CheckEnv 有3个功能点：
// 1. 代码是否有错，正常的编译是否能通过
// 2. 是否存在JUnit配置，无则需要自动完成maven或gradle的配置，有需识别出Junit的版本
// 3. 是否存在mock配置（Mockito),无则需要自动完成maven和gradle的配置
func (e *Environment) CheckEnv() error {
	logger.Info("【准备 => 验证环境~_~】：要保证环境可正确编译, 以支持用例的执行验证")

	logger.Info("【=> 检测构建工具】")
	if e.BuildTool == "" {
		logger.ErrorT(1, "%-10s", "【构建工具异常】❌")
		return fmt.Errorf("环境校验失败：请确保项目为合法的maven或gradle项目")
	}
	if !e.CheckBuildTool() {
		logger.ErrorT(1, "%-10s", "【构建工具异常】❌")
		return fmt.Errorf("环境校验失败：请确保系统中存在构建工具【%s】，可通过在终端运行【%s -version】进行检查 ",
			e.BuildTool, e.BuildTool)
	}

	logger.Info("【构建工具】%s ✅", e.BuildTool)
	cmdList, _ := tools.AssembleCmdList(e.RunCmd, nil)
	status, output := tools.GetShellStatusOutput(cmdList, e.WorkDir, false)
	if status != 0 {
		logger.ErrorT(1, "%-10s", "【环境异常】❌")
		logger.ErrorT(1, "%-10s", "验证指令："+e.RunCmd)
		logger.ErrorT(1, "%-10s", "错误信息："+strings.TrimSpace(output))
		_, out := tools.GetShellStatusOutput([]string{"java", "-version"}, e.WorkDir, false)
		logger.InfoT(1, "java -version: \n%s", out)
		_, out = tools.GetShellStatusOutput([]string{e.BuildTool, "-version"}, e.WorkDir, false)
		logger.InfoT(1, "%s -version: \n%s", e.BuildTool, out)
		return errors.New(fmt.Sprintf("环境校验失败：请检查工程根目录下是否能正常运行下面命令:【%s】如有报错，请解决报错后再次运行任务。", e.RunCmd))
	}

	//_, junitVersion := e.GetUnitFramework("")
	//hasJunit := junitVersion != ""
	//
	//if !hasJunit {
	//	logger.ErrorT(1, "%-10s", "【环境异常】❌")
	//	logger.ErrorT(1, "%-10s", "工具未检测到单元测试框架")
	//	return errors.New("环境校验失败：未检测到单元测试框架。")
	//}

	logger.InfoT(1, "【环境ok】")
	//logger.InfoT(1, "依赖缓冲路径：%s", e.LocalRepoPath)
	return nil
}

// GetUnitFrameworkByStaticParse 通过静态分析获取单元测试框架版本
//
// 参数:
// fPath string: 指定文件或目录的路径
//
// 返回值:
// string: 返回单元测试框架的版本字符串。如果为JUnit 5，则返回"JUNIT5"；如果为JUnit 4，则返回"JUNIT4"；如果无法确定版本，则默认为"JUNIT5"。
//
// 说明:
//  1. 优先取用例文件中的junit特征
//  2. 如果未取到，则尝试解析pom.xml 或者build.gradle文件
//  3. 如果仍未取到，则默认为Junit5
func (e *Environment) GetUnitFrameworkByStaticParse(fPath string) string {
	if e.UnitFramework != "" {
		return e.UnitFramework
	}

	mainPath := filepath.Join("src", "main", "java")
	testPath := filepath.Join("src", "test", "java")
	moduleRoot := strings.TrimSuffix(strings.Split(fPath, mainPath)[0], string(filepath.Separator))

	// 遍历当前目录及其子目录，查找 src/test/java 目录下以 Test.java 为后缀的测试文件
	_ = filepath.WalkDir(moduleRoot, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			logger.Error("Error accessing path %q: %v\n", path, err)
			return err
		}

		if !d.IsDir() && strings.Contains(path, testPath) && strings.HasSuffix(path, "Test.java") {
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			if strings.Contains(string(content), "org.junit.jupiter.api") {
				e.UnitFramework = constant.JUNIT5
				return nil
			} else if strings.Contains(string(content), "org.junit") {
				e.UnitFramework = constant.JUNIT4
				return nil
			}
		}

		return nil
	})

	if e.UnitFramework == "" {
		e.UnitFramework = e.DetectUnitFrameworkFromBuildConfig(moduleRoot)
	}

	if e.UnitFramework == "" {
		e.UnitFramework = constant.JUNIT5
	}

	return e.UnitFramework
}

func (e *Environment) DetectUnitFrameworkFromBuildConfig(moduleRoot string) string {
	junitVersion := ""
	if e.BuildTool == "mvn" {
		// 先取模块下的pom文件
		// 如果没取到，再取根目录下的pom文件
		pomFile := filepath.Join(moduleRoot, "pom.xml")
		if helper.IsFile(pomFile) {
			junitVersion = GetJunitVersionFromPom(pomFile)
		}
	} else if e.BuildTool == "gradle" || e.BuildTool == "./gradlew" {
		buildFile := filepath.Join(e.WorkDir, "build.gradle")
		if helper.IsFile(buildFile) {
			junitVersion = GetJunitVersionFromGradle(buildFile)
		}
	}

	if junitVersion != "" {
		return junitVersion
	} else if moduleRoot != e.WorkDir {
		return e.DetectUnitFrameworkFromBuildConfig(e.WorkDir)
	}

	return junitVersion
}

func (e *Environment) DetectGlobalUnitFramework() map[string]string {
	runner := &JavaAgentRunnerClient{
		ProjectRoot: e.WorkDir,
	}
	e.UnitFrameworks = runner.JudgeJunitVersion()
	if e.UnitFrameworks == nil {
		logger.Panic("无法正确解析到运行时依赖，退出运行！")
	} else if len(e.UnitFrameworks) == 0 {
		// 返回默认配置文件
		if e.BuildTool == "mvn" {
			e.UnitFrameworks[filepath.Join(e.WorkDir, "pom.xml")] = ""
		} else {
			e.UnitFrameworks[e.WorkDir] = ""
		}
	}

	for k, v := range e.UnitFrameworks {
		logger.InfoT(1, "%s: %s", k, v)
	}

	return e.UnitFrameworks
}

// GetUnitFramework 获取单元测试框架，必须有值
func (e *Environment) GetUnitFramework(modelPath string) string {
	if len(e.UnitFrameworks) > 0 {
		var matchedVersion string
		longestMatch := 0
		//var buildScriptPath string

		// 遍历 map，查找与 filePath 最接近且最长的路径前缀
		for path, version := range e.UnitFrameworks {
			npath := filepath.Dir(path)
			if strings.HasPrefix(modelPath, npath) && len(npath) > longestMatch {
				matchedVersion = version
				longestMatch = len(npath)
				//buildScriptPath = path
			}
		}
		return matchedVersion
	} else {
		e.DetectGlobalUnitFramework()
		return e.GetUnitFramework(modelPath)
	}
}

func annotateFuncionts(content string, startline, endline int) string {
	contents := strings.Split(content, "\n")
	for i := startline; i <= endline; i++ {
		contents[i] = "// " + contents[i]
	}
	return strings.Join(contents, "\n")
}

// RunValid 执行用例，返回结果
func (e *Environment) RunValid(caseInfo *models.CaseInfo, passCheck bool) *models.RunResult {
	utFullFilePath := caseInfo.TestFilePath

	// 如果ut文件不存在，则根据内容创建
	utFullFilePath = filepath.Join(os.TempDir(), fmt.Sprintf("%s.%s#%s",
		caseInfo.UnderTest.PackageName, caseInfo.TestClassName, caseInfo.TestMethodName),
		caseInfo.TestClassName+".java")
	newTestFileContent := caseInfo.TestFileContent
	if caseInfo.TestMethodName != "" {
		fileParser, err := javaparser.NewFileParserWithContent(newTestFileContent)
		if err != nil {
			logger.ErrorT(1, "文件解析失败: %s", err)
		}
		// 单测文件解析
		testFileContext := fileParser.Parse(&base.CodeFormatOptions{
			MethodNameToCamelCase: true,
		})
		if testFileContext == nil || testFileContext.MainClass == nil {
			return nil
		}
		methods := testFileContext.MainClass.Methods
		for _, method := range methods {
			if method.Identifier == caseInfo.TestMethodName {
				continue
			}
			if method.IsTestCase {
				newTestFileContent = annotateFuncionts(newTestFileContent, method.StartLine, method.EndLine)
			}
		}
	}

	if err := helper.WriteContent(utFullFilePath, newTestFileContent); err != nil {
		return nil
	}

	runner := &JavaAgentRunnerClient{
		ProjectRoot:    e.WorkDir,
		UTFullFilePath: utFullFilePath,
		UTClassName:    caseInfo.TestClassName,
		UTMethodName:   caseInfo.TestMethodName,
		SrcFilePath:    caseInfo.UnderTest.FilePath,
		SrcClassName:   caseInfo.UnderTest.ClassName,
		PackageName:    caseInfo.UnderTest.PackageName,
		JUnitVersion:   e.UnitFramework,
		LocalRepoPath:  e.LocalRepoPath,
	}

	return runner.RunTestWithJacocoAgent(passCheck)
}
