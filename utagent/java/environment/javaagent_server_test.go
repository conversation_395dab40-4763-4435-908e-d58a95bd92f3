package environment

import (
	"testing"

	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

func TestJavaAgentRunner_RunTestWithJacocoAgent(t *testing.T) {
	type fields struct {
		ExecPath       string
		ProjectRoot    string
		SrcFilePath    string
		SrcClassName   string
		UTFullFilePath string
		PackageName    string
		UTClassName    string
		UTMethodName   string
		JUnitVersion   string
	}
	tests := []struct {
		name            string
		fields          fields
		wantRequestBody map[string]string
		want            *models.RunResult
		wantErr         bool
	}{
		{
			name: "test1",
			fields: fields{
				ProjectRoot:    "/Users/<USER>/test/javaagent",
				UTFullFilePath: "/src/test/java/com/baidu/cov/utils/StringKitTest.java",
				SrcFilePath:    "/src/main/java/com/baidu/cov/utils/StringKit.java",
				SrcClassName:   "StringKit",
				UTClassName:    "StringKitTest",
				JUnitVersion:   "junit5",
				PackageName:    "com.baidu.cov.utils",
			},
			wantRequestBody: map[string]string{},
			want:            &models.RunResult{},
			wantErr:         false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			runner := &JavaAgentRunner{
				ProjectRoot:    tt.fields.ProjectRoot,
				SrcFilePath:    tt.fields.SrcFilePath,
				SrcClassName:   tt.fields.SrcClassName,
				UTFullFilePath: tt.fields.UTFullFilePath,
				PackageName:    tt.fields.PackageName,
				UTClassName:    tt.fields.UTClassName,
				UTMethodName:   tt.fields.UTMethodName,
				JUnitVersion:   tt.fields.JUnitVersion,
			}
			got := runner.RunTestWithJacocoAgent()
			if got == nil {
				t.Errorf("RunTestWithJacocoAgent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_healthCheck(t *testing.T) {
	healthCheck()
}
