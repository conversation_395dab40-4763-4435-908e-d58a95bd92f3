package bug_detect

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

const SecondChatForBugReason = "请使用简短的语言为我解释缺陷原因，并给出修复建议。参照如下格式：\n### 缺陷原因\n...\n### 修复建议\n..."

type AssertDetector struct {
	Model      *llm.ModelConf
	llmService *llm.QFService
	Input      *models.RepairInput
}

func NewAssertDetector(input *models.RepairInput) *AssertDetector {
	// 默认使用EB35模型
	ad := &AssertDetector{
		Input: input,
	}
	ad.llmService = llm.NewQFService(llm.ModelConfForQianfan(llm.EB35))
	return ad
}

func (ad *AssertDetector) Detect() (bool, string) {
	inputData := ad.Input
	data := &promptbuilder.InputData{
		Language:        "java",
		SrcFileContent:  inputData.SrcFileContent,
		TestFileContent: inputData.TestFileContent,
		TestFilePath:    inputData.TestFilePath,
	}

	data.LineNo = len(strings.Split(inputData.TestFileContent, "\n"))
	for _, err := range inputData.RunResult.RunResults {
		data.ErrorLog += err.Exception + "\n"
		// 处理trace信息，只添加到当前文件报错行
		for _, trace := range err.StackTrace {
			data.ErrorLog += fmt.Sprintf("    at %s.%s(%s:%d)\n", trace.DeclaringClass,
				trace.MethodName,
				trace.FileName, trace.LineNumber)
			if trace.FileName == filepath.Base(inputData.TestFilePath) {
				break
			}
		}
	}

	logger.Debug("====== 调用llm检测是否为业务代码缺陷 ======")
	// 构建prompt
	buildPrompt, err := promptbuilder.BuildPrompt(promptbuilder.LLMBugDetect, data, -1)
	helper.SaveTmpFile(fmt.Sprintf("bug-detect-%d-llm-src.txt", time.Now().UnixMilli()), buildPrompt)
	// 调用llm
	llmResponse, err := ad.llmService.InferenceWithMultiChat(buildPrompt)
	if err != nil {
		return false, ""
	}

	result := ad.getResultFromLLMResponse(llmResponse)
	logger.Debug("llm检测结果：是否为业务代码缺陷：%v", result)
	if !result {
		// 非 被测代码bug
		return false, ""
	}

	bugReason, err := ad.llmService.InferenceWithMultiChat(SecondChatForBugReason)
	if err != nil {
		return result, ""
	}
	return result, bugReason
}

func (ad *AssertDetector) getResultFromLLMResponse(llmResponse string) bool {
	if llmResponse == "" {
		return false
	}
	re := regexp.MustCompile("是否为业务代码缺陷：\\s*([01])")
	matches := re.FindStringSubmatch(llmResponse)
	if len(matches) > 0 {
		if matches[1] == "1" {
			return true
		}
	}
	return false
}

func IsSrcBug(input *models.RepairInput) (bool, string) {
	ad := NewAssertDetector(input)
	return ad.Detect()
}
