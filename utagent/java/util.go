package java

import (
	"bufio"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	javaparser "icode.baidu.com/baidu/cov/smartUT-parser/java"
	"path/filepath"
	"strings"
)

// GetTestFile 根据被测文件，查找用例文件，返回其路径及内容
func GetTestFile(srcFile, targetMethodName string) *models.TestFile {
	// 替换路径中的目录
	utFile := strings.Replace(srcFile, filepath.Join("src", "main", "java"), filepath.Join("src", "test", "java"), 1)

	// 获取类名
	base := filepath.Base(utFile)
	clsName := strings.TrimSuffix(base, ".java")

	// 生成测试文件路径
	testFile := matchUTFile(filepath.Dir(utFile), clsName, targetMethodName)
	if testFile == nil {
		testFile = matchUTFile(filepath.Dir(utFile), clsName, "")
	}

	return testFile
}

func GetTestFilePath(srcFile string) string {
	utFile := strings.Replace(srcFile, filepath.Join("src", "main", "java"), filepath.Join("src", "test", "java"), 1)
	// 获取类名
	base := filepath.Base(utFile)
	clsName := strings.TrimSuffix(base, ".java")
	dir := filepath.Dir(utFile)
	utFile = filepath.Join(dir, fmt.Sprintf("%sTest.java", clsName))

	if !helper.IsFileExist(utFile) {
		utFile = filepath.Join(dir, fmt.Sprintf("Test%s.java", clsName))
	}
	return utFile
}

func matchUTFile(dir, clsName, methodName string) *models.TestFile {
	className := clsName
	if methodName != "" {
		className = fmt.Sprintf("%s_%s", clsName, methodName)
	}
	utFile := filepath.Join(dir, fmt.Sprintf("%sTest.java", className))
	utClsName := fmt.Sprintf("%sTest", className)

	if !helper.IsFileExist(utFile) {
		utFile = filepath.Join(filepath.Dir(utFile), fmt.Sprintf("Test%s.java", className))
		utClsName = fmt.Sprintf("Test%s", className)
	}

	if !helper.IsFileExist(utFile) {
		return nil
	}

	return &models.TestFile{
		TestFilePath:    utFile,
		TestFileContent: helper.ReadFile(utFile),
		TestClassName:   utClsName,
	}
}

func GetTestFilePathByClsName(srcFile, testClsName string) string {
	if srcFile == "" || testClsName == "" {
		return ""
	}
	// 替换路径中的目录
	utFilePath := strings.Replace(srcFile, filepath.Join("src", "main", "java"), filepath.Join("src", "test", "java"), 1)

	// 生成测试文件路径
	utFilePath = filepath.Join(filepath.Dir(utFilePath), testClsName+".java")
	return utFilePath
}

func GetFullClsNameByFilePath(filePath string) string {
	filePath = filepath.ToSlash(filePath)
	if filePath == "" || !(strings.Contains(filePath, "src/test/java/") || strings.Contains(filePath, "src/main/java")) {
		return ""
	}
	var tag string
	if strings.Contains(filePath, "src/test/java") {
		tag = "src/test/java/"
	} else if strings.Contains(filePath, "src/main/java") {
		tag = "src/main/java/"
	} else {
		return filePath
	}

	return strings.Replace(filePath[strings.Index(filePath, tag)+len(tag):len(filePath)-5], "/", ".", -1)
}

func DelBugMethodFromContent(testFilePath string, succMethods []string) string {
	fileParser, err := javaparser.NewFileParserWithFilepath(testFilePath)
	if err != nil {
		return ""
	}
	testFileContext := fileParser.Parse(nil)
	if testFileContext.MainClass == nil || len(testFileContext.MainClass.Methods) == 0 {
		return ""
	}

	var delMethodsStartAndEnd = make(map[int]int)
	for _, method := range testFileContext.MainClass.Methods {
		if method.IsTestCase && !helper.InSlice(succMethods, method.Identifier) {
			startLine := method.StartLine
			if method.Comment != "" {
				// 如果存在注释，则起始行需要减去注释的行数，把注释也删除掉
				startLine = startLine - len(strings.Split(strings.TrimSpace(method.Comment), "\n"))
			}
			delMethodsStartAndEnd[startLine] = method.EndLine
		}
	}

	scanner := bufio.NewScanner(strings.NewReader(testFileContext.FileContent))

	var lines []string
	var currentLine = 0
	tmpEndln := -1
	for scanner.Scan() {
		line := scanner.Text()
		if delLine, ok := delMethodsStartAndEnd[currentLine]; ok {
			// 如果当前行是删除区域的起始行，跳过直到结束行
			tmpEndln = delLine
			currentLine++
			continue
		}

		if currentLine < tmpEndln {
			currentLine++
			continue
		} else if currentLine == tmpEndln {
			tmpEndln = -1
			currentLine++
			continue
		}
		lines = append(lines, line)
		currentLine++
	}
	if err := scanner.Err(); err != nil {
		return ""
	}
	return strings.Join(lines, "\n")
}
