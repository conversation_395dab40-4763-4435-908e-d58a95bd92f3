package javarepair

import (
	"fmt"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

func TestSymbolRepairAction_getPkgImportBySymbol(t *testing.T) {
	type fields struct {
		Name          string
		Input         *models.RepairInfo
		Symbols       map[string]string
		MethodSymbols map[string]string
	}
	type args struct {
		ce *models.CompileError
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		//{
		//	name: "test1",
		//	fields: fields{
		//		Name: "SymbolRepairAction",
		//		Symbols: map[string]string{
		//			"TestClass": "java.util.TestClass",
		//		},
		//	},
		//	args: args{
		//		ce: &models.CompileError{
		//			ErrorMessage: "symbol: class TestClass",
		//		},
		//	},
		//	want: "import java.util.TestClass;",
		//},
		//{
		//	name: "test2",
		//	fields: fields{
		//		Name: "SymbolRepairAction",
		//		Symbols: map[string]string{
		//			"Arrays": "java.util.Arrays",
		//		},
		//	},
		//	args: args{
		//		ce: &models.CompileError{
		//			ErrorMessage: "symbol: variable Arrays",
		//		},
		//	},
		//	want: "import java.util.Arrays;",
		//},
		{
			name: "test3",
			fields: fields{
				Name: "SymbolRepairAction",
				Symbols: map[string]string{
					"listArrays": "java.util.ListArrays",
				},
				MethodSymbols: map[string]string{
					"assert": "static org.junit.jupiter.api.Assertions.*",
				},
			},
			args: args{
				ce: &models.CompileError{
					ErrorMessage: "symbol:   method assertThrows(java.lang.Class<java.lang.RuntimeException>,",
				},
			},
			want: "import static org.junit.jupiter.api.Assertions.*;",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SymbolRepairAction{
				Name:    tt.fields.Name,
				Input:   nil,
				Symbols: tt.fields.Symbols,
			}
			if got := s.getPkgImportBySymbol(tt.args.ce); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getPkgImportBySymbol() = %v, want %v", got, tt.want)
				fmt.Println(got, tt.want)
			}
		})
	}
}
