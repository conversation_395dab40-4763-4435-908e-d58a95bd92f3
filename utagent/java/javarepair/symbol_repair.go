package javarepair

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"regexp"
	"strings"
)

type SymbolRepairAction struct {
	Name    string              `json:"name"`
	Input   *models.RepairInput `json:"input"`
	Symbols map[string]string   `json:"symbols"`
	// MethodSymbols map[string]string   `json:"method_symbols"`
	UnitSymbols map[string]map[string]string `json:"unit_symbols"`
}

func (s *SymbolRepairAction) Call() (string, bool, string, error) {
	if s.Symbols == nil || len(s.Symbols) == 0 {
		// 未获取到符号表，不进行修复，直接返回原来的内容
		return s.Input.TestFileContent, false, "", nil
	}

	if s.Input.RunResult == nil || len(s.Input.RunResult.CompileErrors) == 0 {
		// 没有编译错误，直接返回原来的内容
		return s.Input.TestFileContent, false, "", nil
	}

	compileErrors := make([]*models.CompileError, 0)
	AddedImports := make([]string, 0)

	for _, ce := range s.Input.RunResult.CompileErrors {
		if ce.ErrorType != "ERROR" {
			continue
		}
		if s.isSymbolError(ce) {
			// 修复符号错误
			pkgName := s.getPkgImportBySymbol(ce)
			if pkgName != "" {
				//logger.InfoT(2, "修复符号错误，变量名：%s, 包名：%s", ce.ErrorMessage, pkgName)
				if !helper.InSlice(AddedImports, pkgName) {
					AddedImports = append(AddedImports, pkgName)
				}
			} else {
				logger.DebugT(2, "无法修复符号错误，变量名：%s", ce.ErrorMessage)
				compileErrors = append(compileErrors, ce)
			}
		} else {
			logger.DebugT(2, "非符号错误，忽略：%s", ce.ErrorMessage)
			compileErrors = append(compileErrors, ce)
		}
	}

	newContent := s.InsertImportsAndUpdateCompileErrors(AddedImports, compileErrors)
	if len(compileErrors) == 0 {
		return newContent, true, "", nil
	} else if len(AddedImports) > 0 {
		// 已经进行了规则修复，但仍有其他错误的处理
		for _, err := range s.Input.RunResult.RunResults {
			// 处理trace信息中的行号
			for _, trace := range err.StackTrace {
				trace.LineNumber += len(AddedImports)
			}
		}
		// 传给模型继续进行修复
		s.Input.RunResult.CompileErrors = compileErrors
		return newContent, false, "", nil
	} else {
		s.Input.RunResult.CompileErrors = compileErrors
		return s.Input.TestFileContent, false, "", nil
	}
}

func (s *SymbolRepairAction) getPkgImportBySymbol(ce *models.CompileError) string {
	regexPatterns := []*regexp.Regexp{
		regexp.MustCompile(`符号:\s+(变量|类)\s+(\S+)`),
		regexp.MustCompile(`symbol:\s+(variable|class)\s+(\S+)`),
		regexp.MustCompile(`symbol:\s+(method)\s+(\S+?)\(`),
		regexp.MustCompile(`符号:\s+(方法)\s+(\S+?)\(`),
	}

	// 尝试在错误消息中找到匹配项
	var matches []string
	for _, re := range regexPatterns {
		matches = re.FindStringSubmatch(ce.ErrorMessage)
		if len(matches) > 2 {
			break
		}
	}
	if len(matches) > 2 {
		im := s.getImportBySymbol(matches[2])
		if im != "" {
			return fmt.Sprintf("import %s;", im)
		}
	}

	return ""
}

func (s *SymbolRepairAction) getImportBySymbol(symbol string) string {
	im := s.Symbols[symbol]
	if im == "" {
		for k, v := range s.UnitSymbols[s.Input.TestFramework] {
			if strings.HasPrefix(symbol, k) {
				return v
			}
		}
	}

	return im
}

func (s *SymbolRepairAction) isSymbolError(ce *models.CompileError) bool {
	if strings.Contains(ce.ErrorMessage, "找不到符号") {
		return true
	}
	if strings.Contains(ce.ErrorMessage, "cannot find symbol") {
		return true
	}
	return false
}

func (s *SymbolRepairAction) GetName() string {
	return s.Name
}

func (s *SymbolRepairAction) SetInput(input *models.RepairInput) {
	s.Input = input
}

func (s *SymbolRepairAction) InsertImportsAndUpdateCompileErrors(newImports []string,
	compileErrors []*models.CompileError) string {
	if len(newImports) == 0 {
		return s.Input.TestFileContent
	}

	lines := strings.Split(s.Input.TestFileContent, "\n")
	var result []string
	packageFound := strings.Contains(s.Input.TestFileContent, "package")

	if !packageFound {
		// 如果没有找到 package 行，在最开始插入 imports
		result = append(newImports, lines...)
	} else {
		for i, line := range lines {
			if strings.HasPrefix(strings.TrimSpace(line), "package") {
				result = append(result, line)
				result = append(result, newImports...)
				if i+1 < len(lines) {
					result = append(result, lines[i+1:]...)
					break
				}
			} else {
				result = append(result, line)
			}
		}
	}

	utContent := strings.Join(result, "\n")
	return utContent
}

func NewSymbolRepairAction(symbols map[string]string) *SymbolRepairAction {
	symbolRepairAction := &SymbolRepairAction{Name: "SYMBOL_REPAIR", Symbols: symbols, UnitSymbols: make(map[string]map[string]string)}
	symbolRepairAction.AddTestSymbols()
	return symbolRepairAction
}

func (s *SymbolRepairAction) AddTestSymbols() {
	s.Symbols["SpringBootTest"] = "org.springframework.boot.test.context.SpringBootTest"
	s.Symbols["Autowired"] = "org.springframework.beans.factory.annotation.Autowired"
	s.Symbols["SpringRunner"] = "org.springframework.test.context.junit4.SpringRunner"
	s.Symbols["AutoConfigureMockMvc"] = "org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc"
	s.Symbols["MockBean"] = "org.springframework.boot.test.mock.mockito.MockBean"
	s.Symbols["WebEnvironment"] = "org.springframework.boot.test.context.SpringBootTest.WebEnvironment"
	s.Symbols["Mock"] = "org.mockito.Mock"
	s.Symbols["Spy"] = "org.mockito.Spy"
	s.Symbols["InjectMocks"] = "org.mockito.InjectMocks"
	s.Symbols["HttpServletRequestBuilder"] = "javax.servlet.http.HttpServletRequestBuilder"
	s.Symbols["HttpServletRequest"] = "javax.servlet.http.HttpServletRequest"
	s.Symbols["HttpServletResponse"] = "javax.servlet.http.HttpServletResponse"
	s.Symbols["AutoConfigureMockMvc"] = "org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc"
	s.Symbols["WebMvcTest"] = "org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest"
	s.Symbols["WebApplicationContext"] = "org.springframework.web.context.WebApplicationContext"
	s.Symbols["PowerMockito"] = "org.powermock.api.mockito.PowerMockito"
	s.Symbols["PrepareForTest"] = "org.powermock.core.classloader.annotations.PrepareForTest"

	s.UnitSymbols[constant.JUNIT4] = map[string]string{
		"RunWith":     "org.junit.runner.RunWith",
		"Test":        "org.junit.Test",
		"Before":      "org.junit.Before",
		"After":       "org.junit.After",
		"BeforeClass": "org.junit.BeforeClass",
		"AfterClass":  "org.junit.AfterClass",
		"Ignore":      "org.junit.Ignore",
		"Expected":    "org.junit.Test",
		"Assert":      "org.junit.Assert",
		"assert":      "static org.junit.Assert.*",
	}

	s.UnitSymbols[constant.JUNIT5] = map[string]string{
		"Test":             "org.junit.jupiter.api.Test",
		"BeforeEach":       "org.junit.jupiter.api.BeforeEach",
		"AfterEach":        "org.junit.jupiter.api.AfterEach",
		"BeforeAll":        "org.junit.jupiter.api.BeforeAll",
		"AfterAll":         "org.junit.jupiter.api.AfterAll",
		"Assertions":       "org.junit.jupiter.api.Assertions",
		"Timeout":          "org.junit.jupiter.api.Timeout",
		"ExtendWith":       "org.junit.jupiter.api.extension.ExtendWith",
		"MockitoExtension": "org.mockito.junit.jupiter.MockitoExtension",
		"SpringExtension":  "org.springframework.test.context.junit.jupiter.SpringExtension",
		"assert":           "static org.junit.jupiter.api.Assertions.*",
	}
}
