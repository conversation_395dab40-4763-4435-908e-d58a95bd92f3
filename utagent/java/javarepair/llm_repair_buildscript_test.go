package javarepair

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/cov/iUT/helper"
)

func TestLLMRepairBuildScript_Call(t *testing.T) {
	tests := []struct {
		name    string
		l       LLMRepairBuildScript
		want    string
		wantErr bool
	}{
		//{
		//	name: "test1",
		//	l: NewLLMRepairBuildScript(
		//		"",
		//		"",
		//		"/Users/<USER>/git/baidu/cov/java-evaluate-data/xxl-job/xxl-job-executor-samples/xxl-job-executor-sample-frameless/pom.xml",
		//	),
		//	wantErr: false,
		//},
		{
			name: "test1",
			l: NewLLMRepairBuildScript(
				"",
				"",
				"/Users/<USER>/git/baidu/coding-suggestion/intellij-plugin/build.gradle.kts",
			),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.l.Call()
			fmt.Println(got)
			fmt.Println(got, err)
			//assert.Equal(t, tt.wantErr, err != nil)
		})
	}
}

func TestLLMRepairBuildScript_Call1(t *testing.T) {
	pomPath := "/Users/<USER>/workspace/test/crtest/pom.xml"
	l := NewLLMRepairBuildScript("pom", helper.ReadFile(pomPath), pomPath)
	got, err := l.Call()
	fmt.Println(got)
	fmt.Println(got, err)
}

// TestNewLLMRepairBuildScript_WithEmptyScriptContentAndEmptyScriptPath 是用于测试 NewLLMRepairBuildScript_WithEmptyScriptContentAndEmptyScriptPath
// generated by Comate
func TestNewLLMRepairBuildScript_WithEmptyScriptContentAndEmptyScriptPath(t *testing.T) {
	scriptType := "pom"
	scriptContent := ""
	scriptPath := ""

	script := NewLLMRepairBuildScript(scriptType, scriptContent, scriptPath)

	if script.ScriptType != "pom" {
		t.Errorf("Expected ScriptType to be 'pom', got %s", script.ScriptType)
	}

	if script.ScriptContent != "" {
		t.Errorf("Expected ScriptContent to be empty, got non-empty")
	}

	if script.ScriptPath != "" {
		t.Errorf("Expected ScriptPath to be empty, got %s", scriptPath)
	}
}
