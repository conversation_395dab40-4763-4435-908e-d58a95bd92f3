package javarepair

import (
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
	"os"
	"path/filepath"
)

type LLMRepairBuildScript struct {
	ScriptType    string
	ScriptContent string
	ScriptPath    string

	LLMModel *llm.ModelConf
}

func NewLLMRepairBuildScript(scriptType, scriptContent, scriptPath string) LLMRepairBuildScript {
	llmRepairBuildScript := LLMRepairBuildScript{
		ScriptType:    scriptType,
		ScriptContent: scriptContent,
		ScriptPath:    scriptPath,
	}

	// 如果ScriptContent为空，ScriptPath不为空，则读取内容到ScriptContent中
	if llmRepairBuildScript.ScriptContent == "" && llmRepairBuildScript.ScriptPath != "" {
		content, _ := os.ReadFile(llmRepairBuildScript.ScriptPath)
		llmRepairBuildScript.ScriptContent = string(content)
	}

	if llmRepairBuildScript.ScriptType == "" && llmRepairBuildScript.ScriptPath != "" {
		if filepath.Ext(llmRepairBuildScript.ScriptPath) == ".xml" {
			llmRepairBuildScript.ScriptType = "pom"
		} else if filepath.Ext(llmRepairBuildScript.ScriptPath) == ".gradle" ||
			filepath.Ext(llmRepairBuildScript.ScriptPath) == ".kts" {
			llmRepairBuildScript.ScriptType = "gradle"
		}
	}
	llmRepairBuildScript.LLMModel = llm.ModelConfForInnerUtInfer(llm.DSV3)
	return llmRepairBuildScript
}

func (l LLMRepairBuildScript) GetType() string {
	return l.ScriptType
}

func (l LLMRepairBuildScript) Call() (string, error) {
	// 构建prompt
	buildPrompt, err := promptbuilder.BuildPrompt(promptbuilder.LLMRepairBuildScript, l, -1)
	if err != nil {
		return "", err
	}

	result, err := llm.Inference(buildPrompt, l.LLMModel)
	if err != nil {
		return "", err
	}

	return result, nil
}
