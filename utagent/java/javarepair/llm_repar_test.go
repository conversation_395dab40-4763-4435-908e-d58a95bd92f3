package javarepair

import (
	"fmt"
	"testing"
)

func Test_shortenAndAddLineNo(t *testing.T) {
	type args struct {
		fileContent  string
		testCaseName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test with target test case",
			args: args{
				fileContent: `
				@Test
				public void test1() {
					System.out.println("test1");
				}
				@Test
				public void test2() {
					System.out.println("test2");
				}
				@Test
				public void test3() {
					System.out.println("test3");
				}
				@Test
				public void test4() {
					System.out.println("test4");
				}
				@Test
				public void test5() {
					System.out.println("test5");
				}
			`,
				testCaseName: "test3",
			},
		},
		{name: "Test with target test case",
			args: args{
				fileContent: `
package com.macro.mall.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.dto.UmsAdminParam;
import com.macro.mall.model.UmsAdmin;
import com.macro.mall.service.UmsAdminService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletRequest;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import org.springframework.beans.factory.annotation.Value;
import java.util.Arrays;

@TestPropertySource(properties = {
        "jwt.tokenHead=Bearer ",
        "jwt.tokenHeader=Authorization"
})
public class UmsAdminControllerTest{

    @Value("${jwt.tokenHead}")
    private String tokenHead;

    @Value("${jwt.tokenHeader}")
    private String tokenHeader;

    private MockMvc mockMvc;

    @InjectMocks
    private UmsAdminController umsAdminController;

    @Mock
    private UmsAdminService adminService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // testRegisterSuccess 用于测试 register
    // generated by Comate
    @Test
    public void testRegisterSuccess() {
        UmsAdminParam umsAdminParam = new UmsAdminParam();
        umsAdminParam.setUsername("testUser");
        umsAdminParam.setPassword("password");
    
        UmsAdmin umsAdmin = new UmsAdmin();
        umsAdmin.setId(1L);
        umsAdmin.setUsername("testUser");
    
        when(adminService.register(any(UmsAdminParam.class))).thenReturn(umsAdmin);
    
        CommonResult<UmsAdmin> result = umsAdminController.register(umsAdminParam);
    
        assertEquals(CommonResult.success(umsAdmin).getCode(), result.getCode());
        assertEquals(umsAdmin, result.getData());
    
        verify(adminService, times(1)).register(umsAdminParam);
    }

    // testRegisterFailed 用于测试 register
    // generated by Comate
    @Test
    public void testRegisterFailed() {
        UmsAdminParam umsAdminParam = new UmsAdminParam();
        umsAdminParam.setUsername("testUser");
        umsAdminParam.setPassword("password");
    
        when(adminService.register(any(UmsAdminParam.class))).thenReturn(null);
    
        CommonResult<UmsAdmin> result = umsAdminController.register(umsAdminParam);
    
        assertEquals(CommonResult.failed().getCode(), result.getCode());
        assertEquals(null, result.getData());
    
        verify(adminService, times(1)).register(umsAdminParam);
    }

    // testRefreshTokenTokenExpired 用于测试 refreshToken
    // generated by Comate
    @Test
    public void testRefreshTokenTokenExpired() throws Exception {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(umsAdminController).build();
    
        String oldToken = "oldToken";
        HttpServletRequest request = org.mockito.Mockito.mock(HttpServletRequest.class);
        when(request.getHeader("Authorization")).thenReturn(oldToken);
        when(adminService.refreshToken(oldToken)).thenReturn(null);
    
        mockMvc.perform(get("/admin/refreshToken")
                .header("Authorization", oldToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("token已经过期！"));
    }

    // testUpdateRoleFailed 用于测试 updateRole
    // generated by Comate
    @Test
    public void testUpdateRoleFailed() throws Exception {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(umsAdminController).build();
    
        Long adminId = 1L;
        List<Long> roleIds = Arrays.asList(1L, 2L);
    
        when(adminService.updateRole(adminId, roleIds)).thenReturn(-1);
    
        mockMvc.perform(post("/admin/role/update")
                .param("adminId", adminId.toString())
                .param("roleIds", roleIds.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("操作失败"));
    }

}`, testCaseName: "testUpdateRoleFailed",
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := shortenAndAddLineNo(tt.args.fileContent, tt.args.testCaseName)
			fmt.Println(got)
			if got == tt.args.fileContent {
				t.Errorf("shortenAndAddLineNo() = %v, want %v", got, tt.want)
			}
		})
	}
}
