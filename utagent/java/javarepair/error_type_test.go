package javarepair

import (
	"testing"

	"github.com/google/go-cmp/cmp"

	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

func TestAnalyzeErrorType(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected models.ErrorType
	}{
		{
			name:     "import a not existing package",
			input:    "package Function does not exist",
			expected: PkgNotExist,
		},
		{
			name:     "cannot find symbol variable",
			input:    "MetricIdeUseCalcTest.java:61:33\nMetricIdeUseCalcTest.java:61: error: cannot find symbol\n        DataType type = DataType.other;\n                                ^\n  symbol:   variable other\n  location: class com.baidu.coding.suggestion.gateway.metrics.bean.DataType",
			expected: CannotFindSymbolVariable,
		},
		{
			name:     "cannot find symbol method",
			input:    "ScriptUtilTest.java:96:14\nScriptUtilTest.java:96: 错误: 找不到符号\n        Files.writeString(scriptFile, \"#!/bin/bash\\necho 'Hello, World!'\");\n             ^\n  符号:   方法 writeString(java.nio.file.Path,java.lang.String)\n  位置: 类 java.nio.file.Files",
			expected: CannotFindSymbolMethod,
		},
		{
			name:     "has private access",
			input:    "AutoworkConfigServiceImplTest.java:31:37\nAutoworkConfigServiceImplTest.java:31: error: cache has private access in com.baidu.coding.suggestion.gateway.customize.service.impl.AutoworkConfigServiceImpl\n        verify(autoworkConfigService.cache, times(1)).refresh(AutoworkConfigServiceImpl.KEY);",
			expected: HasPrivateAccess,
		},
		{
			name:     "mock exception",
			input:    "org.mockito.exceptions.base.MockitoException: \nCannot mock/spy class java.net.URL\nMockito cannot mock/spy because :\n - final class\n    at com.xxl.job.core.util.XxlJobRemotingUtilTest.testPostBodyException(XxlJobRemotingUtilTest.java:17)\n",
			expected: MockException,
		},
		{
			name:     "no match",
			input:    "error message",
			expected: "",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := GetErrorType(tc.input)
			if diff := cmp.Diff(tc.expected, actual); diff != "" {
				t.Errorf("unexpected error type (-want +got):\n%s", diff)
			}
		})
	}
}
