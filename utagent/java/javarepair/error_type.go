package javarepair

import (
	"regexp"

	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

const (
	PkgNotExist models.ErrorType = "PkgNotExist"

	CannotFindSymbolVariable models.ErrorType = "CannotFindSymbol_Variable"
	CannotFindSymbolMethod   models.ErrorType = "CannotFindSymbol_Method"
	CannotFindSymbolClass    models.ErrorType = "CannotFindSymbol_Class"

	HasPrivateAccess     models.ErrorType = "HasPrivateAccess"
	Exception            models.ErrorType = "Exception"
	MockException        models.ErrorType = "MockException"
	IncompatibleTypes    models.ErrorType = "IncompatibleTypes"
	CannotFindContructor models.ErrorType = "CannotFindConstructor"
	NotInitVar           models.ErrorType = "NotInitVar"

	AssertionError      models.ErrorType = "AssertionError"
	UnnecessaryStubbing models.ErrorType = "Unnecessary stubbings detected."

	Unknown models.ErrorType = "Unknown"
)

var errPatterns = map[models.ErrorType][]*regexp.Regexp{
	PkgNotExist:              {regexp.MustCompile(`package (\S+) does not exist`), regexp.MustCompile(`程序包(\S)+不存在`)},
	CannotFindSymbolVariable: {regexp.MustCompile(`cannot find symbol(.|\n)*\bsymbol:\s+variable\s+(\S+)`), regexp.MustCompile(`找不到符号(.|\n)*符号:\s+(变量)\s+(\S+?)`)},
	CannotFindSymbolMethod:   {regexp.MustCompile(`cannot find symbol(.|\n)*\bsymbol:\s+method\s+(\S+)`), regexp.MustCompile(`找不到符号(.|\n)*符号:\s+(方法)\s+(\S+?)`)},
	CannotFindSymbolClass:    {regexp.MustCompile(`cannot find symbol(.|\n)*\bsymbol:\s+class\s+(\S+)`), regexp.MustCompile(`找不到符号(.|\n)*符号:\s+(类)\s+(\S+?)`)},
	HasPrivateAccess:         {regexp.MustCompile(`has private access`), regexp.MustCompile(`private 访问控制`)},
	Exception:                {regexp.MustCompile(`java(\S+)Exception`)},
	IncompatibleTypes:        {regexp.MustCompile(`incompatible types:`)},
	MockException:            {regexp.MustCompile(`MockitoException`), regexp.MustCompile(`NotAMockException`)},
	CannotFindContructor:     {regexp.MustCompile(`找不到合适的构造器`), regexp.MustCompile(`no suitable constructor found`)},
	NotInitVar:               {regexp.MustCompile(`未初始化变量`), regexp.MustCompile(`not have been initialized`)},
	AssertionError:           {regexp.MustCompile(`AssertionFailedError`), regexp.MustCompile(`AssertionError`)},
	UnnecessaryStubbing:      {regexp.MustCompile(`Unnecessary stubbings detected.`)},
}

func GetErrorType(input string) models.ErrorType {
	for k, v := range errPatterns {
		for _, r := range v {
			if r.MatchString(input) {
				return k
			}
		}
	}
	return ""
}
