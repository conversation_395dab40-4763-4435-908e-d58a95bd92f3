package javarepair

import (
	"bufio"
	"context"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/gen"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"icode.baidu.com/baidu/cov/iUT/utagent/mongo"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
	"path/filepath"
	"regexp"
	"strings"
)

type LLMRepairAction struct {
	Name     string
	Input    *models.RepairInput
	LLMModel *llm.ModelConf
}

// NewLLMRepairAction 暂时使用EC2模型
func NewLLMRepairAction(modelConfig *llm.ModelConf) *LLMRepairAction {
	if modelConfig == nil {
		modelConfig = llm.ModelConfForInnerUtInfer(llm.GetDefaultRepairModel())
	}
	return &LLMRepairAction{
		Name:     "LLM_REPAIR",
		LLMModel: modelConfig,
	}
}

func (l *LLMRepairAction) SetInput(input *models.RepairInput) {
	l.Input = input
}

func shortenAndAddLineNo(originalContent string, testCaseName string) string {
	scanner := bufio.NewScanner(strings.NewReader(originalContent))

	var finalContent []string
	var testCase []string
	var inTargetTestCase bool
	var skip bool
	var found bool

	// Regex patterns to identify test method, annotations, and comments
	testMethodPattern := regexp.MustCompile(`\s+@Test\s*`)
	commentPattern := regexp.MustCompile(`\s*//\s*`)
	var lineNo int

	for scanner.Scan() {
		line := scanner.Text()
		lineNo += 1
		if commentPattern.MatchString(line) {
			line = ""
		}

		// 用例开始行
		if testMethodPattern.MatchString(line) {
			// 上次用例结束，目标用例则添加到最终结果中
			if len(testCase) != 0 {
				if inTargetTestCase == true {
					finalContent = append(finalContent, testCase...)
					skip = false
					found = true
				} else {
					if !skip {
						if !found {
							finalContent = append(finalContent, fmt.Sprintf("\t...\n\t// 已省略无关用例\n\t..."))
						}
						skip = true
					}
				}
			}

			inTargetTestCase = false
			testCase = []string{fmt.Sprintf("%d. %s", lineNo, line)}
			continue
		}

		// 如果在用例中，则判定是否为指定的目标用例
		if len(testCase) != 0 {
			if strings.Contains(line, testCaseName) {
				inTargetTestCase = true
			}

			testCase = append(testCase, fmt.Sprintf("%d. %s", lineNo, line))
		} else {
			finalContent = append(finalContent, fmt.Sprintf("%d. %s", lineNo, line))
		}
	}

	if len(testCase) != 0 && inTargetTestCase == true {
		finalContent = append(finalContent, testCase...)
		found = true
	}

	if err := scanner.Err(); err != nil {
		return ""
	}

	if !found {
		return originalContent
	}

	return strings.Join(finalContent, "\n")
}

func (l *LLMRepairAction) Call() (string, bool, string, error) {
	currentFile := l.Input.TestFileName
	fileContent := l.Input.TestFileContent

	fileContent = shortenAndAddLineNo(fileContent, l.Input.TestMethodName)
	data := &promptbuilder.InputData{
		Language:         "Java",
		LanguageVersion:  java.BuildLanguageVersion(l.Input.LanguageVersion),
		SrcFileContent:   fmt.Sprintf("```java\n%s\n```", l.Input.SrcFileContent),
		TestFileContent:  fmt.Sprintf("```java\n%s\n```", fileContent),
		TestFilePath:     l.Input.TestFilePath,
		HandleTokenLimit: nil,
		TestMethodName:   l.Input.TestMethodName,
	}

	isAssertError := false

	data.LineNo = len(strings.Split(l.Input.TestFileContent, "\n"))
	if !l.Input.RunResult.IsCompileSucceeded {
		// 编译错误信息
		for _, err := range l.Input.RunResult.CompileErrors {
			if err.ErrorType == "ERROR" {
				data.ErrorLog += fmt.Sprintf("%s:%d:%d\n%s\n\n",
					err.SourceName, err.LineNumber, err.ColumnNumber, err.ErrorMessage)
			}
		}
	} else {
		// 断言错误信息
		for _, err := range l.Input.RunResult.RunResults {
			if l.Input.RunResult.IsAssertError(err.Exception) {
				isAssertError = true
			}
			data.ErrorLog += err.Exception + "\n"
			// 处理trace信息，只添加到当前文件报错行
			for _, trace := range err.StackTrace {
				data.ErrorLog += fmt.Sprintf("    at %s.%s(%s:%d)\n", trace.DeclaringClass,
					trace.MethodName,
					trace.FileName, trace.LineNumber)
				if trace.FileName == filepath.Base(currentFile) {
					break
				}
			}
		}
	}

	// 构建prompt
	var buildPrompt string
	var err error
	if isAssertError {
		buildPrompt, err = promptbuilder.BuildPrompt(promptbuilder.LLMRepairAssert, data, -1)
	} else {
		buildPrompt, err = promptbuilder.BuildPrompt(promptbuilder.LLMRepair, data, -1)
	}

	// todo: 上线后删除 debug 调试
	helper.SaveTmpFile(filepath.Join(l.Input.ErrorType.Value(), fmt.Sprintf("repair-%s-%s-llm-src.txt", l.Input.TestMethodName, l.Input.RepairId)), buildPrompt)

	if err != nil {
		return "", false, "", err
	}
	// 调用llm
	result, err := llm.Inference(buildPrompt, l.LLMModel)
	if err != nil {
		return "", false, "", err
	}

	result = gen.GetUtContentFromLLMResult(result)
	result = helper.RemoveLineNoAndEmptyLines(result)

	helper.SaveTmpFile(filepath.Join(l.Input.ErrorType.Value(), fmt.Sprintf("repair-%s-%s-llm-repaired.txt", l.Input.TestMethodName, l.Input.RepairId)), result)

	input := l.Input
	runErrorMsg := ""
	if input.RunResult != nil {
		runErrorMsg = input.RunResult.ErrorMessage()
	}
	mongoID := mongo.RepairCase(context.Background(), input.TestFilePath, input.TestFileContent, input.TestMethodName,
		buildPrompt, result, input.ErrorType.Value(), runErrorMsg, input.MongoID)

	return result, false, mongoID, nil
}

func (l *LLMRepairAction) GetName() string {
	return l.Name
}
