package gen

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
)

type LLMUtFirstGen struct {
	Input  *gen.Input
	Model  *llm.ModelConf
	Parser *java.Parser
}

func NewLLMUtFirstGen(modelConf *llm.ModelConf, parser *java.Parser, input *gen.Input) *LLMUtFirstGen {
	if modelConf == nil {
		// 默认模型
		modelConf = llm.ModelConfForInnerUtInfer(llm.GetDefaultGenModel())
	}
	return &LLMUtFirstGen{
		Model:  modelConf,
		Input:  input,
		Parser: parser,
	}
}

func (g *LLMUtFirstGen) buildPrompt() string {
	method := g.Input.GetJavaMethod()
	fileContext, testFileContext := g.Input.GetJavaFileContext()
	parser := g.Parser
	codeInfo, _ := parser.ParseMethod(fileContext, []*java.Method{method})
	codeInfo.FeatureCode.LanguageVersion = g.Input.LanguageVersion
	if codeInfo == nil {
		logger.DebugT(1, "Failed to parse method %s in file %s\n", method.Identifier,
			fileContext.Path)
		return ""
	}
	var testCodeInfo *java.TestCodeInfo
	if testFileContext != nil {
		testCodeInfo, _ = parser.ParseExistedUT(testFileContext, codeInfo, "")
	}

	if strings.Contains(codeInfo.TargetCode, "Controller") {
		for i, content := range codeInfo.FeatureCode.MockFrameworks {
			if strings.Contains(content, "MockMvc") {
				codeInfo.FeatureCode.MockFrameworks[i] = "Mockito"
			}
		}
	}
	prompt, _ := java.BuildPrompt(context.Background(), codeInfo, testCodeInfo, g.Input.Framework, llm.DCTokenLimit)
	return prompt
}

func (g *LLMUtFirstGen) Predict() (string, error) {
	if g.Input == nil {
		return "", fmt.Errorf("input is empty")
	}
	prompt := g.buildPrompt()
	result, err := llm.Inference(prompt, g.Model)
	if err != nil {
		return "", err
	}
	return GetUtContentFromLLMResult(result), nil
}

func (g *LLMUtFirstGen) GetName() string {
	return "首次生成"
}
