package gen

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"path/filepath"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
)

type LLMUtGenWithScenario struct {
	Model  *llm.ModelConf
	Input  *gen.Input
	Parser *java.Parser
}

func NewLLMUtGenWithScenario(modelConf *llm.ModelConf, parser *java.Parser, input *gen.Input) *LLMUtGenWithScenario {
	if modelConf == nil {
		modelConf = llm.ModelConfForInnerUtInfer(llm.GetDefaultRegenModel())
	}
	return &LLMUtGenWithScenario{
		Model:  modelConf,
		Input:  input,
		Parser: parser,
	}
}

func (g *LLMUtGenWithScenario) buildPrompt() (string, error) {
	method := g.Input.GetJavaMethod()
	fileContext, _ := g.Input.GetJavaFileContext()

	codeInfo, err := g.Parser.ParseMethod(fileContext, []*java.Method{method})

	if err != nil {
		return "", err
	}

	builderInput := &promptbuilder.InputData{
		Language:            "Java",
		TargetMethod:        method.Identifier,
		SrcFileContent:      fmt.Sprintf("```java\n%s\n```", buildFileContent(codeInfo)),
		TargetCodeSignature: buildFocalMethodSignature(codeInfo),
		Mocks:               g.Input.Framework,
		InnerCallCode:       buildInnerCallCode(codeInfo),
		HandleTokenLimit:    nil,
		TestMethodName:      g.Input.TestMethodName,
	}

	prompt, err := promptbuilder.BuildPrompt(promptbuilder.LLMGenWithScenario, builderInput, -1)

	if err != nil {
		return "", err
	}

	return prompt, nil
}

func (g *LLMUtGenWithScenario) Predict() (string, error) {
	prompt, err := g.buildPrompt()
	if err != nil {
		return "", err
	}

	tm := time.Now().UnixMilli()
	helper.SaveTmpFile(filepath.Join("repair", fmt.Sprintf("%s-repairgen-%d-src.txt", g.Input.TestMethodName, tm)), prompt)
	result, err := llm.Inference(prompt, g.Model)
	if err != nil {
		return "", err
	}

	helper.SaveTmpFile(filepath.Join("repair", fmt.Sprintf("%s-repairgen-%d-src.txt", g.Input.TestMethodName, tm)), result)
	return GetUtContentFromLLMResult(result), nil
}

func (g *LLMUtGenWithScenario) GetName() string {
	return "修复生成 - " + g.Input.TestMethodName
}
