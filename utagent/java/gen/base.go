package gen

import (
	"regexp"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/llm"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
)

var codeRegex = regexp.MustCompile(".*```(java|go)\n([\\s\\S]*?)\n```")

// NewUtGenAction
//
//	根据输入参数 input、模型名称 modelName 和动作名称 actionName 创建一个 UtGen 对象
//
// input: 指向 Input 结构体的指针，包含测试用例修复所需的输入数据
// modelName: 字符串类型，指定使用的模型名称
// actionName: 字符串类型，指定执行的动作名称，可选值为 FirstGen 或 CoverageRegen
//
// 返回值：返回一个实现了 UtGen 接口的对象，具体类型由 actionName 确定
func NewUtGenAction(modelConf *llm.ModelConf, parser *java.Parser, input *basegen.Input, actionName string) basegen.UtGen {
	switch actionName {
	case basegen.FirstGen:
		return NewLLMUtFirstGen(modelConf, parser, input)
	case basegen.CoverageRegen:
		return NewLLMUtReGenForCoverage(modelConf, parser, input)
	case basegen.GenWithScenario:
		return NewLLMUtGenWithScenario(modelConf, parser, input)
	default:
		panic("unknown action name")
	}
}

func GetUtContentFromLLMResult(LLMResult string) string {
	matches := codeRegex.FindAllString(LLMResult, -1)
	var newContent string
	if len(matches) > 0 {
		for _, match := range matches {
			if strings.Contains(match, "\npackage") && (strings.Contains(match, "@Test") ||
				strings.Contains(match, "testing.T")) {
				newContent = match
				break
			}

			if strings.Contains(match, "import") && (strings.Contains(match, "@Test") ||
				strings.Contains(match, "testing.T")) {
				newContent = match
			}
		}
	}

	if newContent == "" {
		newContent = LLMResult
	}

	finalContent := utils.GetCorrectCodeFromLLMResult(newContent)
	if finalContent == "" {
		return newContent
	}
	return finalContent
}
