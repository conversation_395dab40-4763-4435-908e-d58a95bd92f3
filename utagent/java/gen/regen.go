package gen

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
)

type LLMUtReGenForCoverage struct {
	Model  *llm.ModelConf
	Input  *gen.Input
	Parser *java.Parser
}

func NewLLMUtReGenForCoverage(modelConf *llm.ModelConf, parser *java.Parser, input *gen.Input) *LLMUtReGenForCoverage {
	if modelConf == nil {
		modelConf = llm.ModelConfForInnerUtInfer(llm.GetDefaultRegenModel())
	}
	return &LLMUtReGenForCoverage{
		Model:  modelConf,
		Input:  input,
		Parser: parser,
	}
}

func handleTestFileContent(testFileContext *java.FileContext, targetMethod *java.Method) string {
	// 处理已有单测用例文件
	if testFileContext == nil || testFileContext.MainClass == nil || len(testFileContext.MainClass.Methods) == 0 {
		return ""
	}

	var delUtCase = make(map[int]int)
	for _, m := range testFileContext.MainClass.Methods {
		if m.IsTestCase && !strings.Contains(m.Identifier, "test"+helper.CapitalizeFirstLetter(targetMethod.Identifier)) {
			delUtCase[m.StartLine] = m.EndLine
		}
	}
	testFileContent := ""
	var skipLineEnd = -1
	for i, lineContent := range strings.Split(testFileContext.FileContent, "\n") {
		if skipLineEnd > -1 && i <= skipLineEnd {
			continue
		}

		if strings.HasPrefix(strings.TrimSpace(lineContent), "//") {
			continue
		}

		if _, ok := delUtCase[i]; !ok {
			testFileContent += lineContent + "\n"
		} else {
			skipLineEnd = delUtCase[i]
		}
	}
	return testFileContent
}

func (g *LLMUtReGenForCoverage) buildPrompt() (string, error) {
	method := g.Input.GetJavaMethod()
	fileContext, testFileContext := g.Input.GetJavaFileContext()

	codeInfo, err := g.Parser.ParseMethod(fileContext, []*java.Method{method})

	if err != nil {
		return "", err
	}

	builderInput := &promptbuilder.InputData{
		Language:            "Java",
		TargetMethod:        method.Identifier,
		SrcFileContent:      fmt.Sprintf("```java\n%s\n```", buildFileContent(codeInfo)),
		TargetCodeSignature: buildFocalMethodSignature(codeInfo),
		Mocks:               g.Input.Framework,
		InnerCallCode:       buildInnerCallCode(codeInfo),
		HandleTokenLimit:    nil,
	}

	// 处理已有单测用例文件，从已有单测中挑选出当前被测关联的测试用例
	testFileContent := handleTestFileContent(testFileContext, method)

	if testFileContent != "" {
		builderInput.TestFileContent = fmt.Sprintf("```java\n%s\n```", testFileContent)
	}

	builderInput.CoveredLines = ""
	for _, line := range g.Input.CoverageInfo.CoveredLines {
		builderInput.CoveredLines += fmt.Sprintf("%d, ", line)
	}
	builderInput.CoveredLines = strings.TrimSuffix(builderInput.CoveredLines, ", ")

	builderInput.MissedLines = ""
	for _, line := range g.Input.CoverageInfo.MissedLines {
		builderInput.MissedLines += fmt.Sprintf("%d, ", line)
	}
	builderInput.MissedLines = strings.TrimSuffix(builderInput.MissedLines, ", ")

	builderInput.CoveredBranches = ""
	for _, branch := range g.Input.CoverageInfo.CoveredBranches {
		builderInput.CoveredBranches += fmt.Sprintf("%d, ", branch)
	}
	builderInput.CoveredBranches = strings.TrimSuffix(builderInput.CoveredBranches, ", ")

	builderInput.MissedBranches = ""
	for _, branch := range g.Input.CoverageInfo.MissedBranches {
		builderInput.MissedBranches += fmt.Sprintf("%d, ", branch)
	}
	builderInput.MissedBranches = strings.TrimSuffix(builderInput.MissedBranches, ", ")

	prompt, err := promptbuilder.BuildPrompt(promptbuilder.LLMReGen, builderInput, -1)

	if err != nil {
		return "", err
	}

	return prompt, nil
}

// Predict 函数是LLMUtReGenForCoverage结构体的方法，用于预测并生成测试用例内容
// 返回预测的测试用例内容字符串和错误信息（如果有的话）
func (g *LLMUtReGenForCoverage) Predict() (string, error) {
	prompt, err := g.buildPrompt()
	if err != nil {
		return "", err
	}

	methodName := g.Input.GetJavaMethod().Identifier

	tm := time.Now().UnixMilli()
	helper.SaveTmpFile(filepath.Join("regen", fmt.Sprintf("%s-regen-%d-src.txt", methodName, tm)), prompt)
	result, err := llm.Inference(prompt, g.Model)
	if err != nil {
		return "", err
	}
	helper.SaveTmpFile(filepath.Join("regen", fmt.Sprintf("%s-regen-%d-tgt.txt", methodName, tm)), result)
	return GetUtContentFromLLMResult(result), nil
}

func (g *LLMUtReGenForCoverage) GetName() string {
	return "补充生成"
}

func buildInnerCallCode(codeInfo *java.CodeInfo) map[string]string {
	innerCallCode := make(map[string]string, 0)
	for pkgName, code := range codeInfo.RelativeCodeCalled {
		innerCallCode[pkgName] = fmt.Sprintf("```java\n%s\n```", code)
	}
	return innerCallCode
}

func buildFileContent(codeInfo *java.CodeInfo) string {
	var fileContent string
	var startLineTag = -1
	method := codeInfo.TargetMethod[0] // 只有一个正在处理的方法
	for _, line := range strings.Split(codeInfo.TargetCode, "\n") {
		if strings.Contains(line, "// focal method") {
			fileContent += "...\n"
			fileContent += fmt.Sprintf("%d %s\n", method.StartLine, line)
			startLineTag = method.StartLine
			continue
		}
		if startLineTag > -1 && startLineTag <= method.EndLine {
			fileContent += fmt.Sprintf("%d %s\n", startLineTag+1, line)
			startLineTag++
			if startLineTag == method.EndLine+1 {
				fileContent += "...\n"
			}
		} else {
			fileContent += line + "\n"
		}
	}

	return fileContent
}

func buildFocalMethodSignature(codeInfo *java.CodeInfo) string {
	var targetCodeSignature string
	for _, signature := range codeInfo.TargetCodeSignature {
		targetCodeSignature = signature
	}

	if !strings.Contains(codeInfo.TargetCode, "@Controller") &&
		!strings.Contains(codeInfo.TargetCode, "@RestController") {
		return fmt.Sprintf("```java\n%s\n```\n", targetCodeSignature)
	}

	return ""
}
