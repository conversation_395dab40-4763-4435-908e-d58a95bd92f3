package java

import (
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/file_processor"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/gen"
	parserBase "icode.baidu.com/baidu/cov/smartUT-parser/base"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
	"icode.baidu.com/baidu/cov/smartUT-parser/merge/javamerge"
)

func (u *UTAgent) FirstGenerateForMethod() *base.FirstGeneratedInfoForMethod {
	// 首次生成，生成后把内容缓存下来
	for f, methods := range u.FileMethods {
		junitVersion := u.Environment.GetUnitFrameworkByStaticParse(f)
		for startLn, _ := range methods {
			fileContext := u.getFileContext(f)
			if fileContext == nil {
				continue
			}
			for _, m := range fileContext.MainClass.Methods {
				if m.StartLine == startLn {
					testFile := GetTestFile(f, m.Identifier)
					genInput := &basegen.Input{
						TargetMethod:    m,
						FileContext:     fileContext,
						Framework:       junitVersion,
						LanguageVersion: u.GetParams().GetLanguageVersion(),
					}
					var testFileContext *java.FileContext
					if testFile != nil {
						testFileContext, _ = u.Parser.ParseFilePath(testFile.TestFilePath)
					}
					genInput.TestFileContext = testFileContext
					llmService := gen.NewLLMUtFirstGen(u.llmModelForInfer, u.Parser, genInput)
					result, err := llmService.Predict()
					if err != nil {
						logger.Error("首次生成失败，错误信息为: %s", err)
						return nil
					}

					// 模型首次生成结果进行格式化。进行添加pkg、驼峰化方法名
					result = javamerge.FormatLLMResult(result, fileContext.Pkg)

					firstGenInfo := mergeFirstGenCode(result, fileContext, m, testFileContext)
					return firstGenInfo
				}
			}
		}
		break
	}
	return nil
}

func mergeFirstGenCode(llmResult string, srcFileContext *java.FileContext, targetMethod *java.Method,
	userTestFileContext *java.FileContext) *base.FirstGeneratedInfoForMethod {
	fileParser, err := java.NewFileParserWithContent(llmResult)
	if err != nil {
		logger.Error("解析首次生成单测文件失败：%s", err)
		return nil
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse(&parserBase.CodeFormatOptions{
		MethodNameToCamelCase: true,
	})
	if llmTestFileContext == nil || llmTestFileContext.MainClass == nil {
		return nil
	}
	var firstGenInfo = &base.FirstGeneratedInfoForMethod{
		OriginLLMResult:  llmResult,
		TargetMethodName: targetMethod.Identifier,
	}

	var methodsNeedMerge []string
	for _, m := range llmTestFileContext.MainClass.Methods {
		if m.IsTestCase {
			methodsNeedMerge = append(methodsNeedMerge, m.Identifier)
		}
	}
	firstGenInfo.GenCaseNameList = methodsNeedMerge

	if userTestFileContext != nil {
		mergeResult := file_processor.Merge(targetMethod.Identifier, srcFileContext.Pkg, llmTestFileContext,
			methodsNeedMerge, userTestFileContext.Path, userTestFileContext.FileContent)
		firstGenInfo.MergedUtFileContent = mergeResult.MergedResult
		firstGenInfo.TestFilePath = userTestFileContext.Path
	} else {
		firstGenInfo.MergedUtFileContent = llmResult
		// 首次生成时，没有用户单测文件，则自动生成测试文件
		firstGenInfo.TestFilePath = GetTestFilePathByClsName(srcFileContext.Path, llmTestFileContext.MainClass.Identifier)
	}

	return firstGenInfo
}
