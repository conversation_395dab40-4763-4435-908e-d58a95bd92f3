package utagent

import (
	"context"
	"runtime/debug"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

// 模型推理接口
func (generator *UTGenerator) inferenceInBackground(ctx context.Context) {
	defer generator.wg.Done()

	timer := helper.NewTimer()
	l := logger.NewProcessLogger()
	defer l.Close()

	for {
		select {
		case msg := <-generator.QueueForLLMCall: // 调用模型推理接口
			var underTest models.UnderTestInfo
			logger.Info("【模型】队列长度%v", len(generator.QueueForLLMCall))
			func() {
				// 模型推理异常时的处理
				defer func() {
					if r := recover(); r != nil {
						logger.ErrorT(1, "【模型推理】处理消息时发生异常: %v\n%s", r, debug.Stack())
						generator.handleReferenceError(underTest)
					}
				}()

				if msg == nil {
					logger.ErrorT(1, "【模型推理】收到空消息")
					return
				}

				// 开始处理模型推理请求
				timer.Start()
				underTest = msg.(models.UnderTestInfo)
				//if generator.checkCoverageAndExit(underTest) {
				//	return
				//}
				round := underTest.Process.GetIterNum(underTest.FuncID, "")
				generator.UTAgent.GetMemory().FileProcessStart(underTest.FilePath)
				generator.UTAgent.GetFileTimer(underTest.FilePath).Start()
				// 调用模型推理接口，生成UT内容。假定内容为utContent
				l.InfoT(2, "【模型推理 Round#%d - %s】 %s",
					round,
					underTest.LLMGenAction.GetName(),
					underTest.ID)

				var utContent, mongoID string
				var err error

				tmpGeneratedInfo := generator.UTAgent.GetMemory().GetFirstGeneratedInfo()
				if tmpGeneratedInfo != nil && tmpGeneratedInfo.TargetMethodName == underTest.FuncName {
					l.InfoT(2, "【模型推理 Round#%d - %s】 IDE场景下的单方法生成，已经首次生成完毕，无需再次推理",
						round, underTest.LLMGenAction.GetName())
					utContent = tmpGeneratedInfo.OriginLLMResult
					mongoID = tmpGeneratedInfo.MongoId
				} else {
					utContent, err = underTest.LLMGenAction.Predict()
				}

				if err != nil {
					l.EndT(2, "【模型推理 Round#%d - %s】 %s 发生错误：%s",
						round,
						underTest.LLMGenAction.GetName(),
						underTest.ID,
						err.Error())
					generator.handleReferenceError(underTest)
					return
				}

				if utContent == "" {
					l.EndT(2, "【模型推理 Round#%d - %s】 %s 发生异常：未从模型返回中提取到有效的java代码",
						round,
						underTest.LLMGenAction.GetName(),
						underTest.ID)
					generator.handleReferenceError(underTest)
					return
				}

				// 将生成的UT内容放入验证队列中进行验证
				caseInfo := models.CaseInfo{
					TestFileContent: utContent,
					MongoID:         mongoID,
					UnderTest:       underTest,
				}
				timer.Pause()
				generator.UTAgent.GetFileTimer(underTest.FilePath).Pause()
				l.EndT(2, "【模型推理 Round#%d - %s】 %s, 推理完成，耗时 %s",
					round,
					underTest.LLMGenAction.GetName(),
					underTest.ID,
					timer.ElapsedSecond())
				func() {
					logger.Info("进入验证: %s", underTest.FuncID)
					generator.QueueForValidation <- caseInfo
				}()
			}()
		case <-ctx.Done():
			logger.Info("【模型推理】退出生成模块，耗时 %s", helper.FormatMS(timer.Duration()))
			return
		default:
			time.Sleep(1 * time.Millisecond)
		}
	}
}

func (generator *UTGenerator) handleReferenceError(underTest models.UnderTestInfo) {
	// 上个流程结束，流程减一
	generator.cache.Dec(underTest.FuncID)

	// 准备进入下一个迭代，先判断是否迭代已经完成
	if underTest.Process.IsInteractionFinished(underTest.FuncID) {
		// 迭代完成，删除缓冲记录，并判定最终是否退出
		generator.deleteFromCache(underTest.FilePath, underTest, models.ReasonMaxIterationReached)
		return
	}

	// 进入下一个迭代。 迭代次数增加
	if generator.UTAgent.CallLLMGen(generator.QueueForLLMCall, underTest, "") {
		// 放入模型推理队列，流程加一
		logger.Info("推理出错进入下一次迭代: %s", underTest.FuncID)
		generator.cache.Inc(underTest.FuncID)
	} else {
		// 唯一失败原因为覆盖率达标，因此终止该被测方法的测试生成
		underTest.Process.EndProcess()
		generator.deleteFromCache(underTest.FilePath, underTest, models.ReasonCoverageReached)
	}
}
