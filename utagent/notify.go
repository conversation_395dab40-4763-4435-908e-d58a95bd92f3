package utagent

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"os"

	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools/statistics"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

func (generator *UTGenerator) notifyTaskStatus(summary *models.TaskSummary, status string, errMsg string) {
	if generator.ideNotifier != nil {
		generator.ideNotifier.UpdateSummary(summary, status, errMsg)
	}
}

func (generator *UTGenerator) notifyEnvResult(status bool, err error) {
	if generator.ideNotifier == nil {
		return
	}

	var errMsg string
	if err != nil {
		errMsg = err.Error()
	}

	generator.ideNotifier.UpdateEnvCheckStatus(status, errMsg)
}

func (generator *UTGenerator) notifyFrameworkResult(check *models.FrameworkCheck) {
	if generator.ideNotifier == nil {
		return
	}

	generator.ideNotifier.GetProcessData().FrameworkCheck = check
	generator.ideNotifier.Notify()
}

func (generator *UTGenerator) NotifyTaskExpectMsg(msg string) {
	if generator.ideNotifier == nil {
		return
	}
	// 单方法生成，被测方法只有一个时，给出首次生成,
	if generator.param.GetType() == constant.GenerateForMethod && generator.param.GetSrcMethodsCount() == 1 {
		generator.ideNotifier.GetProcessData().FirstGenerate = new(models.FirstGenerate)
		generator.ideNotifier.GetProcessData().FirstGenerate.State = constant.RUNNING
		generator.ideNotifier.GetProcessData().LoadingMsg = constant.LoadingMsgForGen
	}
	// diff生成，文件扫描状态
	// 单方法生成，被测方法有多个时，在srcFileScan字段给出被测方法名列表
	if generator.param.GetType() == constant.GenerateForDiff || generator.param.GetType() == constant.GenerateForGen ||
		(generator.param.GetType() == constant.GenerateForMethod && generator.param.GetSrcMethodsCount() > 1) {
		generator.ideNotifier.GetProcessData().SrcFileScan = new(models.SrcFileScanResult)
		generator.ideNotifier.GetProcessData().SrcFileScan.State = constant.RUNNING
		// 根据任务类型，给出下拉列表的表名
		switch generator.param.GetType() {
		case constant.GenerateForDiff:
			generator.ideNotifier.GetProcessData().SrcFileScan.TableHeader = constant.TargetScanTableHeaderForDiff
		case constant.GenerateForGen:
			generator.ideNotifier.GetProcessData().SrcFileScan.TableHeader = constant.TargetScanTableHeaderForGen
		case constant.GenerateForMethod:
			generator.ideNotifier.GetProcessData().SrcFileScan.TableHeader = constant.TargetScanTableHeaderForMethod
		}
		generator.ideNotifier.GetProcessData().LoadingMsg = constant.LoadingMsgForParse
	}
	generator.ideNotifier.UpdateTaskExpectDesc(msg)
}

func (generator *UTGenerator) NotifySrcFileScanState(values []string) {
	// diff和gen展示目标被测文件名
	// method展示目标被测方法名
	if generator.ideNotifier == nil || (generator.param.GetType() == constant.GenerateForMethod && generator.param.GetSrcMethodsCount() == 1) {
		return
	}
	generator.ideNotifier.GetProcessData().SrcFileScan.State = constant.SUCCEED
	generator.ideNotifier.GetProcessData().SrcFileScan.SrcFilesName = values
	if len(values) > 0 {
		switch generator.param.GetType() {
		case constant.GenerateForDiff:
			generator.ideNotifier.GetProcessData().SrcFileScan.Summary = fmt.Sprintf(constant.TargetScanSummaryForDiff, len(values))
		case constant.GenerateForGen:
			generator.ideNotifier.GetProcessData().SrcFileScan.Summary = fmt.Sprintf(constant.TargetScanSummaryForGen, len(values))
		case constant.GenerateForMethod:
			generator.ideNotifier.GetProcessData().SrcFileScan.Summary = fmt.Sprintf(constant.TargetScanSummaryForMethod, len(values))
		}
	} else {
		switch generator.param.GetType() {
		case constant.GenerateForDiff:
			generator.ideNotifier.GetProcessData().SrcFileScan.Summary = constant.NoSrcFilesFoundForDiff
		case constant.GenerateForGen:
		case constant.GenerateForMethod:
			generator.ideNotifier.GetProcessData().SrcFileScan.Summary = constant.NoSrcFilesFoundForGen
		}
	}
	generator.ideNotifier.GetProcessData().LoadingMsg = constant.LoadingMsgForGen
	generator.ideNotifier.Notify()
}

func (generator *UTGenerator) notifyEnvCheckStart() {
	if generator.ideNotifier == nil {
		return
	}
	generator.ideNotifier.EnvCheckStart()
}

func (generator *UTGenerator) notifyLoadingMsg(msg string) {
	if generator.ideNotifier == nil {
		return
	}
	generator.ideNotifier.GetProcessData().LoadingMsg = msg
	generator.ideNotifier.Notify()
}

// notifyFirstGenerate 函数用于通知首次生成单元测试的结果

// 功能说明：
// 当需要通知首次生成单元测试的结果时，该函数会被调用。
// 仅IDE场景下的单方法生成需要
func (generator *UTGenerator) notifyFirstGenerate() error {
	// 首次生成:只有一个被测方法时给firstGenerate；如果有多个被测方法，不给firstGenerate，给出被测方法名列表，用srcFileScan字段给出
	if generator.ideNotifier == nil || generator.param.GetType() != constant.GenerateForMethod || generator.param.GetSrcMethodsCount() > 1 {
		return nil
	}

	firstGenerateInfo := generator.UTAgent.FirstGenerateForMethod()
	if firstGenerateInfo == nil {
		return nil
	}
	// 如果首次生成有误，直接结束任务
	if firstGenerateInfo.FirstGenerateError != nil {
		generator.ideNotifier.GetProcessData().FirstGenerate.State = constant.FAILED
		generator.notifyTaskStatus(nil, constant.FAILED, firstGenerateInfo.FirstGenerateError.Error())
		return firstGenerateInfo.FirstGenerateError
	}
	// 将首次生成的信息保存到内存中，以便后续使用
	generator.UTAgent.GetMemory().SetFirstGeneratedInfo(firstGenerateInfo)

	generator.ideNotifier.GetProcessData().FirstGenerate.State = constant.SUCCEED
	generator.ideNotifier.GetProcessData().FirstGenerate.CaseName = firstGenerateInfo.GenCaseNameList
	generator.ideNotifier.GetProcessData().FirstGenerate.TestFileContent = firstGenerateInfo.MergedUtFileContent
	generator.ideNotifier.GetProcessData().FirstGenerate.TestFilePath = firstGenerateInfo.TestFilePath
	// summary 总结
	generator.ideNotifier.GetProcessData().FirstGenerate.Summary = constant.FirstGenerateForMethodSummary
	// loadingMsg 更新
	generator.ideNotifier.GetProcessData().LoadingMsg = constant.LoadingMsgForOptimize
	generator.ideNotifier.Notify()
	return nil
}

func (generator *UTGenerator) notifyUtResult(underTest models.UnderTestInfo, testFilePath, testFileContent string) {
	if generator.ideNotifier == nil {
		return
	}
	u := generator.UTAgent
	var cov = new(coverage.CoverageInfo)
	if _, ok := u.GetMemory().Diffs[underTest.ClassFullName]; ok {
		cov = u.GetFileCoverageByDiff(underTest.ClassFullName)
	} else {
		cov = u.GetFileCoverage(underTest.ClassFullName)
	}

	if generator.param.GetTrigger() == constant.ICODETrigger {
		generator.ideNotifier.UpdateTaskProcessCoverage(u.GetMemory())
		// ICODE场景不需要通知内容
		testFileContent = ""
	}

	generator.ideNotifier.NotifyUtResult(underTest, testFilePath, testFileContent, u.GetMemory(), cov)
}

func (generator *UTGenerator) notifyUtResultSucceed(underTest models.UnderTestInfo, srcFilePath string) {
	if generator.ideNotifier == nil {
		return
	}

	if generator.ideNotifier.Data.Params.TaskSummary == nil {
		generator.ideNotifier.Data.Params.TaskSummary = new(models.TaskSummary)
	}

	summary := &models.TaskSummary{}

	for _, p := range generator.UTAgent.GetMemory().GetAllFileProcess() {
		summary.SrcMethodCount += p.TotalFunc
		summary.ProcessedSrcMethodCount += p.ExecutedFunc
		summary.SucceedSrcMethodCount += p.SucceedFunc
		summary.GeneratedTestMethodCount += p.ValidCaseNum
	}

	generator.ideNotifier.Data.Params.TaskSummary = summary
	results := generator.ideNotifier.Data.Params.UtFileResult
	for index, r := range results {
		if r.SrcFilePath == srcFilePath {
			r.State = constant.SUCCEED
			if len(r.Bugs) > 0 {
				// 获取仅验证完全成功的用例内容
				results[index].TestFileContentOnlySuccess = generator.UTAgent.GetTestFileContentOnlySuccess(r, underTest)
			} else {
				results[index].TestFileContentOnlySuccess = r.TestFileContent
			}
			break
		}
	}
	generator.ideNotifier.Notify()
}

// 文件为粒度进行上报
func (generator *UTGenerator) reportGenerateForFile(srcFilePath string) {
	memory := generator.UTAgent.GetMemory()
	// 文件处理结束 && 没上报过 =》以文件为粒度上报生成
	if process := memory.FileProcess[srcFilePath]; process != nil && process.Status == 2 && !process.UploadGenerate {
		// 为每个单测文件上报
		srcContent := generator.getSrcFileTargetMethods(srcFilePath)
		for utAbsPath, _ := range memory.GetFileProcess(srcFilePath).TestFilePath {
			utContent := ""
			if utFile, e := os.ReadFile(utAbsPath); e == nil {
				utContent = string(utFile)
			}

			report := &statistics.ReportGenerationParam{
				CodeRepo:   generator.CodeRepoInfo,
				Trigger:    generator.param.GetTrigger(),
				SrcContent: srcContent,
				UtAbsPath:  utAbsPath,
				UtContent:  utContent,
				CaseNum:    generator.UTAgent.GetMemory().GetFileProcess(srcFilePath).ValidCaseNum,
			}
			err := report.ReportGenerationForFile()
			if err != nil {
				logger.Error("上报生成失败，请联系Comate为您解决问题：", err)
			}
		}
		process.UploadGenerate = true
	}
}
