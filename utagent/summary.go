package utagent

import (
	"encoding/json"
	"fmt"
	"github.com/toolkits/file"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"os"
	"path/filepath"
)

func (generator *UTGenerator) summary() *models.TaskSummary {
	// 汇总信息，生成报告
	logger.Info("---------  结束 ---------")
	logger.Info("=> 文件详情：")

	if generator.param.GetType() == constant.GenerateForMethod {
		summary := generator.summaryForMethod()
		generator.saveResultFile(summary)
		return summary
	}

	agent := generator.UTAgent
	memory := agent.GetMemory()

	var covered, missed int
	var originCovered, originMissed int

	var incCovered, incMissed, incOriginCovered, incOriginMissed int

	var genCaseNum int
	var summary = new(models.TaskSummary)
	allFileMethods := generator.param.GetSrcMethods()
	for f := range allFileMethods {
		f = filepath.ToSlash(f)
		cls := agent.GetFullClsNameByFilePath(f)
		originIncCov := memory.GetIncFileCov(cls, memory.Diffs[cls], constant.OriginCoverage)
		currentIncCov := memory.GetIncFileCov(cls, memory.Diffs[cls], constant.CurrentCoverage)

		originCov := memory.GetTotalFileCov(cls, constant.OriginCoverage)
		cov := memory.GetTotalFileCov(cls, constant.CurrentCoverage)

		duration := memory.GetFileDurations(f)
		logger.InfoT(1, "文件：%s", f)

		fileProcess := memory.GetFileProcess(f)
		if fileProcess != nil && fileProcess.TotalFunc == 0 {
			// 解析后无待测方法，不打印状态
		} else if fileProcess != nil && fileProcess.TotalFunc > 0 {
			summary.SrcMethodCount += fileProcess.TotalFunc
			summary.ProcessedSrcMethodCount += fileProcess.ExecutedFunc
			summary.SucceedSrcMethodCount += fileProcess.SucceedFunc
			status := memory.GetFileProcessStatus(f)
			if fileProcess.Status == 2 && cov.IsCoverageReached(float64(generator.DesiredCoverage)) {
				status = "已达标"
			}
			logger.InfoT(2, "=> 【%s】覆盖率：%0.2f%% -> %0.2f%%, 耗时：%0.02fs，共 %d 个待测方法，已完成 %d/%d，共生成 %d 个用例",
				status,
				originCov.GetCoveragePercent(),
				cov.GetCoveragePercent(),
				float64(duration)/1000,
				fileProcess.TotalFunc,
				fileProcess.ExecutedFunc,
				fileProcess.TotalFunc,
				fileProcess.ValidCaseNum)

			genCaseNum += fileProcess.ValidCaseNum
		} else {
			logger.InfoT(2, "=> 【未开始】覆盖率：%0.2f%%，耗时：-- ",
				cov.GetCoveragePercent())
		}

		if cov != nil {
			covered += len(cov.CoveredLines)
			missed += len(cov.MissedLines)
		}

		if originCov != nil {
			originCovered += len(originCov.CoveredLines)
			originMissed += len(originCov.MissedLines)
		}

		if originIncCov != nil {
			incOriginCovered += len(originIncCov.CoveredLines)
			incOriginMissed += len(originIncCov.MissedLines)
		}

		if currentIncCov != nil {
			incCovered += len(currentIncCov.CoveredLines)
			incMissed += len(currentIncCov.MissedLines)
		}
	}

	// oldCoverage的分母替换成当前覆盖率的分母
	oldCoverage := helper.Divide(originCovered, covered+missed, 4) * 100
	newCoverage := helper.Divide(covered, covered+missed, 4) * 100

	// 增量覆盖率
	oldIncCoverage := helper.Divide(incOriginCovered, incOriginCovered+incOriginMissed, 4) * 100
	currentIncCoverage := helper.Divide(incCovered, incCovered+incMissed, 4) * 100

	logger.Info("=> 结果汇总：")
	logger.Info("【文件覆盖率提升】%0.2f%%（%d/%d）-> %0.2f%%（%d/%d）",
		oldCoverage, originCovered, covered+missed,
		newCoverage, covered, covered+missed)

	if generator.param.GetType() == constant.GenerateForDiff {
		logger.Info("【增量覆盖率提升】%0.2f%%（%d/%d）-> %0.2f%%（%d/%d）",
			oldIncCoverage, incOriginCovered, incOriginCovered+incOriginMissed,
			currentIncCoverage, incCovered, incCovered+incMissed)
	}

	logger.Info("【生成用例信息】 被测文件总数：%d, 被测方法总数：%d, 生成用例总数:%d", len(allFileMethods),
		summary.SrcMethodCount, genCaseNum)
	logger.Info("【总耗时】 %s", helper.FormatMS(memory.TotalTimer.Elapsed()))

	logger.Info("=> 成功率汇总：")
	logger.Info("【生成】正确率：%v/%v = %0.2f%%，有效率：%v/%v = %0.2f%%，断言失败占比：%v/%v = %0.2f%%",
		memory.Summary.PassCaseNum.Load(), memory.Summary.GeneratedCaseNum.Load(),
		helper.Divide(int(memory.Summary.PassCaseNum.Load()), int(memory.Summary.GeneratedCaseNum.Load()), 4)*100,
		memory.Summary.ValidCaseNum.Load(), memory.Summary.GeneratedCaseNum.Load(),
		helper.Divide(int(memory.Summary.ValidCaseNum.Load()), int(memory.Summary.GeneratedCaseNum.Load()), 4)*100,
		memory.Summary.AssertFailedNum.Load(),
		memory.Summary.GeneratedCaseNum.Load()-memory.Summary.PassCaseNum.Load(),
		helper.Divide(int(memory.Summary.AssertFailedNum.Load()),
			int(memory.Summary.GeneratedCaseNum.Load()-memory.Summary.PassCaseNum.Load()), 4)*100)
	logger.Info("【修复】正确率：%v/%v = %0.2f%%，有效率：%v/%v = %0.2f%%", memory.Summary.RepairSucceedCaseNum.Load(),
		memory.Summary.RepairCaseNum.Load(),
		helper.Divide(int(memory.Summary.RepairSucceedCaseNum.Load()), int(memory.Summary.RepairCaseNum.Load()), 4)*100,
		memory.Summary.RepairValidCaseNum.Load(), memory.Summary.RepairCaseNum.Load(),
		helper.Divide(int(memory.Summary.RepairValidCaseNum.Load()), int(memory.Summary.RepairCaseNum.Load()), 4)*100)

	logger.Info("【修复方式详情】规则修复：%v/%v = %0.2f%%，模型修复：%v/%v = %0.2f%%", memory.Summary.RuleRepairSucceedCaseNum.Load(),
		memory.Summary.RuleRepairedCaseNum.Load(),
		helper.Divide(int(memory.Summary.RuleRepairSucceedCaseNum.Load()), int(memory.Summary.RuleRepairedCaseNum.Load()), 4)*100,
		memory.Summary.ModelRepairSucceedCaseNum.Load(), memory.Summary.ModelRepairedCaseNum.Load(),
		helper.Divide(int(memory.Summary.ModelRepairSucceedCaseNum.Load()), int(memory.Summary.ModelRepairedCaseNum.Load()), 4)*100)

	summary.GeneratedTestMethodCount = genCaseNum

	// 全量文件覆盖率
	summary.Coverage = fmt.Sprintf("%0.2f%%", newCoverage)
	summary.OldCoverage = fmt.Sprintf("%0.2f%%", oldCoverage)
	if newCoverage-oldCoverage > 0 {
		summary.IncreaseRate = fmt.Sprintf("%0.2f%%", newCoverage-oldCoverage)
	} else {
		summary.IncreaseRate = constant.PercentZero
	}

	if generator.param.GetType() == constant.GenerateForDiff {
		// 增量覆盖率
		summary.OldIncCoverage = fmt.Sprintf("%0.2f%%", oldIncCoverage)
		summary.CurrentIncCoverage = fmt.Sprintf("%0.2f%%", currentIncCoverage)
		if currentIncCoverage-oldIncCoverage > 0 {
			summary.IncCoverageIncreaseRate = fmt.Sprintf("%0.2f%%", currentIncCoverage-oldIncCoverage)
		} else {
			summary.IncCoverageIncreaseRate = constant.PercentZero
		}
	}

	summary.TimeElapsed = memory.TotalTimer.Elapsed()
	generator.saveResultFile(summary)

	return summary

}

func (generator *UTGenerator) summaryForMethod() *models.TaskSummary {
	// 单方法生成汇总信息
	var summary = new(models.TaskSummary)
	var originCov, newCov *coverage.CoverageInfo
	var genCaseNum int
	memory := generator.UTAgent.GetMemory()
	allFileMethods := generator.param.GetSrcMethods()
	for f, method := range allFileMethods {
		logger.InfoT(1, "文件：%s", f)
		f = filepath.ToSlash(f)
		cls := generator.UTAgent.GetFullClsNameByFilePath(f)
		// 获取方法覆盖率
		for startLn, endLn := range method {
			originCov = memory.GetOriginalRangeCoverage(cls, startLn+1, endLn)
			newCov = memory.GetRangeCoverage(cls, startLn+1, endLn)
			break
		}

		fileProcess := memory.GetFileProcess(f)
		duration := memory.GetFileDurations(f)
		if fileProcess != nil && fileProcess.TotalFunc == 0 {
			// 解析后无待测方法，不打印状态
			continue
		} else if fileProcess != nil && fileProcess.TotalFunc > 0 {
			summary.SrcMethodCount += fileProcess.TotalFunc
			summary.ProcessedSrcMethodCount += fileProcess.ExecutedFunc
			summary.SucceedSrcMethodCount += fileProcess.SucceedFunc
			status := memory.GetFileProcessStatus(f)
			logger.InfoT(2, "=> 【%s】方法覆盖率：%0.2f%% -> %0.2f%%, 耗时：%0.02fs，共 %d 个待测方法，已完成 %d/%d，共生成 %d 个用例",
				status,
				originCov.GetCoveragePercent(),
				newCov.GetCoveragePercent(),
				float64(duration)/1000,
				fileProcess.TotalFunc,
				fileProcess.ExecutedFunc,
				fileProcess.TotalFunc,
				fileProcess.ValidCaseNum)

			genCaseNum += fileProcess.ValidCaseNum
		} else {
			logger.InfoT(2, "=> 【未开始】覆盖率：%0.2f%%，耗时：-- ",
				newCov.GetCoveragePercent())
		}

	}

	summary.GeneratedTestMethodCount = genCaseNum
	summary.Coverage = fmt.Sprintf("%0.2f%%", newCov.GetCoveragePercent())
	summary.OldCoverage = fmt.Sprintf("%0.2f%%", originCov.GetCoveragePercent())
	if newCov.GetCoverage()-originCov.GetCoverage() > 0 {
		summary.IncreaseRate = fmt.Sprintf("%0.2f%%", newCov.GetCoverage()-originCov.GetCoverage())
	} else {
		summary.IncreaseRate = constant.PercentZero
	}

	return summary
}

func (generator *UTGenerator) saveResultFile(summary *models.TaskSummary) {
	// 评测保存结果所需
	if generator.param.GetResultPath() == "" {
		return
	}
	resultPath := generator.param.GetResultPath()

	if !file.IsExist(file.Dir(resultPath)) {
		os.MkdirAll(file.Dir(resultPath), os.ModePerm)
	}

	f, err := os.Create(resultPath)
	if err != nil {
		logger.Warn("存储summary文件发生错误，错误原因：%s", err.Error())
		return
	}

	defer f.Close()
	data := map[string]interface{}{
		"result": summary,
		"detail": generator.UTAgent.GetMemory().Summary.ToMap(),
	}
	d, _ := json.Marshal(data)
	f.WriteString(string(d))

	logger.Info("UTAgent 执行结束，结果已保存至 %s", resultPath)
}
