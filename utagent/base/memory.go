package base

import (
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"sync"
	"sync/atomic"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type Memory struct {
	Coverages          map[string]*CoverageRecord
	FileProcess        map[string]*Process // 文件进度，所有方法都出队，代表文件处理结束
	TotalTimer         *helper.Timer
	FileProcessInQueue float64
	Summary            Summary
	TmpValidTestFile   map[string]*models.TestFile

	Diffs             map[string][]int // 记录代码增量信息
	GeneratedUnitTest map[string][]models.GenUnitTestCase
	FirstGenerated    *FirstGeneratedInfoForMethod // 记录第一次生成单测信息，仅单方法生成时需要

	mu sync.Mutex
}

type MethodProcess struct {
	CurIteration int
}

type CoverageRecord struct {
	OriginalCoverage *coverage.CoverageInfo
	NewCoverage      *coverage.CoverageInfo
}

type FirstGeneratedInfoForMethod struct {
	OriginLLMResult     string
	MongoId             string
	TargetMethodName    string
	MergedUtFileContent string
	TestFilePath        string
	GenCaseNameList     []string
	FirstGenerateError  error // 记录首次生成的错误，如：首次生成截断，无法解析模型生成的单测文件
}

type Process struct {
	Status         int
	TotalFunc      int
	ExecutedFunc   int
	SucceedFunc    int
	ValidCaseNum   int
	timer          *helper.Timer
	UploadGenerate bool            // 被测文件是否上报生成
	TestFilePath   map[string]bool // 被测文件对应的所有单测文件路径
}

type Summary struct {
	GeneratedCaseNum atomic.Int64
	ValidCaseNum     atomic.Int64 // 正确且对覆盖率有提升
	PassCaseNum      atomic.Int64 // 正确
	AssertFailedNum  atomic.Int64 // 断言失败

	RepairCaseNum        atomic.Int64
	RepairSucceedCaseNum atomic.Int64
	RepairValidCaseNum   atomic.Int64

	RuleRepairedCaseNum      atomic.Int64
	RuleRepairSucceedCaseNum atomic.Int64

	ModelRepairedCaseNum      atomic.Int64
	ModelRepairSucceedCaseNum atomic.Int64
}

func (s *Summary) ToMap() map[string]int64 {
	temp := map[string]int64{
		"generatedCaseNum":          s.GeneratedCaseNum.Load(), // 将 atomic.Int64 转换为 int64
		"validCaseNum":              s.ValidCaseNum.Load(),
		"passCaseNum":               s.PassCaseNum.Load(),
		"assertFailedNum":           s.AssertFailedNum.Load(),
		"repairCaseNum":             s.RepairCaseNum.Load(),
		"repairSucceedCaseNum":      s.RepairSucceedCaseNum.Load(),
		"repairValidCaseNum":        s.RepairValidCaseNum.Load(),
		"ruleRepairedCaseNum":       s.RuleRepairedCaseNum.Load(),
		"ruleRepairSucceedCaseNum":  s.RuleRepairSucceedCaseNum.Load(),
		"modelRepairedCaseNum":      s.ModelRepairedCaseNum.Load(),
		"modelRepairSucceedCaseNum": s.ModelRepairSucceedCaseNum.Load(),
	}
	return temp
}

func (s *Summary) UpdateRepair(repairTool string, repairSucceed bool) {
	if repairTool == "SYMBOL_REPAIR" {
		if repairSucceed {
			s.RuleRepairSucceedCaseNum.Add(1)
		} else {
			s.RuleRepairedCaseNum.Add(1)
		}
	} else if repairTool == "LLM_REPAIR" {
		if repairSucceed {
			s.ModelRepairSucceedCaseNum.Add(1)
		} else {
			s.ModelRepairedCaseNum.Add(1)
		}
	}
}

func NewMemory() *Memory {
	return &Memory{
		Coverages:        map[string]*CoverageRecord{},
		FileProcess:      map[string]*Process{},
		TotalTimer:       helper.NewTimer(),
		TmpValidTestFile: map[string]*models.TestFile{},
		Diffs:            map[string][]int{},
	}
}

func (m *Memory) GetTmpValidTestFile(filePath string) *models.TestFile {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.TmpValidTestFile[filePath]
}

func (m *Memory) UpdateTmpValidTestFile(filePath string, tmp *models.TestFile) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.TmpValidTestFile == nil {
		m.TmpValidTestFile = map[string]*models.TestFile{}
	}
	m.TmpValidTestFile[filePath] = tmp
}

func (m *Memory) AddGeneratedValidCaseDetail(testFilePath string, caseInfo models.GenUnitTestCase) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.GeneratedUnitTest == nil {
		m.GeneratedUnitTest = make(map[string][]models.GenUnitTestCase)
	}
	m.GeneratedUnitTest[testFilePath] = append(m.GeneratedUnitTest[testFilePath], caseInfo)
}

func (m *Memory) FileTimerStart(filePath string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.FileProcess[filePath] == nil {
		m.FileProcess[filePath] = &Process{
			Status:       0,
			TotalFunc:    0,
			ExecutedFunc: 0,
			timer:        helper.NewTimer(),
		}
	}
	m.FileProcess[filePath].timer.Start()
}

func (m *Memory) FileTimerPause(filePath string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.FileProcess[filePath].timer.Pause()
}

func (m *Memory) GetFileProcess(filePath string) *Process {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.FileProcess[filePath]
}

func (m *Memory) GetAllFileProcess() map[string]*Process {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.FileProcess
}

func (m *Memory) RecordValidCaseNum(filePath string, validCaseNum int) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.FileProcess[filePath].ValidCaseNum += validCaseNum
	m.FileProcess[filePath].SucceedFunc += 1
}

func (m *Memory) SaveTestFilePath(filePath string, testFilePath string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.FileProcess[filePath].TestFilePath == nil {
		m.FileProcess[filePath].TestFilePath = make(map[string]bool)
	}
	m.FileProcess[filePath].TestFilePath[testFilePath] = false
}

func (m *Memory) FileProcessUpdate(filePath string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.FileProcess[filePath].ExecutedFunc++
	if m.FileProcess[filePath].ExecutedFunc == m.FileProcess[filePath].TotalFunc {
		m.FileProcess[filePath].Status = 2
	}
}

func (m *Memory) FileProcessStart(filePath string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.FileProcess[filePath].Status = 1
}

func (m *Memory) GetFileCoverage(cls string) *coverage.CoverageInfo {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.Coverages[cls] == nil {
		return &coverage.CoverageInfo{}
	}
	return m.Coverages[cls].NewCoverage
}

// GetTotalFileCov 获取指定类别的总覆盖率信息
// 参数：
//
//	cls: 类别名称
//	covType: 覆盖率类型，可以是"origin"或"current"
//
// 返回值：
//
//	返回指定类别的总覆盖率信息
func (m *Memory) GetTotalFileCov(cls string, covType string) *coverage.CoverageInfo {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.Coverages[cls] == nil {
		return &coverage.CoverageInfo{}
	}

	if covType == constant.OriginCoverage {
		return m.Coverages[cls].OriginalCoverage
	}

	return m.Coverages[cls].NewCoverage
}

// GetIncFileCov 获取增量文件覆盖率
// 参数：
// cls: 类名
// diffs: 增量行号
// covType: 覆盖类型
// 返回值：
// 增量文件覆盖率信息
func (m *Memory) GetIncFileCov(cls string, diffs []int, covType string) *coverage.CoverageInfo {
	m.mu.Lock()
	defer m.mu.Unlock()

	cov := new(coverage.CoverageInfo)

	if len(diffs) == 0 { // 无增量
		return &coverage.CoverageInfo{}
	}

	if m.Coverages[cls] == nil { // 无覆盖率
		return &coverage.CoverageInfo{
			Coverage:     0,
			MissedLines:  diffs,
			CoveredLines: []int{},
		}
	}

	var coverage *coverage.CoverageInfo
	if covType == constant.OriginCoverage {
		coverage = m.Coverages[cls].OriginalCoverage
	} else {
		coverage = m.Coverages[cls].NewCoverage
	}

	if coverage == nil {
		if m.Coverages[cls].NewCoverage != nil {
			cov.MissedLines = append(m.Coverages[cls].NewCoverage.MissedLines, m.Coverages[cls].NewCoverage.CoveredLines...)
			cov.MissedLines = helper.Intersect(cov.MissedLines, diffs)
		} else {
			cov.MissedLines = diffs
		}
	} else {
		cov.MissedLines = helper.Intersect(coverage.MissedLines, diffs)
		cov.CoveredLines = helper.Intersect(coverage.CoveredLines, diffs)
	}

	cov.Coverage = cov.GetCoveragePercent()
	return cov

}

func (m *Memory) GetFileOriginCoverage(cls string) *coverage.CoverageInfo {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.Coverages[cls] == nil {
		return &coverage.CoverageInfo{}
	}
	return m.Coverages[cls].OriginalCoverage
}

// GetProjectCoverage 返回项目的代码覆盖率，以百分比形式表示。
func (m *Memory) GetProjectCoverage(covType string) (int, int) {
	m.mu.Lock()
	defer m.mu.Unlock()

	var missedLineCount, coveredLineCount int
	if m.Diffs != nil { // 有增量
		for cls, diffs := range m.Diffs {
			if m.Coverages[cls] == nil { // 无覆盖率
				missedLineCount += len(diffs)
			} else {
				var cov *coverage.CoverageInfo
				// 有覆盖率
				if covType == "origin" {
					cov = m.Coverages[cls].OriginalCoverage
				} else if covType == "current" {
					cov = m.Coverages[cls].NewCoverage
				}
				if cov == nil {
					if m.Coverages[cls].NewCoverage != nil {
						missedLineCount += len(m.Coverages[cls].NewCoverage.CoveredLines) +
							len(m.Coverages[cls].NewCoverage.MissedLines)
					} else {
						missedLineCount += len(diffs)
					}
				} else {
					missedLineCount += len(helper.Intersect(cov.MissedLines, m.Diffs[cls]))
					coveredLineCount += len(helper.Intersect(cov.CoveredLines, m.Diffs[cls]))
				}
			}
		}
	} else {
		for _, v := range m.Coverages {
			var cov *coverage.CoverageInfo
			// 有覆盖率
			if covType == "origin" {
				cov = v.OriginalCoverage
			} else if covType == "current" {
				cov = v.NewCoverage
			}
			if cov == nil {
				continue
			}
			missedLineCount += len(cov.MissedLines)
			coveredLineCount += len(cov.CoveredLines)
		}
	}

	return coveredLineCount, missedLineCount + coveredLineCount
}

func (m *Memory) GetRangeCoverage(cls string, startLn, endLn int) *coverage.CoverageInfo {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.Coverages[cls] == nil {
		return &coverage.CoverageInfo{}
	}

	coverageInfo := m.Coverages[cls]
	if coverageInfo.NewCoverage == nil {
		return &coverage.CoverageInfo{}
	}

	coveredLines := helper.FindRangeByValues(coverageInfo.NewCoverage.CoveredLines, startLn, endLn)
	missedLines := helper.FindRangeByValues(coverageInfo.NewCoverage.MissedLines, startLn, endLn)
	coveredBranches := helper.FindRangeByValues(coverageInfo.NewCoverage.CoveredBranches, startLn, endLn)
	missedBranches := helper.FindRangeByValues(coverageInfo.NewCoverage.MissedBranches, startLn, endLn)
	return &coverage.CoverageInfo{
		CoveredLines:    coveredLines,
		MissedLines:     missedLines,
		CoveredBranches: coveredBranches,
		MissedBranches:  missedBranches,
		Coverage:        helper.Divide(len(coveredLines), len(coveredLines)+len(missedLines), 4),
	}
}

func (m *Memory) GetOriginalRangeCoverage(cls string, startLn, endLn int) *coverage.CoverageInfo {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.Coverages[cls] == nil {
		return &coverage.CoverageInfo{}
	}

	coverageInfo := m.Coverages[cls]
	if coverageInfo.OriginalCoverage == nil {
		return &coverage.CoverageInfo{}
	}

	return coverageInfo.OriginalCoverage.GetMethodCoverage(startLn, endLn)
}

func (m *Memory) InitOriginalCoverage(coverages map[string]*coverage.CoverageInfo) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if coverages == nil {
		return
	}

	for cls, coverageInfo := range coverages {
		if m.Coverages[cls] == nil {
			m.Coverages[cls] = new(CoverageRecord)
		}
		if m.Coverages[cls].OriginalCoverage == nil {
			m.Coverages[cls].OriginalCoverage = coverageInfo
			m.Coverages[cls].NewCoverage = new(coverage.CoverageInfo)
		} else {
			m.Coverages[cls].OriginalCoverage.Merge(coverageInfo)
		}

		m.Coverages[cls].NewCoverage = new(coverage.CoverageInfo)
		err := helper.DeepCopy(m.Coverages[cls].OriginalCoverage, m.Coverages[cls].NewCoverage)
		if err != nil {
			panic(err)
		}
	}
}

func (m *Memory) UpdateCoverage(coverages map[string]*coverage.CoverageInfo) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if coverages == nil {
		return
	}

	for cls, coverageInfo := range coverages {
		if m.Coverages[cls] == nil {
			m.Coverages[cls] = new(CoverageRecord)
		}

		if m.Coverages[cls].NewCoverage == nil {
			m.Coverages[cls].NewCoverage = coverageInfo
		} else {
			m.Coverages[cls].NewCoverage.Merge(coverageInfo)
		}
	}
}

func (m *Memory) GetFileTimer(filePath string) *helper.Timer {
	m.mu.Lock()
	defer m.mu.Unlock()

	return m.FileProcess[filePath].timer
}

func (m *Memory) GetFileDurations(filePath string) int64 {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.FileProcess[filePath] == nil {
		return 0
	}
	timer := m.FileProcess[filePath].timer
	if timer == nil {
		return 0
	}
	return timer.Duration()
}

func (m *Memory) GetFileProcessStatus(filePath string) string {
	m.mu.Lock()
	defer m.mu.Unlock()

	fileProcess := m.FileProcess[filePath]
	if fileProcess == nil {
		return "--"
	} else if fileProcess.Status == 0 {
		return "未开始"
	} else if fileProcess.Status == 1 {
		return "执行中"
	} else if fileProcess.Status == 2 {
		return "已完成"
	}

	return "--"
}

func (m *Memory) SetFirstGeneratedInfo(info *FirstGeneratedInfoForMethod) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.FirstGenerated = info
}

func (m *Memory) GetFirstGeneratedInfo() *FirstGeneratedInfoForMethod {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.FirstGenerated
}

func (m *Memory) GetGeneratedUnitTest(fPath string) []models.GenUnitTestCase {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.GeneratedUnitTest[fPath]
}
