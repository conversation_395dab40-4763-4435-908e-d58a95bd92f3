package base

import (
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type Agent struct {
	WorkDir       string
	RunCmd        string
	FileMethods   map[string]map[int]int
	FileDiffLines map[string][]int

	MaxIterations   int
	DesiredCoverage int

	llmModelForInfer *llm.ModelConf
	cache            *helper.SafeMap
	resultPath       string
	isParsedCoverage bool
	genType          string
	trigger          string
	codeRepoInfo     *models.CodeRepo
	userParams       *models.Params

	queueForRepair chan<- any // 待修复的队列

}

//type UTAgent struct {
//	Parser *java.Parser
//
//	Environment *environment.Environment
//	Memory      *Memory
//	RepairAct   *repair.Agent
//
//	sensitiveWordsFilter *filter.SensitiveWordsChecker
//}
