package models

import (
	"errors"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"io/fs"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/llm"
)

var inputParam = InputParam{}

type InputParam struct {
	WorkDir string // 工作目录
	Include string // 包含的目录的通配符, 多个目录用逗号分隔
	Exclude string // 排除的目录的通配符, 多个目录用逗号分隔
	RunCmd  string // 单测运行指令

	IterationMax          int    // 单测最大迭代次数, 默认1, 范围0-10
	RepairIterMax         int    // 修复迭代最大次数，默认1, 范围0-10
	SaveCaseStrategy      string // 保存单测结果策略（0：运行通过，1：编译通过)
	DesiredCoverage       int    // 允许为0（以最大努力提升覆盖率）
	AutoModifyBuildScript bool   // 是否自动修改构建脚本

	Debug      bool   // 是否开启调试模式
	RunMode    string // 运行模式
	LogPath    string // 日志路径
	ResultPath string // 结果文件路径

	// 高级参数
	UnitFramework   string // 单元测试框架
	LanguageVersion string // 语言jdk版本

	// 隐藏参数
	UserName string // 用户名
	Trigger  string // 触发方式, 用于记录使用来源
	Language string // 语言环境
	ToolPath string // 工具路径，仅用在IDE环境，默认为~/.UTAgent下面
	CovFile  string // 覆盖率文件
	License  string // comate的license

	RepairLLMModel *llm.ModelConf   // 修复llm模型
	SrcFileMethods map[string][]int // 源文件方法列表

	// vpc使用场景
	Endpoint string
}

func (p *InputParam) Init(cmd *cobra.Command) {
	cmdset := cmd.Flags()

	// workDir 必填参数
	cmdset.StringVarP(&p.WorkDir, "workDir", "w", "", "【必填】可构建目录的绝对路径，仅支持一个输入")
	cmd.MarkFlagRequired("workDir")

	// include的目录，为空时，默认等于workDir
	cmdset.StringVarP(&p.Include, "include", "i", "", "待生成用例的文件夹或文件，为空时默认为workDir路径。支持通配符格式，多个输入用逗号分隔，例如：**/core/")
	cmdset.StringVarP(&p.Exclude, "exclude", "e", "", "排除掉的文件夹或文件，多个输入间使用逗号分隔，例如：**/test/")
	cmdset.StringVarP(&p.RunCmd, "runCmd", "r", "", "环境编译指令")

	// 迭代参数
	cmdset.IntVarP(&p.IterationMax, "iterationMax", "n", 1, "整体执行迭代次数，选填。取值范围（0,10)")
	cmdset.IntVarP(&p.RepairIterMax, "repairIterationMax", "", 1, "修复轮次，选填。取值范围（0,10)")

	cmdset.StringVarP(&p.SaveCaseStrategy, "saveCaseStrategy", "s", SaveRunPassCaseStrategy, "用例保留策略\n"+
		"\t"+SaveRunPassCaseStrategy+": 保留执行通过的用例\n"+
		"\t"+saveCanRunCaseStrategy+": 保留可执行的用例\n")
	cmdset.BoolVarP(&p.AutoModifyBuildScript, "auto", "", false, "是否自动修改构建脚本")

	// 执行模式
	cmdset.StringVarP(&p.RunMode, "runMode", "m", "fast", "UTAgent的运行模式\n\t"+
		"fast：快速模式，速度优先\n\t"+
		"exact：精确模式，准确度高，速度较慢\n")

	cmdset.StringVarP(&p.LogPath, "logPath", "l", "logs/", "日志文件路径，默认为当前目录")
	cmdset.StringVarP(&p.ResultPath, "resultPath", "", "", "结果文件，支持设置目录或json文件路径")

	cmdset.IntVarP(&p.DesiredCoverage, "desiredCoverage", "", 100, "期望达到的覆盖率，默认是0，表示尽可能提高覆盖率")

	cmdset.StringVarP(&p.UnitFramework, "unitFramework", "u", "", "单元测试框架。java语言工程可选：junit4, junit5")
	cmdset.MarkHidden("unitFramework")
	cmdset.StringVarP(&p.LanguageVersion, "languageVersion", "v", "", "语言版本。例如：JDK1.8")
	cmdset.MarkHidden("languageVersion")

	// 隐藏参数
	cmdset.StringVarP(&p.ToolPath, "toolPath", "", "", "指定执行工具下载路径，仅用在IDE环境，默认为~/.UTAgent")
	cmdset.MarkHidden("toolPath")
	cmdset.StringVarP(&p.CovFile, "covFile", "", "", "覆盖率文件路径，java支持.xml格式")
	cmdset.MarkHidden("covFile")
	cmdset.StringVarP(&p.UserName, "userName", "", "", "用户名")
	cmdset.MarkHidden("userName")
	cmdset.StringVarP(&p.License, "license", "", "", "license")
	cmdset.MarkHidden("license")
	// debug模式的特点：打印info
	cmdset.BoolVarP(&p.Debug, "debug", "", false, "是否使用debug模式")
	cmdset.MarkHidden("debug")
	// useSource改为trigger：tool、ide（日志页）不用区分内外。
	cmdset.StringVarP(&p.Trigger, "trigger", "", "TOOL", "用于记录用户使用工具的场景来源。包括：IDE、ICODE、TOOL，默认为TOOL")
	cmdset.MarkHidden("trigger")
	cmdset.StringVarP(&p.Language, "language", "", "", "用于自动检测workDir语言环境失败时，用户指定工程所属语言")
	cmdset.MarkHidden("language")
	cmdset.StringVarP(&p.Endpoint, "endpoint", "", "", "单测服务endpoint")
	cmdset.MarkHidden("endpoint")
}

func (p *InputParam) ParamValid() map[string]string {
	msg := make(map[string]string) // 存储错误参数名和报错信息
	// 检查workDir
	if p.WorkDir == "" {
		msg["workDir"] = "workDir不能为空，请输入一个可构建的工作路径"
		return msg
	}
	if _, err := os.Stat(p.WorkDir); os.IsNotExist(err) {
		msg["workDir"] = p.WorkDir + "不存在"
		return msg
	}
	// 校验include和exclude下是否有文件，没有目标文件时直接退出程序
	if p.Include == "" {
		p.Include = p.WorkDir
	}

	// 校验单测最大迭代次数
	if p.IterationMax < 0 || p.IterationMax > 10 {
		msg["iterationMax"] = "单测最大迭代次数范围为[0,10]，当前数值 " + strconv.Itoa(p.IterationMax) + " 不在合法范围内。"
	}

	// 校验修复迭代最大次数
	if p.RepairIterMax < 0 || p.RepairIterMax > 10 {
		msg["repairIterMax"] = "修复迭代最大次数范围为[0,10]，当前数值 " + strconv.Itoa(p.RepairIterMax) + " 不在合法范围内。"
	}

	// 校验保存单测结果策略
	if p.SaveCaseStrategy != SaveRunPassCaseStrategy && p.SaveCaseStrategy != saveCanRunCaseStrategy {
		msg["saveCaseStrategy"] = "用例保留策略只能指定为\"runPass\"策略或\"canRun\"策略。"
	}

	// 期望达到的覆盖率范围
	if p.DesiredCoverage < 0 || p.DesiredCoverage > 100 {
		msg["desiredCoverage"] = "期望达到的覆盖率范围为(0,100]，当前数值 " + strconv.Itoa(p.DesiredCoverage) + " 不在合法范围内。"
	}

	if p.RunMode == "exact" {
		p.RepairLLMModel = llm.ModelConfForQianfan(llm.EB35)
	} else {
		p.RepairLLMModel = llm.ModelConfForInnerUtInfer(llm.GetDefaultRepairModel())
	}

	if p.ResultPath != "" {
		if filepath.Ext(p.ResultPath) == "" {
			p.ResultPath = filepath.Join(p.ResultPath, "result.json")
		} else if filepath.Ext(p.ResultPath) != ".json" {
			msg["resultPath"] = "结果文件格式错误，请指定为json文件"
		}
	}

	if p.ToolPath != "" {
		if !filepath.IsAbs(p.ToolPath) {
			dir, _ := os.Getwd()
			p.ToolPath = filepath.Join(dir, p.ToolPath)
		}
		os.Setenv("UTAGENT_HOME", p.ToolPath)
	}

	// 其他参数校验可以根据实际需求添加
	return msg
}

func (p *InputParam) PrintParams() {
	logger.Info("\n【运行参数】")
	logger.InfoT(1, "- desiredCoverage     : %d", p.DesiredCoverage)
	logger.InfoT(1, "- iterationMax        : %d", p.IterationMax)
	logger.InfoT(1, "- repairIterMax       : %d", p.RepairIterMax)
	logger.InfoT(1, "- saveCaseStrategy    : %s", p.SaveCaseStrategy)
	logger.InfoT(1, "- workDir             : %s", p.WorkDir)
	logger.InfoT(1, "- include             : %s", p.Include)
	logger.InfoT(1, "- exclude             : %s", p.Exclude)
	logger.InfoT(1, "- autoModifyBuildScript: %t", p.AutoModifyBuildScript)
	if p.RunCmd != "" {
		logger.InfoT(1, "- runCmd              : %s", p.RunCmd)
	}
	if p.Debug {
		logger.InfoT(1, "- debugMode          : %t", p.Debug)
	}
	if p.UnitFramework != "" {
		logger.InfoT(1, "- unitFramework       : %s", p.UnitFramework)
	}
	if p.LanguageVersion != "" {
		logger.InfoT(1, "- languageVersion     : %s", p.LanguageVersion)
	}
	if p.UserName != "" {
		logger.InfoT(1, "- userName            : %s", p.UserName)
	}
}

// GetWorkDirLanguage 获取工作目录下的可构建语言环境，根据文件后缀和构建脚本类型判断语言
func (p *InputParam) GetWorkDirLanguage() string {
	if len(p.Language) > 0 {
		return p.Language
	}
	// 检测workDir下的构建脚本
	var isGoEnv, isJavaEnv bool
	var hasGoFile, hasJavaFile bool

	workDir := p.WorkDir
	err := filepath.WalkDir(workDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() {
			return nil
		}

		if !d.IsDir() {
			switch d.Name() {
			case "go.mod":
				isGoEnv = true
			case "pom.xml", "gradlew", "build.gradle", "build.gradle.kts":
				isJavaEnv = true
			default:
				if !hasJavaFile && !hasGoFile {
					switch filepath.Ext(d.Name()) {
					case ".go":
						hasGoFile = true
					case ".java":
						hasJavaFile = true
					}
				}
			}

			if (isGoEnv && hasGoFile) || (isJavaEnv && hasJavaFile) {
				return errors.New("find language")
			}
		}
		return nil
	})

	if err != nil && !strings.Contains(err.Error(), "find language") {
		logger.Fatal("遍历工作目录失败，请检查文件是否存在。错误信息：", err)
	}

	if !isGoEnv && !isJavaEnv {
		logger.Error("判断workDir所属语言环境失败,请使用参数--language指定工程所属语言。")
		return ""
	}

	if isJavaEnv && hasJavaFile {
		p.Language = constant.LangJava
	} else if isGoEnv && hasGoFile {
		p.Language = constant.LangGo
	}

	return p.Language
}

func (p *InputParam) GetIncludeFilesAndSrcFiles() []string {
	antMatcher := FormatCovFilter(p.WorkDir, p.Include, p.Exclude)
	srcFiles := make([]string, 0)

	//logger.Debug("【待生成单测文件列表】")
	// 遍历所有文件,查找目标文件
	filepath.Walk(p.WorkDir, func(path string, info fs.FileInfo, err error) error {
		if strings.HasSuffix(path, "mall-demo/src/main/java/com/macro/mall/demo/service/impl/DemoServiceImpl.java") {
			logger.DebugT(1, "path: %s", path)
		}
		if !antMatcher.Match(path) {
			return nil
		}

		if info.IsDir() {
			return nil
		}

		// 只保留主流编程语言后缀的文件
		if !helper.InSlice(helper.GetLangExts(p.GetWorkDirLanguage()), filepath.Ext(info.Name())) {
			return nil
		}
		srcFiles = append(srcFiles, path)
		return nil
	})
	return srcFiles
}

func (p *InputParam) IsTriggerFromIDEorICODE() bool {
	return strings.ToUpper(p.Trigger) == constant.IDETrigger || strings.ToUpper(p.Trigger) == constant.ICODETrigger
}

func (p *InputParam) IsDebug() bool {
	return p.Debug
}

type Params interface {
	Init(diffCmd *cobra.Command)
	ParamValid() map[string]string
	PrintParams()
	CollectSrcMethods() error

	GetDesiredCoverage() int
	GetLang() string
	GetWorkDir() string
	GetRunCmd() string
	GetIterationMax() int
	GetRepairIterMax() int
	GetRepairLLMModel() *llm.ModelConf
	GetSrcMethods() map[string]map[int]int
	GetSrcMethodsCount() int
	GetDiffs() map[string][]int
	GetTrigger() string
	GetType() string
	GetUnitFramework() string
	GetLanguageVersion() string
	GetResultPath() string
	GetCovFilePath() string
	GetSaveCaseStrategy() string
	GetUserName() string
	GetLicense() string
	IsAutoModifyBuildScript() bool
}
