package models

import (
	"icode.baidu.com/baidu/cov/iUT/helper/baidu"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools"
)

type CodeRepo struct {
	GitRoot      string
	RepoName     string
	Branch       string
	CommitID     string
	BaseCommitID string
	Committer    string

	IsCRCommit bool
}

func BuildCodeRepo(workDir, baseCommitID string) *CodeRepo {
	codeRepo := &CodeRepo{}
	gitRoot, err := tools.GetGitRoot(workDir)
	if err != nil {
		logger.Warn("get git root error: %v", err)
	}
	codeRepo.GitRoot = gitRoot

	branch, err := tools.GetGitBranch(gitRoot)
	if err != nil {
		logger.Warn("get git branch error: %v", err)
	}
	codeRepo.Branch = branch

	codeRepo.RepoName = baidu.GetBaiduRepo(gitRoot)

	commitInfo, err := tools.GetCodeCommitInfo(gitRoot)
	if err != nil {
		logger.Warn("get code commit info error: %v", err)
	} else {
		codeRepo.CommitID = commitInfo.CommitID
		codeRepo.Committer = commitInfo.Committer
	}

	// 判断是否为CR提交
	if codeRepo.CommitID != "" && !tools.IsCommitInRemote(gitRoot, codeRepo.CommitID) {
		codeRepo.IsCRCommit = true
	}

	// 未提供基准版本，则使用当前版本作为基准
	if baseCommitID == "" {
		baseCommitID = codeRepo.CommitID
	}

	codeRepo.BaseCommitID = baseCommitID
	return codeRepo
}
