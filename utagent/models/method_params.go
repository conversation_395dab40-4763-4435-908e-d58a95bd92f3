package models

import (
	"fmt"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	goparser "icode.baidu.com/baidu/cov/smartUT-parser/golang"
	javaparser "icode.baidu.com/baidu/cov/smartUT-parser/java"
	"os"
	"sort"
)

type MethodParam struct {
	InputParam *InputParam

	StartLine int // 起始行
	EndLine   int // 结束行

	SrcFileMethods    map[string]map[int]int
	TargetMethodsName []string
}

func NewMethodParam(base *InputParam) *MethodParam {
	return &MethodParam{
		InputParam: base,
	}
}

func (mp *MethodParam) Init(cmd *cobra.Command) {
	mp.InputParam.Init(cmd)
	cmdSet := cmd.Flags()
	cmdSet.IntVarP(&mp.StartLine, "startLine", "", 0, "【必填】指定方法起始行")
	cmdSet.IntVarP(&mp.EndLine, "endLine", "", 0, "【必填】指定方法结束行")
	cmd.MarkFlagRequired("startLine")
	cmd.MarkFlagRequired("endLine")
	cmdSet.Parse(os.Args[1:])

	if mp.InputParam.Debug {
		config.SetDebug()
		logger.DebugMode()
	}

	// 设置单测服务endpoint
	if mp.InputParam.Endpoint != "" {
		config.SetServerEndpoint(mp.InputParam.Endpoint)
	}
}

func (mp *MethodParam) ParamValid() map[string]string {
	errorParams := mp.InputParam.ParamValid()
	if len(errorParams) != 0 {
		return errorParams
	}

	// 指定方法生成参数,
	if mp.StartLine < 1 {
		errorParams["startLine"] = "startLine 不能为空或小于1"
		return errorParams
	}

	if mp.EndLine < 1 {
		errorParams["endLine"] = "endLine 不能为空或小于1"
		return errorParams
	}

	return nil
}

func (mp *MethodParam) PrintParams() {
	mp.InputParam.PrintParams()
	logger.InfoT(1, "- startLine           : %d", mp.StartLine)
	logger.InfoT(1, "- endLine             : %d", mp.EndLine)
}

func (mp *MethodParam) CollectSrcMethods() error {
	srcFiles := mp.InputParam.GetIncludeFilesAndSrcFiles()
	if len(srcFiles) == 0 {
		return fmt.Errorf("根据提供的include和exclude信息, 未收集到需要生成用例的源文件")
	}

	if len(srcFiles) > 1 {
		return fmt.Errorf("根据提供的include和exclude信息, 收集到多个需要生成用例的源文件, 仅支持一个被测代码文件输入")
	}

	if mp.GetLang() == constant.LangJava {
		return mp.parseJavaMethod(srcFiles[0])
	} else if mp.GetLang() == constant.LangGo {
		return mp.parseGoMethod(srcFiles[0])
	} else {
		return fmt.Errorf("仅支持Java和Go语言代码")
	}
}

func (mp *MethodParam) parseJavaMethod(filePath string) error {
	fileContent := helper.ReadFile(filePath)
	parser, err := javaparser.NewFileParserWithContent(fileContent)
	if err != nil {
		logger.DebugT(1, "解析文件失败: %s", err)
		return fmt.Errorf("文件%s创建解析器失败，文件内容为：\n%s\n", filePath, fileContent)
	}
	fileContext := parser.Parse(nil)
	if fileContext.MainClass == nil || len(fileContext.MainClass.Methods) == 0 {
		logger.InfoT(1, "文件%s没有需要生成用例的方法", filePath)
		return fmt.Errorf("文件%s解析失败，文件内容为：\n%s\n", filePath, fileContent)
	}

	sortedMethods := fileContext.MainClass.Methods
	sort.Slice(sortedMethods, func(i, j int) bool {
		return sortedMethods[i].StartLine < sortedMethods[j].StartLine
	})

	mp.SrcFileMethods = make(map[string]map[int]int)
	for _, method := range sortedMethods {
		if !method.IsPrivateOrProtected() {
			// 判断方法是否被代码块覆盖
			if method.StartLine+1 <= mp.EndLine && method.EndLine+1 >= mp.StartLine {
				if mp.SrcFileMethods[filePath] == nil {
					mp.SrcFileMethods[filePath] = map[int]int{method.StartLine: method.EndLine}
				}
				mp.SrcFileMethods[filePath][method.StartLine] = method.EndLine
				mp.TargetMethodsName = append(mp.TargetMethodsName, method.Identifier)
			}
		}
	}

	return nil
}

func (mp *MethodParam) parseGoMethod(filePath string) error {
	fileContent := helper.ReadFile(filePath)
	parser, err := goparser.NewFileParserWithContent(fileContent)
	if err != nil {
		logger.DebugT(1, "解析文件失败: %s", err)
		return fmt.Errorf("文件%s解析失败", filePath)
	}
	fileContext := parser.Parse()
	if fileContext == nil || len(fileContext.Methods) == 0 {
		return fmt.Errorf("文件%s解析失败", filePath)
	}

	sortedMethods := fileContext.Methods
	sort.Slice(sortedMethods, func(i, j int) bool {
		return sortedMethods[i].Block.StartLine < sortedMethods[j].Block.StartLine
	})

	mp.SrcFileMethods = make(map[string]map[int]int)
	for _, method := range sortedMethods {
		// 判断方法是否被代码块覆盖
		if method.Block.StartLine+1 <= mp.EndLine && method.Block.EndLine+1 >= mp.StartLine {
			if mp.SrcFileMethods[filePath] == nil {
				mp.SrcFileMethods[filePath] = map[int]int{method.Block.StartLine: method.Block.EndLine}
			}
			mp.SrcFileMethods[filePath][method.Block.StartLine] = method.Block.EndLine
			mp.TargetMethodsName = append(mp.TargetMethodsName, method.Identifier)
		}

	}

	return nil
}

func (mp *MethodParam) GetDesiredCoverage() int {
	return mp.InputParam.DesiredCoverage
}
func (mp *MethodParam) GetWorkDir() string {
	return mp.InputParam.WorkDir
}

func (mp *MethodParam) GetLang() string {
	return mp.InputParam.GetWorkDirLanguage()
}

func (mp *MethodParam) GetRunCmd() string {
	return mp.InputParam.RunCmd
}

func (mp *MethodParam) GetIterationMax() int {
	return mp.InputParam.IterationMax
}

func (mp *MethodParam) GetRepairLLMModel() *llm.ModelConf {
	return mp.InputParam.RepairLLMModel
}

func (mp *MethodParam) GetSrcMethods() map[string]map[int]int {
	return mp.SrcFileMethods
}

func (mp *MethodParam) GetSrcMethodsCount() int {
	var count int
	for _, methods := range mp.SrcFileMethods {
		count += len(methods)
	}
	return count
}

func (mp *MethodParam) GetTrigger() string {
	return mp.InputParam.Trigger
}

func (mp *MethodParam) GetType() string {
	return constant.GenerateForMethod
}

func (mp *MethodParam) GetUnitFramework() string {
	return mp.InputParam.UnitFramework
}

func (mp *MethodParam) GetLanguageVersion() string {
	return mp.InputParam.LanguageVersion
}

func (mp *MethodParam) GetResultPath() string {
	return mp.InputParam.ResultPath
}

func (mp *MethodParam) GetCovFilePath() string {
	return mp.InputParam.CovFile
}

func (mp *MethodParam) GetDiffs() map[string][]int {
	return nil
}

func (mp *MethodParam) GetSaveCaseStrategy() string {
	return mp.InputParam.SaveCaseStrategy
}

func (mp *MethodParam) GetUserName() string {
	return mp.InputParam.UserName
}

func (mp *MethodParam) GetLicense() string {
	return mp.InputParam.License
}

func (mp *MethodParam) GetRepairIterMax() int {
	return mp.InputParam.RepairIterMax
}

func (mp *MethodParam) IsAutoModifyBuildScript() bool {
	return mp.InputParam.AutoModifyBuildScript
}
