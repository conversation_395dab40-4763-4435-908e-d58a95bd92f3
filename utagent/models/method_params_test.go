package models

import (
	"os"
	"testing"
)

// TestParseJavaMethod_FileNotFound 是用于测试 ParseJavaMethod_FileNotFound
// generated by Comate
func TestParseJavaMethod_FileNotFound(t *testing.T) {
	methodParam := &MethodParam{
		StartLine: 1,
		EndLine:   3,
	}

	err := methodParam.parseJavaMethod("non_existent.java")
	if err == nil {
		t.Errorf("Expected error, got nil")
	}
}

// TestParseJavaMethod_NoMethods 是用于测试 ParseJavaMethod_NoMethods
// generated by Comate
func TestParseJavaMethod_NoMethods(t *testing.T) {
	testFileContent := `
		package com.example;
		public class ExampleClass {
			private void testMethod1() {
			}
			private void testMethod2() {
			}
			private void testMethod3() {
			}
		}
	`
	err := os.WriteFile("test.java", []byte(testFileContent), 0644)
	if err != nil {
		t.Fatalf("Failed to write test file: %v", err)
	}
	defer os.Remove("test.java")
	methodParam := &MethodParam{
		StartLine: 1,
		EndLine:   3,
	}
	err = methodParam.parseJavaMethod("test.java")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if len(methodParam.TargetMethodsName) != 0 {
		t.Errorf("Expected no methods, got %v", methodParam.TargetMethodsName)
	}
}

// TestGetRepairIterMax1 是用于测试 GetRepairIterMax
// generated by Comate
func TestGetRepairIterMax1(t *testing.T) {
	// 测试输入参数为空的情况
	mp := &MethodParam{
		InputParam: &InputParam{
			RepairIterMax: 0,
		},
	}
	expected := 0
	actual := mp.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}

	// 测试输入参数为1的情况
	mp = &MethodParam{
		InputParam: &InputParam{
			RepairIterMax: 1,
		},
	}
	expected = 1
	actual = mp.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}

	// 测试输入参数为10的情况
	mp = &MethodParam{
		InputParam: &InputParam{
			RepairIterMax: 10,
		},
	}
	expected = 10
	actual = mp.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}

	// 测试输入参数为负数的情况
	mp = &MethodParam{
		InputParam: &InputParam{
			RepairIterMax: -1,
		},
	}
	expected = -1
	actual = mp.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}
}

// TestIsAutoModifyBuildScript1 是用于测试 IsAutoModifyBuildScript
// generated by Comate
func TestIsAutoModifyBuildScript1(t *testing.T) {
	// 测试InputParam.AutoModifyBuildScript为true时，IsAutoModifyBuildScript返回true
	inputParam := &InputParam{
		AutoModifyBuildScript: true,
	}
	methodParam := &MethodParam{
		InputParam: inputParam,
	}
	if !methodParam.IsAutoModifyBuildScript() {
		t.Errorf("Expected IsAutoModifyBuildScript to return true, but got false")
	}

	// 测试InputParam.AutoModifyBuildScript为false时，IsAutoModifyBuildScript返回false
	inputParam = &InputParam{
		AutoModifyBuildScript: false,
	}
	methodParam = &MethodParam{
		InputParam: inputParam,
	}
	if methodParam.IsAutoModifyBuildScript() {
		t.Errorf("Expected IsAutoModifyBuildScript to return false, but got true")
	}
}
