package models

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
)

type RepairResult struct {
	RepairContent string    // 修复内容
	RepairID      string    // 修复ID
	RepairTool    string    // 修复工具
	ErrorType     ErrorType // 错误类型
}

type RunResult struct {
	State              int                               `json:"state"`         // 运行状态 0 成功其他失败
	Message            string                            `json:"message"`       // 运行信息
	IsCompileSucceeded bool                              `json:"compileState"`  // 编译是否成功
	CompileErrors      []*CompileError                   `json:"compileErrors"` // 编译错误
	IsRunWithoutError  bool                              `json:"runState"`      // 运行时是否出现异常
	RunErrorMsg        string                            `json:"runErrorMsg"`   // 运行时异常信息
	RunResults         map[string]*UnitTestResult        `json:"runResult"`     // 运行结果
	ClassCoverageInfo  map[string]*coverage.CoverageInfo `json:"classCoverage"` // 类覆盖率信息
}

func (rr *RunResult) ErrorMessage() string {
	msg := make([]string, 0)
	if !rr.IsCompileSucceeded {
		for _, compileError := range rr.CompileErrors {
			msg = append(msg, fmt.Sprintf("%s:%d %s", compileError.SourceName, compileError.LineNumber, compileError.ErrorMessage))
		}
	} else if !rr.IsRunWithoutError {
		msg = append(msg, rr.RunErrorMsg)
	} else {
		for _, runResults := range rr.RunResults {
			if runResults.IsRunSucceed {
				continue
			}
			if runResults.StackTrace != nil {
				for _, stack := range runResults.StackTrace {
					msg = append(msg, fmt.Sprintf("%s:%d %s.%s", filepath.Base(stack.FileName), stack.LineNumber, stack.DeclaringClass, stack.MethodName))
				}
			} else {
				msg = append(msg, fmt.Sprintf("%s:%d %s", runResults.MethodName, runResults.LineNumber, runResults.FailureMessage))
			}

		}
	}
	return strings.Join(msg, "\n")
}

type CompileError struct {
	LineNumber   int    `json:"lineNum"`    // 行号
	ColumnNumber int    `json:"columnNum"`  // 列号
	SourceName   string `json:"sourceName"` // 源文件名称
	ErrorMessage string `json:"errorMsg"`   // 错误消息
	ErrorType    string `json:"type"`       // 错误类型
}

type UnitTestResult struct {
	IsRunSucceed   bool          `json:"succeed"`    // 执行是否成功（包含运行时和断言）
	MethodName     string        `json:"methodName"` // 方法名称
	LineNumber     int           `json:"lineNum"`    // 行号
	FailureMessage string        `json:"failureMsg"` // 失败消息
	Exception      string        `json:"exception"`  // 异常信息
	StackTrace     []*StackTrace `json:"stackTrace"` // 堆栈信息
	//UnitType       string        `json:"type"`       // 单元类型，有container和test两种

	ActualResultOutput string `json:"actualResultOutput"` // 实际结果输出

	MethodCoverageInfo map[string]*coverage.CoverageInfo `json:"methodCoverages"` // 方法覆盖率信息
}

type StackTrace struct {
	DeclaringClass string `json:"className"`  // 声明类
	MethodName     string `json:"methodName"` // 方法名称
	FileName       string `json:"fileName"`   // 文件名称
	LineNumber     int    `json:"lineNumber"` // 行号
}

// IsRunPass 判断单测是否运行通过
// 如果编译成功，则遍历所有方法运行结果，若所有方法均运行成功则返回true，否则返回false
// 如果编译失败，则直接返回false
func (result *RunResult) IsRunPass() bool {
	if result != nil && result.IsCompileSucceeded {
		if !result.IsRunWithoutError {
			return false
		}

		if len(result.RunResults) == 0 {
			return false
		}

		for _, methodRunResult := range result.RunResults {
			if !methodRunResult.IsRunSucceed {
				return false
			}
		}
		return true
	}
	return false
}

// CollectRunResult 返回运行的结果： 是否可以运行， 是否运行通过
func (result *RunResult) CollectRunResult(testCaseName string) (bool, bool) {
	if result != nil && result.IsCompileSucceeded {

		if !result.IsRunWithoutError {
			// 运行时存在异常，返回失败
			return false, false
		}

		caseResult := result.RunResults[testCaseName]
		if caseResult == nil {
			return false, false
		}

		if caseResult.IsRunSucceed {
			return true, true
		}

		if result.IsAssertError(caseResult.Exception) {
			return true, false
		}
	}

	return false, false
}

func (result *RunResult) CanRepair() bool {
	if result == nil {
		return false
	}

	if len(result.CompileErrors) > 10 {
		return false
	}

	return true
}

func (result *RunResult) IsAssertError(errorMsg string) bool {
	if strings.Contains(errorMsg, "java.lang.AssertionError") {
		return true
	}

	if strings.Contains(errorMsg, "AssertionFailedError") {
		return true
	}

	re := regexp.MustCompile(`expected:(.+)but was:(.+)`)
	if re.MatchString(errorMsg) {
		return true
	}

	// go的断言错误匹配
	re = regexp.MustCompile(`(?i)Expected(.+)got(.+)`)
	if re.MatchString(errorMsg) {
		return true
	}

	re = regexp.MustCompile(`(?i)expected(.|\n)+actual(.+)`)
	if re.MatchString(errorMsg) {
		return true
	}

	return false
}

func (result *RunResult) CollectErrorBug(testCaseName string, testFileName string) *Bug {
	if result == nil {
		return nil
	}
	caseResult := result.RunResults[testCaseName]
	if caseResult == nil {
		return nil
	}

	if caseResult.IsRunSucceed {
		return nil
	}

	msg := caseResult.Exception
	focusLine := -1
	for _, trace := range caseResult.StackTrace {
		msg += "\n" + fmt.Sprintf("    at %s.%s(%s:%d)", trace.DeclaringClass,
			trace.MethodName,
			trace.FileName, trace.LineNumber)
		if trace.FileName == filepath.Base(testFileName) {
			focusLine = trace.LineNumber
			break
		}
	}

	return &Bug{
		BugMsg:       fmt.Sprintf("### 错误详情\n```java\n%s\n```", msg),
		BugFocusLine: focusLine,
	}
}
