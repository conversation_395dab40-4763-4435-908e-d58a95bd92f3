package models

import (
	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
)

// FormatContentWithTool 格式化代码,多语言通用
func FormatContentWithTool(language, codeContent, filePath string) string {
	formatUrl := config.GetHost() + config.FormatAPIPath
	formatBody := map[string]interface{}{
		"language":     language,
		"code_content": codeContent,
		"file_path":    filePath, // 给虚拟文件路径即可
	}
	formatRes, err := httputil.HTTPPostBody(formatUrl, formatBody)
	if err != nil {
		return codeContent
	}
	data := formatRes["data"].(map[string]interface{})
	if data != nil && data["ut_file_content"].(string) != "" {
		return data["ut_file_content"].(string)
	}

	return codeContent
}
