package models

import (
	"fmt"
	"reflect"
	"testing"
)

func TestDiffParam_CollectSrcMethods(t *testing.T) {
	type fields struct {
		DiffParam    *DiffParam
		BaseCommitID string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "test-1",
			fields: fields{
				DiffParam: NewDiffParam(&InputParam{
					WorkDir:        "/Users/<USER>/git/baidu/cov/java-evaluate-data/xxl-job",
					Include:        "/Users/<USER>/git/baidu/cov/java-evaluate-data/xxl-job",
					Exclude:        "",
					SrcFileMethods: make(map[string][]int),
				}),
				BaseCommitID: "",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if err := tt.fields.DiffParam.CollectSrcMethods(); (err == nil) != tt.wantErr {
				t.Errorf("DiffParam.CollectSrcMethods() error = %v, wantErr %v", err, tt.wantErr)
			}
			fmt.Println(reflect.ValueOf(tt.fields.DiffParam).Elem().FieldByName("SrcFileMethods").Interface())
		})
	}
}

func TestDiffParam_CollectSrcMethods_1(t *testing.T) {
	tests := []struct {
		name    string
		dp      *DiffParam
		wantErr bool
	}{
		{
			name: "test-1",
			dp: &DiffParam{
				InputParam: &InputParam{
					WorkDir:        "/Users/<USER>/git/baidu/cov/runUTAgent",
					Include:        "/Users/<USER>/git/baidu/cov/runUTAgent",
					Exclude:        "",
					SrcFileMethods: make(map[string][]int),
				},
				BaseCommitID: "724dd806af1bfb09f69dc1132cfd104272c60959",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.dp.CollectSrcMethods(); (err == nil) != tt.wantErr {
				t.Errorf("DiffParam.CollectSrcMethods() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDiffParam_CollectSrcMethodsByFileAndDiff(t *testing.T) {
	type fields struct {
		InputParam     *InputParam
		BaseCommitID   string
		SrcFileMethods map[string]map[int]int
	}
	type args struct {
		file         string
		workDir      string
		baseCommitID string
		gitBase      string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "test-1",
			fields: fields{
				InputParam: &InputParam{
					WorkDir: "/Users/<USER>/git/baidu/cov/runUTAgent",
					Include: "/Users/<USER>/git/baidu/cov/runUTAgent",
					Exclude: "",
				},
				BaseCommitID: "",
			},
			args: args{
				file:         "/Users/<USER>/git/baidu/cov/runUTAgent/runner/src/main/java/com/baidu/cov/runner/Util.java",
				workDir:      "/Users/<USER>/git/baidu/cov/runUTAgent",
				baseCommitID: "724dd806af1bfb09f69dc1132cfd104272c60959",
				gitBase:      "/Users/<USER>/git/baidu/cov/runUTAgent",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dp := &DiffParam{
				InputParam:     tt.fields.InputParam,
				BaseCommitID:   tt.fields.BaseCommitID,
				SrcFileMethods: tt.fields.SrcFileMethods,
			}
			dp.Diffs = make(map[string][]int)
			dp.SrcFileMethods = make(map[string]map[int]int)

			dp.CollectSrcMethodsByFileAndDiff(tt.args.file, tt.args.workDir, tt.args.baseCommitID, tt.args.gitBase)

			if len(dp.Diffs) == 0 {
				t.Errorf("DiffParam.Diffs = %v", dp.Diffs)
			}

		})
	}
}

// TestGetRepairIterMax2 是用于测试 GetRepairIterMax
// generated by Comate
func TestGetRepairIterMax2(t *testing.T) {
	// 测试默认值
	diffParam := &DiffParam{
		InputParam: &InputParam{
			RepairIterMax: 1,
		},
	}
	expected := 1
	actual := diffParam.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}

	// 测试非默认值
	diffParam = &DiffParam{
		InputParam: &InputParam{
			RepairIterMax: 5,
		},
	}
	expected = 5
	actual = diffParam.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}

	// 测试0值
	diffParam = &DiffParam{
		InputParam: &InputParam{
			RepairIterMax: 0,
		},
	}
	expected = 0
	actual = diffParam.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}

	// 测试负值
	diffParam = &DiffParam{
		InputParam: &InputParam{
			RepairIterMax: -1,
		},
	}
	expected = -1
	actual = diffParam.GetRepairIterMax()
	if actual != expected {
		t.Errorf("Expected %d, but got %d", expected, actual)
	}
}

// TestIsAutoModifyBuildScript2 是用于测试 IsAutoModifyBuildScript
// generated by Comate
func TestIsAutoModifyBuildScript2(t *testing.T) {
	// 测试InputParam.AutoModifyBuildScript为true的情况
	diffParam := &DiffParam{
		InputParam: &InputParam{
			AutoModifyBuildScript: true,
		},
	}
	if !diffParam.IsAutoModifyBuildScript() {
		t.Errorf("Expected IsAutoModifyBuildScript to be true, but got false")
	}

	// 测试InputParam.AutoModifyBuildScript为false的情况
	diffParam = &DiffParam{
		InputParam: &InputParam{
			AutoModifyBuildScript: false,
		},
	}
	if diffParam.IsAutoModifyBuildScript() {
		t.Errorf("Expected IsAutoModifyBuildScript to be false, but got true")
	}
}
