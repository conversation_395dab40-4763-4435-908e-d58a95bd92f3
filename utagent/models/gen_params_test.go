package models

import (
	"testing"
)

// TestGetRepairIterMax 是用于测试 GetRepairIterMax
// generated by Comate
func TestGetRepairIterMax(t *testing.T) {
	// 测试输入参数为空的情况
	genParam := &GenParam{
		InputParam: &InputParam{
			RepairIterMax: 0,
		},
	}
	expected := 0
	actual := genParam.GetRepairIterMax()
	if actual != expected {
		t.<PERSON><PERSON><PERSON>("Expected %d, but got %d", expected, actual)
	}

	// 测试输入参数为非空的情况
	genParam = &GenParam{
		InputParam: &InputParam{
			RepairIterMax: 5,
		},
	}
	expected = 5
	actual = genParam.GetRepairIterMax()
	if actual != expected {
		t.<PERSON><PERSON>rf("Expected %d, but got %d", expected, actual)
	}

	// 测试输入参数为负数的情况
	genParam = &GenParam{
		InputParam: &InputParam{
			RepairIterMax: -1,
		},
	}
	expected = -1
	actual = genParam.GetRepairIterMax()
	if actual != expected {
		t.<PERSON><PERSON><PERSON>("Expected %d, but got %d", expected, actual)
	}
}

// TestIsAutoModifyBuildScript 是用于测试 IsAutoModifyBuildScript
// generated by Comate
func TestIsAutoModifyBuildScript(t *testing.T) {
	// 测试InputParam.AutoModifyBuildScript为true时，IsAutoModifyBuildScript返回true
	param := &GenParam{
		InputParam: &InputParam{
			AutoModifyBuildScript: true,
		},
	}
	if !param.IsAutoModifyBuildScript() {
		t.Errorf("Expected IsAutoModifyBuildScript to return true, but got false")
	}

	// 测试InputParam.AutoModifyBuildScript为false时，IsAutoModifyBuildScript返回false
	param = &GenParam{
		InputParam: &InputParam{
			AutoModifyBuildScript: false,
		},
	}
	if param.IsAutoModifyBuildScript() {
		t.Errorf("Expected IsAutoModifyBuildScript to return false, but got true")
	}
}
