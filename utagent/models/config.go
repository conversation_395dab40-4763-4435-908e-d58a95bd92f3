package models

import (
	"errors"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/llm"
)

type UserConfig struct {
	LLMModelConfig *ModelConfigs `toml:"LLMModel"`
	License        string        `toml:"License"`
}

func (u *UserConfig) IsValidAndAccessible() (bool, error) {
	if u == nil {
		return false, errors.New("未获取到任何配置信息，请检查配置文件是否存在，或者使用 UTAgent setup 命令初始化配置文件")
	}
	if u.LLMModelConfig == nil || !u.LLMModelConfig.IsValid() {
		return false, errors.New("配置文件内容错误，请在配置文件中配置正确的LLM模型！")
	}
	if err := llm.CheckModelConfigAccess(u.LLMModelConfig.Gen); err != nil {
		return false, fmt.Errorf("模型配置LLMModel.Gen错误：%s，请检查模型配置文件内容", err.Error())
	}

	if err := llm.CheckModelConfigAccess(u.LLMModelConfig.Repair); err != nil {
		return false, fmt.Errorf("模型配置LLMModel.Repair错误：%s，请检查模型配置文件内容", err.Error())
	}

	return true, nil
}

type ModelConfigs struct {
	Repair *llm.ModelConf `toml:"Repair,omitempty"`
	Gen    *llm.ModelConf `toml:"Gen,omitempty"`
}

func (u *ModelConfigs) IsValid() bool {
	if u == nil {
		return false
	}

	if u.Gen == nil || u.Gen.Model == "" {
		return false
	}

	if u.Repair == nil || u.Repair.Model == "" {
		return false
	}

	if u.Gen.Model == llm.DeepCode || u.Gen.Model == llm.EC2Sft {
		return true
	}

	return false
}
