package models

import (
	"testing"

	"github.com/spf13/cobra"
)

// TestInputParam_Init 是用于测试 InputParam_Init
// generated by Comate
func TestInputParam_Init(t *testing.T) {
	cmd := &cobra.Command{}
	inputParam := &InputParam{}
	inputParam.Init(cmd)

	// 检查是否添加了必要的标志
	flagNames := []string{"workDir", "include", "exclude", "runCmd", "iterationMax", "repairIterationMax", "saveCaseStrategy", "runMode", "logPath", "resultPath", "desiredCoverage", "unitFramework", "languageVersion", "toolPath", "covFile", "userName", "license", "debug", "trigger", "language", "endpoint"}
	for _, flagName := range flagNames {
		if cmd.Flags().Lookup(flagName) == nil {
			t.Errorf("Flag %s not added", flagName)
		}
	}

	// 检查是否添加了隐藏标志
	hiddenFlagNames := []string{"toolPath", "covFile", "userName", "license", "debug", "trigger", "language", "endpoint"}
	for _, flagName := range hiddenFlagNames {
		if cmd.Flags().Lookup(flagName) == nil || !cmd.Flags().Lookup(flagName).Hidden {
			t.Errorf("Hidden flag %s not added", flagName)
		}
	}

	// 检查默认值
	if inputParam.RunMode != "fast" {
		t.Errorf("Default runMode should be 'fast'")
	}
	if inputParam.LogPath != "logs/" {
		t.Errorf("Default logPath should be 'logs/'")
	}
	if inputParam.DesiredCoverage != 100 {
		t.Errorf("Default desiredCoverage should be 100")
	}
	if inputParam.Debug != false {
		t.Errorf("Default debug should be false")
	}
	if inputParam.Trigger != "TOOL" {
		t.Errorf("Default trigger should be 'TOOL'")
	}
}

// TestParamValid_WorkDirNotExist 是用于测试 ParamValid_WorkDirNotExist
// generated by Comate
func TestParamValid_WorkDirNotExist(t *testing.T) {
	inputParam := &InputParam{
		WorkDir: "/non_existent_dir",
	}
	result := inputParam.ParamValid()
	if len(result) == 0 {
		t.Error("Expected error, but got no error")
	}
	if _, ok := result["workDir"]; !ok {
		t.Error("Expected error for workDir, but got no error for workDir")
	}
	if result["workDir"] != "/non_existent_dir不存在" {
		t.Errorf("Expected error message for workDir: /non_existent_dir不存在, but got: %s", result["workDir"])
	}
}

// TestParamValid_WorkDirEmpty 是用于测试 ParamValid_WorkDirEmpty
// generated by Comate
func TestParamValid_WorkDirEmpty(t *testing.T) {
	inputParam := &InputParam{
		WorkDir: "",
	}
	result := inputParam.ParamValid()
	if len(result) == 0 {
		t.Error("Expected error, but got no error")
	}
	if _, ok := result["workDir"]; !ok {
		t.Error("Expected error for workDir, but got no error for workDir")
	}
	if result["workDir"] != "workDir不能为空，请输入一个可构建的工作路径" {
		t.Errorf("Expected error message for workDir: workDir不能为空，请输入一个可构建的工作路径, but got: %s", result["workDir"])
	}
}

// TestParamValid_IterationMaxOutOfRange 是用于测试 ParamValid_IterationMaxOutOfRange
// generated by Comate
func TestParamValid_IterationMaxOutOfRange(t *testing.T) {
	inputParam := &InputParam{
		WorkDir:       "/tmp",
		IterationMax:  11,
		RepairIterMax: 10,
	}
	result := inputParam.ParamValid()
	if len(result) == 0 {
		t.Error("Expected error, but got no error")
	}
	if _, ok := result["iterationMax"]; !ok {
		t.Error("Expected error for iterationMax, but got no error for iterationMax")
	}
	if result["iterationMax"] != "单测最大迭代次数范围为[0,10]，当前数值 11 不在合法范围内。" {
		t.Errorf("Expected error message for iterationMax: 单测最大迭代次数范围为[0,10]，当前数值 11 不在合法范围内。, but got: %s", result["iterationMax"])
	}
}

// TestParamValid_RepairIterMaxOutOfRange 是用于测试 ParamValid_RepairIterMaxOutOfRange
// generated by Comate
func TestParamValid_RepairIterMaxOutOfRange(t *testing.T) {
	inputParam := &InputParam{
		WorkDir:       "/tmp",
		IterationMax:  0,
		RepairIterMax: -1,
	}
	result := inputParam.ParamValid()
	if len(result) == 0 {
		t.Error("Expected error, but got no error")
	}
	if _, ok := result["repairIterMax"]; !ok {
		t.Error("Expected error for repairIterMax, but got no error for repairIterMax")
	}
	if result["repairIterMax"] != "修复迭代最大次数范围为[0,10]，当前数值 -1 不在合法范围内。" {
		t.Errorf("Expected error message for repairIterMax: 修复迭代最大次数范围为[0,10]，当前数值 -1 不在合法范围内。, but got: %s", result["repairIterMax"])
	}
}

// TestInit 是用于测试 Init
// generated by Comate
func TestInit(t *testing.T) {
	cmd := &cobra.Command{}
	param := &InputParam{}
	param.Init(cmd)

	// 检查默认值
	if param.RunMode != "fast" {
		t.Errorf("Expected RunMode to be 'fast', got %s", param.RunMode)
	}
	if param.LogPath != "logs/" {
		t.Errorf("Expected LogPath to be 'logs/', got %s", param.LogPath)
	}
	if param.DesiredCoverage != 100 {
		t.Errorf("Expected DesiredCoverage to be 100, got %d", param.DesiredCoverage)
	}
	if param.AutoModifyBuildScript != false {
		t.Errorf("Expected AutoModifyBuildScript to be false, got %t", param.AutoModifyBuildScript)
	}
	if param.Debug != false {
		t.Errorf("Expected Debug to be false, got %t", param.Debug)
	}
	if param.Trigger != "TOOL" {
		t.Errorf("Expected Trigger to be 'TOOL', got %s", param.Trigger)
	}
}
