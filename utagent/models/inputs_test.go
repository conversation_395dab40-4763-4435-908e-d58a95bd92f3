package models

import (
	"sync"
	"testing"

	"icode.baidu.com/baidu/cov/iUT/helper"
)

func TestString(t *testing.T) {
	tests := []struct {
		name string
		e    ErrorType
		want string
	}{
		{
			name: "test1",
			e:    "error",
			want: "error",
		},
		{
			name: "test2",
			e:    "another error",
			want: "another error",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.e.Value(); got != tt.want {
				t.Errorf("String() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestGetRound 是用于测试 GetRound
// generated by Comate
func TestGetRound(t *testing.T) {
	// 初始化 Process 结构体
	p := &Process{
		mu:             sync.RWMutex{},
		TotalNum:       100,
		CurrentNum:     50,
		MaxRepairNum:   10,
		MaxIterNum:     5,
		ValidUTCase:    50,
		End:            false,
		IterationInfos: sync.Map{},
		currentIterNum: 3,
		Cache:          &helper.SafeMap{},
	}

	// 调用 GetRound 方法
	round := p.GetRound()

	// 验证返回值
	expectedRound := "Round#3"
	if round != expectedRound {
		t.Errorf("Expected round to be %s, but got %s", expectedRound, round)
	}
}

// TestGetRepairIterNum 是用于测试 GetRepairIterNum
// generated by Comate
func TestGetRepairIterNum(t *testing.T) {
	p := &Process{
		End:            false,
		IterationInfos: sync.Map{},
		currentIterNum: 0,
		MaxRepairNum:   10,
		MaxIterNum:     20,
	}

	// 测试正常情况，返回正确的迭代次数
	srcMethod := "srcMethod1"
	testMethod := "testMethod1"
	iterInfo := &sync.Map{}
	iterInfo.Store(testMethod, 5)
	p.IterationInfos.Store(srcMethod, iterInfo)

	result := p.GetRepairIterNum(srcMethod, testMethod)
	if result != 5 {
		t.Errorf("Expected 5, got %d", result)
	}

	// 测试不存在的测试方法
	result = p.GetRepairIterNum(srcMethod, "nonExistentMethod")
	if result != 0 {
		t.Errorf("Expected 0, got %d", result)
	}

	// 测试不存在的源方法
	result = p.GetRepairIterNum("nonExistentMethod", testMethod)
	if result != 0 {
		t.Errorf("Expected 0, got %d", result)
	}

	// 测试End为true的情况，返回MaxRepairNum
	p.End = true
	result = p.GetRepairIterNum(srcMethod, testMethod)
	if result != p.MaxRepairNum {
		t.Errorf("Expected %d, got %d", p.MaxRepairNum, result)
	}
}

// TestProcess_GetRepairRound 是用于测试 Process_GetRepairRound
// generated by Comate
func TestProcess_GetRepairRound(t *testing.T) {
	process := &Process{
		MaxRepairNum:   10,
		MaxIterNum:     5,
		IterationInfos: sync.Map{},
		currentIterNum: 3,
	}

	srcMethod := "srcMethod"
	testMethod := "testMethod"

	// 设置迭代信息
	iterInfo := &sync.Map{}
	iterInfo.Store(testMethod, 5)
	process.IterationInfos.Store(srcMethod, iterInfo)

	// 测试正常情况
	expected := "Round#3 - Repair#5"
	actual := process.GetRepairRound(srcMethod, testMethod)
	if expected != actual {
		t.Errorf("Expected %s, but got %s", expected, actual)
	}

	// 测试不存在的测试方法
	expected = "Round#3 - Repair#0"
	actual = process.GetRepairRound(srcMethod, "nonExistentMethod")
	if expected != actual {
		t.Errorf("Expected %s, but got %s", expected, actual)
	}

	// 测试不存在的源方法
	expected = "Round#3 - Repair#0"
	actual = process.GetRepairRound("nonExistentSrcMethod", testMethod)
	if expected != actual {
		t.Errorf("Expected %s, but got %s", expected, actual)
	}

	// 测试End为true的情况
	process.End = true
	expected = "Round#3 - Repair#10"
	actual = process.GetRepairRound(srcMethod, testMethod)
	if expected != actual {
		t.Errorf("Expected %s, but got %s", expected, actual)
	}
}

// TestIsRepairInteractionFinished_EndTrue 是用于测试 IsRepairInteractionFinished_EndTrue
// generated by Comate
func TestIsRepairInteractionFinished_EndTrue(t *testing.T) {
	p := &Process{
		MaxRepairNum: 5,
		End:          true,
	}

	srcMethod := "srcMethod"
	testMethod := "testMethod"

	result := p.IsRepairInteractionFinished(srcMethod, testMethod)
	if !result {
		t.Errorf("Expected IsRepairInteractionFinished to return true when End is true")
	}
}

// TestIsRepairRoundFinished_WhenEndIsTrue 是用于测试 IsRepairRoundFinished_WhenEndIsTrue
// generated by Comate
func TestIsRepairRoundFinished_WhenEndIsTrue(t *testing.T) {
	p := &Process{
		End: true,
	}

	if !p.IsRepairRoundFinished("srcMethod") {
		t.Error("Expected IsRepairRoundFinished to return true when End is true")
	}
}

// TestIsRepairRoundFinished_WhenNoPendingRepair 是用于测试 IsRepairRoundFinished_WhenNoPendingRepair
// generated by Comate
func TestIsRepairRoundFinished_WhenNoPendingRepair(t *testing.T) {
	p := &Process{
		End: false,
	}

	if !p.IsRepairRoundFinished("srcMethod") {
		t.Error("Expected IsRepairRoundFinished to return true when no pending repair")
	}
}

// TestIsRepairRoundFinished_WhenSomeRepairIsNotFinished 是用于测试 IsRepairRoundFinished_WhenSomeRepairIsNotFinished
// generated by Comate
func TestIsRepairRoundFinished_WhenSomeRepairIsNotFinished(t *testing.T) {
	p := &Process{
		End: false,
	}

	iterDetail := &sync.Map{}
	iterDetail.Store("testMethod1", true)
	iterDetail.Store("testMethod2", false)
	p.IterationInfos.Store("srcMethod", iterDetail)

	if p.IsRepairRoundFinished("srcMethod") {
		t.Error("Expected IsRepairRoundFinished to return false when some repairs are not finished")
	}
}

// TestEndRepairInteraction 是用于测试 EndRepairInteraction
// generated by Comate
func TestEndRepairInteraction(t *testing.T) {
	// 初始化 Process 实例
	p := &Process{
		mu: sync.RWMutex{},
	}

	// 初始化 IterationInfos 为一个空的 sync.Map
	p.IterationInfos = sync.Map{}

	// 添加一个测试数据
	srcMethodName := "TestMethod"
	p.IterationInfos.Store(srcMethodName, "TestValue")

	// 调用 EndRepairInteraction 方法
	p.EndRepairInteraction(srcMethodName)

	// 验证数据是否被删除
	_, ok := p.IterationInfos.Load(srcMethodName)
	if ok {
		t.Errorf("Expected %s to be deleted from IterationInfos, but it still exists.", srcMethodName)
	}
}

// TestIncrRepairIteration 是用于测试 IncrRepairIteration
// generated by Comate
func TestIncrRepairIteration(t *testing.T) {
	p := &Process{
		MaxRepairNum:   3,
		IterationInfos: sync.Map{},
	}

	// 测试第一次调用IncrRepairIteration
	iteration := p.IncrRepairIteration("srcMethod1", "testMethod1")
	if iteration != 1 {
		t.Errorf("Expected 1, got %d", iteration)
	}

	// 测试第二次调用IncrRepairIteration
	iteration = p.IncrRepairIteration("srcMethod1", "testMethod1")
	if iteration != 2 {
		t.Errorf("Expected 2, got %d", iteration)
	}

	// 测试第三次调用IncrRepairIteration
	iteration = p.IncrRepairIteration("srcMethod1", "testMethod1")
	if iteration != 3 {
		t.Errorf("Expected 3, got %d", iteration)
	}

	// 测试第四次调用IncrRepairIteration，超过MaxRepairNum
	iteration = p.IncrRepairIteration("srcMethod1", "testMethod1")
	if iteration != 3 {
		t.Errorf("Expected 3, got %d", iteration)
	}

	// 测试不同的srcMethod
	iteration = p.IncrRepairIteration("srcMethod2", "testMethod1")
	if iteration != 1 {
		t.Errorf("Expected 1, got %d", iteration)
	}

	// 测试不同的testMethod
	iteration = p.IncrRepairIteration("srcMethod1", "testMethod2")
	if iteration != 1 {
		t.Errorf("Expected 1, got %d", iteration)
	}
}

// TestIncrIteration 是用于测试 IncrIteration
// generated by Comate
func TestIncrIteration(t *testing.T) {
	p := &Process{
		currentIterNum: 0,
	}

	srcMethodName := "TestMethod"

	// 初始化currentIterNum为0，并调用IncrIteration
	expectedIterationNum := 1
	actualIterationNum := p.IncrIteration(srcMethodName)
	if actualIterationNum != expectedIterationNum {
		t.Errorf("Expected currentIterNum to be %d, but got %d", expectedIterationNum, actualIterationNum)
	}

	// 验证IterationInfos中是否存在srcMethodName
	_, ok := p.IterationInfos.Load(srcMethodName)
	if !ok {
		t.Errorf("Expected IterationInfos to contain %s", srcMethodName)
	}

	// 再次调用IncrIteration，验证currentIterNum是否增加
	expectedIterationNum = 2
	actualIterationNum = p.IncrIteration(srcMethodName)
	if actualIterationNum != expectedIterationNum {
		t.Errorf("Expected currentIterNum to be %d, but got %d", expectedIterationNum, actualIterationNum)
	}
}
