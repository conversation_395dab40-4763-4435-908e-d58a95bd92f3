package models

import (
	"bytes"
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
)

type JsonRpcData struct {
	JsonRpc string     `json:"jsonrpc"`
	Id      int        `json:"id"`
	Method  string     `json:"method"`
	Params  *RpcParams `json:"params"`
}

func NewJsonRpcData() *JsonRpcData {
	return &JsonRpcData{
		JsonRpc: "2.0",
		Id:      -10,
		Method:  "",
		Params:  new(RpcParams),
	}
}

// NewJsonRpcDataWithStatusFailed 创建一个JsonRpcData指针，并设置其JsonRpc为"2.0"，Id为-10，Method为空字符串，Params为新的RpcParams指针。
// 函数的参数message用于设置返回的JsonRpcData的Params中的Message字段。
// 函数返回指向JsonRpcData的指针。
func NewJsonRpcDataWithStatusFailed(message string) *JsonRpcData {
	data := &JsonRpcData{
		JsonRpc: "2.0",
		Id:      -10,
		Method:  "",
		Params:  new(RpcParams),
	}
	data.Params.Status = constant.FAILED
	data.Params.Message = message
	return data
}

func (receiver *JsonRpcData) Print() {
	dataBytes, _ := json.Marshal(receiver)
	dataBytes = bytes.ReplaceAll(dataBytes, []byte("\r\n"), []byte("\n"))
	header := fmt.Sprintf("Content-Length: %d\r\n\r\n", len(dataBytes))

	fmt.Printf("%s%s\n", header, string(dataBytes))
	logger.Info("[SEND DATA]: %s", string(dataBytes))
}

type RpcParams struct {
	Process            *TaskProcess     `json:"process"`
	UtFileResult       []*UtFileResult  `json:"utFileResult"`
	TaskProcessCovDate *TaskProcessData `json:"taskProcessCovDate,omitempty"`
	TaskSummary        *TaskSummary     `json:"taskSummary"`
	Status             string           `json:"status"`
	Message            string           `json:"message"`
}

type TaskProcess struct {
	TaskStart      string             `json:"taskStart"`
	TaskExpect     string             `json:"taskExpect"`
	FirstGenerate  *FirstGenerate     `json:"firstGenerate"`
	EnvCheck       *EnvironmentData   `json:"envCheck"`
	FrameworkCheck *FrameworkCheck    `json:"frameworkCheck"`
	SrcFileScan    *SrcFileScanResult `json:"srcFileScan"`
	LoadingMsg     string             `json:"loadingMsg"`
}

type FrameworkCheck struct {
	State         string       `json:"state"` // 成功、失败、修复
	Tools         []Tool       `json:"tools"`
	AdjustedFiles []FileDetail `json:"adjustedFiles"`
}

type Tool struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

type FileDetail struct {
	FilePath    string `json:"filePath"`
	FileContent string `json:"fileContent"`
}

type SrcFileScanResult struct {
	TableHeader  string   `json:"tableHeader"` // 侧边栏下拉列表的表头名
	State        string   `json:"state"`
	SrcFilesName []string `json:"srcFilesName"`
	Summary      string   `json:"summary"`
}

type EnvironmentData struct {
	EnvState string       `json:"envState"` //验证中RUNNING，环境可用USABLE、环境不可用UNUSABLE。变成结束状态后，execute必须给完整结构
	ErrorMsg *EnvErrorMsg `json:"errorMsg"`
}

type EnvErrorMsg struct {
	Msg           string `json:"msg"`
	RepairSuggest string `json:"repairSuggest"`
}

// TaskProcessData 任务执行过程中的覆盖率信息变更，for Icode CR 场景
type TaskProcessData struct {
	OldCoverLine     int    `json:"oldCoverLine"`
	OldTotalLine     int    `json:"oldTotalLine"`
	OldCoverage      string `json:"oldCoverage"`
	CurrentCoverage  string `json:"currentCoverage"`
	CurrentCoverLine int    `json:"currentCoverLine"`
	CurrentTotalLine int    `json:"currentTotalLine"`
	IncreaseRate     string `json:"increaseRate"`
}

type FirstGenerate struct {
	State           string   `json:"state"`
	TestFilePath    string   `json:"testFilePath"`
	TestFileContent string   `json:"testFileContent"`
	CaseName        []string `json:"caseName"`
	Summary         string   `json:"summary"`
}

type UtFileResult struct {
	State                      string `json:"state"`
	TestFilePath               string `json:"testFilePath"`
	SrcFilePath                string `json:"srcFilePath"`
	TestFileContent            string `json:"testFileContent"`
	TestFileContentOnlySuccess string `json:"testFileContentOnlySuccess"`
	Meta                       struct {
		ProcessedSrcMethodName      []string `json:"processedSrcMethodName"`
		ProcessedSrcMethodCount     int      `json:"processedSrcMethodCount"`
		GeneratedTestMethodCount    int      `json:"generatedTestMethodCount"`
		GeneratedRunPassMethodCount int      `json:"generatedRunPassCaseCount"`
		GeneratedTestMethodName     []string `json:"generatedTestMethodName"`
		FileCoverage                string   `json:"fileCoverage"`
	} `json:"meta"`
	Bugs []GenUnitTestCase `json:"bugs"`
}

type TaskSummary struct {
	SrcMethodCount           int    `json:"srcMethodCount"`
	ProcessedSrcMethodCount  int    `json:"processedSrcMethodCount"`
	SucceedSrcMethodCount    int    `json:"succeedSrcMethodCount"`
	GeneratedTestMethodCount int    `json:"generatedTestMethodCount"`
	OldCoverage              string `json:"oldCoverage"`
	Coverage                 string `json:"coverage"`
	IncreaseRate             string `json:"increaseRate"`

	OldIncCoverage          string `json:"oldIncCoverage"`
	CurrentIncCoverage      string `json:"currentIncCoverage"`
	IncCoverageIncreaseRate string `json:"incCoverageIncreaseRate"`

	TimeElapsed int64 `json:"timeElapsed"`
}

func (receiver *TaskSummary) String() string {
	b, _ := json.Marshal(receiver)
	return string(b)
}
