package models

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
	"sync"
)

// RepairInfo 修复失败case
type RepairInfo struct {
	CaseInfo  CaseInfo
	RunResult *RunResult
}

// CaseInfo 测试用例信息
type CaseInfo struct {
	Language        string
	TestFilePath    string
	TestFileContent string
	TestClassName   string
	TestMethodName  string
	TestMethodBody  string

	RepairResult RepairResult // 修复工具
	MongoID      string
	RepairId     string

	UnderTest UnderTestInfo
}

// UnderTestInfo 被测信息, 发送至LLMCall管道
type UnderTestInfo struct {
	ID string // 唯一标识,由被测文件相对路径和方法名组成，格式如：文件#方法签名
	// 记录进度
	Process  *Process
	Language string

	TestFramework string

	PackageName   string
	ClassName     string
	ClassFullName string
	FuncName      string
	FuncID        string

	FilePath         string
	RelativeFilePath string
	TestFilePath     string

	TestFileContext   interface{}
	TargetFileContext interface{}
	TargetMethod      interface{}

	SucceedTestMethods []string // 已有测试用例

	LLMGenAction   gen.UtGen // 用于模型推理，根据不同的阶段action不同
	IterationTimes int       // 记录第几次迭代，记录修复时生成
}

func (u *UnderTestInfo) GetMethodName() string {
	if u.Language == "go" {
		return u.TargetMethod.(*golang.Method).Identifier
	} else if u.Language == "java" {
		return u.TargetMethod.(*java.Method).Identifier

	}
	return ""
}

func (u *UnderTestInfo) GetJavaMethod() *java.Method {
	return u.TargetMethod.(*java.Method)
}

func (u *UnderTestInfo) GetGoMethod() *golang.Method {
	return u.TargetMethod.(*golang.Method)
}

func (u *UnderTestInfo) GetTargetMethod() *Method {
	if u.Language == constant.LangGo {
		tm := u.GetGoMethod()
		return &Method{
			Identifier: tm.Identifier,
			StartLine:  tm.Block.StartLine,
			EndLine:    tm.Block.EndLine,
			Body:       tm.Block.Content,
		}
	} else if u.Language == constant.LangJava {
		tm := u.GetJavaMethod()
		return &Method{
			Identifier: tm.Identifier,
			StartLine:  tm.StartLine,
			EndLine:    tm.EndLine,
			Body:       tm.Body,
		}
	}
	return nil
}

type CommonMethod struct {
	MethodName string
	StartLine  int
	EndLine    int
}

type TestFile struct {
	TestFilePath    string
	TestFileContent string
	TestClassName   string
}

// RepairInput 修复失败case所需的入参
type RepairInput struct {
	TestFramework   string
	LanguageVersion string // JDK版本号，例如1.8, 17等
	SrcFileContent  string
	TestFileContent string
	TestFilePath    string
	TestFileName    string
	TestMethodName  string
	RunResult       *RunResult
	RepairId        string
	MethodId        string
	MongoID         string
	ErrorType       ErrorType
}

type ErrorType string // 修复失败case的错误类型

func (e ErrorType) Value() string {
	return fmt.Sprintf("%v", e)
}

type Process struct {
	mu sync.RWMutex

	TotalNum   int
	CurrentNum int

	MaxRepairNum int
	MaxIterNum   int
	ValidUTCase  int

	End                 bool // 覆盖率达标后，直接置为true
	IterationInfos      sync.Map
	currentIterNum      int
	currentIterExecuted bool

	Cache *helper.SafeMap // 记录在相关将要发生和在发生的任务数
}

func (p *Process) GetFileProcess() string {
	return fmt.Sprintf("(File %d/%d)", p.CurrentNum, p.TotalNum)
}

func (p *Process) EndProcess() {
	p.End = true
}

func (p *Process) SetCurrentIterExecuted() {
	p.currentIterExecuted = true
}

func (p *Process) SetIterNum(srcMethod string, curIterationNum int) {
	if curIterationNum > p.MaxIterNum {
		curIterationNum = p.MaxIterNum
	}

	p.currentIterNum = curIterationNum

}

func (p *Process) GetRepairIterNum(srcMethod string, testMethod string) int {
	if p.End {
		return p.MaxRepairNum
	}

	val, exists := p.IterationInfos.Load(srcMethod)
	if !exists {
		return 0
	}

	iterInfo := val.(*sync.Map)

	if value, testExists := iterInfo.Load(testMethod); testExists {
		return value.(int)
	}

	return 0
}

func (p *Process) GetRound() string {
	return fmt.Sprintf("Round#%d", p.currentIterNum)
}

func (p *Process) GetRepairRound(srcMethod string, testMethod string) string {
	return fmt.Sprintf("Round#%d - Repair#%d", p.currentIterNum, p.GetRepairIterNum(srcMethod, testMethod))
}

func (p *Process) GetIterNum(srcMethod string, testMethod string) int {
	return p.currentIterNum
}

func (p *Process) IsInteractionFinished(srcMethod string) bool {
	return p.End || p.GetIterNum(srcMethod, "") >= p.MaxIterNum
}

func (p *Process) IsNeedRepair(srcMethod string, testMethod string) bool {
	if p.End {
		return false
	}
	val, ok := p.IterationInfos.Load(srcMethod)
	if !ok {
		// 说明没有待修复的测试方法
		return false
	}
	iterDetail := val.(*sync.Map)
	_, testMethodExists := iterDetail.Load(testMethod)
	return testMethodExists
}

// IsRepairInteractionFinished 判断修复交互是否完成
func (p *Process) IsRepairInteractionFinished(srcMethod string, testMethod string) bool {
	return p.End || p.GetRepairIterNum(srcMethod, testMethod) >= p.MaxRepairNum
}

func (p *Process) IsRepairRoundFinished(srcMethod string) bool {
	if !p.currentIterExecuted {
		return false
	}

	if p.End {
		return true
	}

	val, ok := p.IterationInfos.Load(srcMethod)
	if !ok {
		// 说明没有待修复的测试方法
		return true
	}
	iterDetail := val.(*sync.Map)
	// 遍历所有待修复的测试方法，check所有的测试方法都完成修复
	repairingNum := 0
	iterDetail.Range(func(key, value any) bool {
		logger.Info("REPAIR %s, %d", key, value)
		repairingNum++
		return true
	})
	logger.Warn("[%s] RepairingNum: %d", srcMethod, repairingNum)
	return repairingNum == 0
}

func (p *Process) InitTestMethodIteration(srcMethodName string, testMethodName string) {
	val, _ := p.IterationInfos.LoadOrStore(srcMethodName, new(sync.Map))
	testMethods := val.(*sync.Map)
	testMethods.Store(testMethodName, 0)
	p.IterationInfos.Store(srcMethodName, testMethods)
}

func (p *Process) DeleteTestMethodIteration(srcMethodName string, testMethodName string) {
	val, exists := p.IterationInfos.Load(srcMethodName)
	if exists {
		testMethods := val.(*sync.Map)
		testMethods.Delete(testMethodName)
		p.IterationInfos.Store(srcMethodName, testMethods)
	}
}

func (p *Process) EndRepairInteraction(srcMethodName string) {
	p.IterationInfos.Delete(srcMethodName)
}

func (p *Process) IncrRepairIteration(srcMethodName string, testMethodName string) int {
	val, _ := p.IterationInfos.LoadOrStore(srcMethodName, new(sync.Map))
	testMethods := val.(*sync.Map)
	currentValue, _ := testMethods.LoadOrStore(testMethodName, 0)
	currentIter := currentValue.(int)

	if currentIter < p.MaxRepairNum {
		currentIter += 1
	}

	testMethods.Store(testMethodName, currentIter)
	return currentIter
}

func (p *Process) IncrIteration(srcMethodName string) int {
	p.currentIterNum += 1
	// 重新初始化修复的迭代
	p.IterationInfos.Store(srcMethodName, new(sync.Map))
	p.currentIterExecuted = false
	return p.currentIterNum
}
