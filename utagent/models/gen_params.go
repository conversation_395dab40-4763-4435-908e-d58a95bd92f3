package models

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
)

type GenParam struct {
	InputParam *InputParam
	// 非用户参数
	SrcFileMethods map[string]map[int]int // 待生成用例的源文件和方法起始行

}

func NewGenParam(base *InputParam) *GenParam {
	return &GenParam{
		InputParam: base,
	}
}

func (p *GenParam) Init(genCmd *cobra.Command) {
	p.InputParam.Init(genCmd)
	cmdSet := genCmd.Flags()
	cmdSet.Parse(os.Args[1:])

	if p.InputParam.Debug {
		config.SetDebug()
		logger.DebugMode()
	}
	// 设置单测服务endpoint
	if p.InputParam.Endpoint != "" {
		config.SetServerEndpoint(p.InputParam.Endpoint)
	}
}

const (
	// 保留用例策略
	SaveRunPassCaseStrategy = "runPass"
	saveCanRunCaseStrategy  = "canRun"
)

func (p *GenParam) CollectSrcMethods() error {
	srcFiles := p.InputParam.GetIncludeFilesAndSrcFiles()

	if len(srcFiles) == 0 {
		return fmt.Errorf("根据提供的include和exclude信息, 未收集到需要生成用例的源文件")
	}

	p.SrcFileMethods = make(map[string]map[int]int)
	for _, file := range srcFiles {
		if helper.IsValidSourceFile(file, p.InputParam.GetWorkDirLanguage()) {
			p.SrcFileMethods[file] = nil
		}
	}

	return nil
}

func (p *GenParam) PrintParams() {
	p.InputParam.PrintParams()
}

func (p *GenParam) ParamValid() map[string]string {
	return p.InputParam.ParamValid()
}

// FormatCovFilter helper.AntMatcher 格式化include和exclude, 以绝对路径格式返回
func FormatCovFilter(workDir string, include string, exclude string) *helper.AntMatcher {
	covInclude := make([]string, 0)
	covExclude := make([]string, 0)
	for _, in := range helper.StringSplit(include, ",") {
		in = strings.TrimPrefix(strings.TrimPrefix(strings.TrimPrefix(in, "./"), ".\\"), ".")
		if strings.HasSuffix(in, string(filepath.Separator)) {
			in = in + "**"
		} else if !strings.Contains(in, ".") && !strings.HasSuffix(in, "*") { // 如果路径中不包括. 则认为当前路径是目录而不是文件
			in = in + string(filepath.Separator) + "**"
		} else if in == "" {
			continue
		}
		if !strings.HasPrefix(in, workDir) {
			in = filepath.Join(workDir, in)
		}
		covInclude = append(covInclude, in)
	}

	for _, ex := range helper.StringSplit(exclude, ",") {
		ex = strings.TrimPrefix(strings.TrimPrefix(strings.TrimPrefix(ex, "./"), ".\\"), ".")
		if strings.HasSuffix(ex, string(filepath.Separator)) {
			ex = ex + "**"
		} else if !strings.Contains(ex, ".") { // 如果路径中不包括. 则认为当前路径是目录而不是文件
			ex = ex + string(filepath.Separator) + "**"
		} else if strings.HasPrefix(ex, "*") && strings.Contains(ex, ".") { // 适配cov平台默认值eg.*test.go、*Test*.java
			ex = "**" + string(filepath.Separator) + ex
		} else if ex == "" {
			continue
		}

		if !strings.HasPrefix(ex, workDir) {
			ex = filepath.Join(workDir, ex)
		}
		covExclude = append(covExclude, ex)
	}

	return helper.NewAntMatcher(covInclude, covExclude)
}

func (p *GenParam) GetDesiredCoverage() int {
	return p.InputParam.DesiredCoverage
}
func (p *GenParam) GetWorkDir() string {
	return p.InputParam.WorkDir
}

func (p *GenParam) GetLang() string {
	return p.InputParam.GetWorkDirLanguage()
}

func (p *GenParam) GetRunCmd() string {
	return p.InputParam.RunCmd
}

func (p *GenParam) GetIterationMax() int {
	return p.InputParam.IterationMax
}

func (p *GenParam) GetRepairIterMax() int {
	return p.InputParam.RepairIterMax
}

func (p *GenParam) GetRepairLLMModel() *llm.ModelConf {
	return p.InputParam.RepairLLMModel
}

func (p *GenParam) GetSrcMethods() map[string]map[int]int {
	if len(p.SrcFileMethods) == 0 {
		p.CollectSrcMethods()
	}
	return p.SrcFileMethods
}

func (p *GenParam) GetSrcMethodsCount() int {
	count := 0
	for _, methods := range p.SrcFileMethods {
		count += len(methods)
	}
	return count
}

func (p *GenParam) GetTrigger() string {
	return p.InputParam.Trigger
}

func (p *GenParam) GetType() string {
	return constant.GenerateForGen
}

func (p *GenParam) GetUnitFramework() string {
	return p.InputParam.UnitFramework
}

func (p *GenParam) GetLanguageVersion() string {
	return p.InputParam.LanguageVersion
}

func (p *GenParam) GetResultPath() string {
	return p.InputParam.ResultPath
}

func (p *GenParam) GetCovFilePath() string {
	return p.InputParam.CovFile
}

func (p *GenParam) GetDiffs() map[string][]int {
	return nil
}

func (p *GenParam) GetSaveCaseStrategy() string {
	return p.InputParam.SaveCaseStrategy
}

func (p *GenParam) GetUserName() string {
	return p.InputParam.UserName
}

func (p *GenParam) GetLicense() string {
	return p.InputParam.License
}

func (p *GenParam) IsAutoModifyBuildScript() bool {
	return p.InputParam.AutoModifyBuildScript
}
