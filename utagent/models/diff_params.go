package models

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
	javaparser "icode.baidu.com/baidu/cov/smartUT-parser/java"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
)

type DiffParam struct {
	InputParam *InputParam

	BaseCommitID   string                 // 基准commitId
	SrcFileMethods map[string]map[int]int // 源文件方法列表

	Diffs      map[string][]int // 增量文件新增行列表
	ModuleName string           // 模块名，解析go.mod获取
}

func NewDiffParam(base *InputParam) *DiffParam {
	return &DiffParam{
		InputParam: base,
	}
}

func (dp *DiffParam) Init(diffCmd *cobra.Command) {
	dp.InputParam.Init(diffCmd)
	cmdSet := diffCmd.Flags()
	// diff生成参数,
	cmdSet.StringVarP(&dp.BaseCommitID, "baseCommitID", "b", "", "增量生成单测的基准commitId，仅支持一个输入")
	cmdSet.Parse(os.Args[1:])

	if dp.InputParam.Debug {
		config.SetDebug()
		logger.DebugMode()
	}

	// 设置单测服务endpoint
	if dp.InputParam.Endpoint != "" {
		config.SetServerEndpoint(dp.InputParam.Endpoint)
	}
}

func (dp *DiffParam) ParamValid() map[string]string {
	errorParams := dp.InputParam.ParamValid()
	if len(errorParams) != 0 {
		return errorParams
	}

	if !tools.IsGitRepo(dp.InputParam.WorkDir) {
		errorParams["baseCommitId"] = fmt.Sprintf("当前路径%s不在git控制下", dp.InputParam.WorkDir)
		return errorParams
	}

	if dp.BaseCommitID != "" {
		if !tools.IsValidCommitID(dp.InputParam.WorkDir, dp.BaseCommitID) {
			errorParams["baseCommitId"] = "baseCommitId在路径" + dp.InputParam.WorkDir + "下不存在，请检查baseCommitId是否正确"
			return errorParams
		}
	}
	dp.GetLang()
	if dp.GetLang() == constant.LangGo {
		dp.ModuleName = tools.GetGoModuleNameByWorkDir(dp.InputParam.WorkDir)
	}
	return nil
}

func (dp *DiffParam) PrintParams() {
	dp.InputParam.PrintParams()
	logger.InfoT(1, "- baseCommitID        : %s", dp.BaseCommitID)
}

// CollectSrcMethods 收集需要生成用例的源文件
func (dp *DiffParam) CollectSrcMethods() error {
	if dp.InputParam.Include == "" {
		dp.InputParam.Include = dp.InputParam.WorkDir
	}
	if dp.GetLang() == constant.LangGo && dp.ModuleName == "" {
		dp.ModuleName = tools.GetGoModuleNameByWorkDir(dp.InputParam.WorkDir)
	}
	antMatcher := FormatCovFilter(dp.InputParam.WorkDir, dp.InputParam.Include, dp.InputParam.Exclude)
	dp.SrcFileMethods = make(map[string]map[int]int)
	dp.Diffs = make(map[string][]int)

	untrackedFiles := tools.GetUntrackedFiles(dp.InputParam.WorkDir)

	diffFiles, gitBase := tools.GetDiffFiles(dp.InputParam.WorkDir, dp.BaseCommitID,
		"git -c core.autocrlf=input diff "+dp.BaseCommitID+" --name-only")
	diffCacheFiles, _ := tools.GetDiffFiles(dp.InputParam.WorkDir, dp.BaseCommitID,
		"git diff "+dp.BaseCommitID+" --cached --name-only")
	diffFiles = append(diffFiles, diffCacheFiles...)
	tmpCache := make(map[string]bool)
	if len(diffFiles) != 0 {
		for _, file := range diffFiles {
			if tmpCache[file] {
				continue
			}
			if !(helper.IsFileExist(file) && antMatcher.Match(file)) {
				continue
			}
			tmpCache[file] = true
			if helper.IsValidSourceFile(file, dp.InputParam.Language) {
				toContinue := dp.CollectSrcMethodsByFileAndDiff(file, dp.InputParam.WorkDir, dp.BaseCommitID, gitBase)
				if toContinue && !utils.InSlice(untrackedFiles, file) {
					untrackedFiles = append(untrackedFiles, file)
				}
			}
		}
	}

	if len(untrackedFiles) != 0 {
		for _, file := range untrackedFiles {
			if antMatcher.Match(file) && helper.IsValidSourceFile(file, dp.InputParam.Language) {
				dp.CollectSrcMethodsByFile(file)
			}
		}
	}

	return nil
}

func (dp *DiffParam) CollectSrcMethodsByFile(file string) {
	if dp.InputParam.Language == constant.LangJava {
		parser, err := javaparser.NewFileParserWithFilepath(file)
		if err != nil {
			logger.DebugT(1, "解析文件失败: %s", err)
		}
		fileContext := parser.Parse(nil)
		if fileContext.MainClass == nil || len(fileContext.MainClass.Methods) == 0 {
			logger.DebugT(1, "文件%s没有需要生成用例的方法", file)
			return
		}

		dp.Diffs[strings.Join([]string{fileContext.Pkg, fileContext.MainClass.Identifier}, ".")] =
			helper.GenerateSlice(len(strings.Split(fileContext.FileContent, "\n")))

		methods := make(map[int]int)
		for _, method := range fileContext.MainClass.Methods {
			if strings.Contains(method.Modifier, "@Test") {
				return
			}
			if !method.IsPrivateOrProtected() {
				methods[method.StartLine] = method.EndLine
			}
		}

		if len(methods) != 0 {
			dp.SrcFileMethods[file] = methods
		}
	} else if dp.InputParam.Language == constant.LangGo {
		parser, err := golang.NewFileParserWithFilepath(file)
		if err != nil {
			logger.DebugT(1, "解析文件失败: %s", err)
		}
		fileContext := parser.Parse()
		if len(fileContext.Methods) == 0 {
			logger.DebugT(1, "文件%s没有需要生成用例的方法", file)
			return
		}

		realPath := helper.GetRelativePath(file, dp.InputParam.WorkDir)
		dp.Diffs[strings.Join([]string{dp.ModuleName, realPath}, string(filepath.Separator))] =
			helper.GenerateSlice(len(strings.Split(fileContext.FileContent, "\n")))

		methods := make(map[int]int)
		for _, method := range fileContext.Methods {

			if !method.IsTestCase {
				methods[method.Block.StartLine] = method.Block.EndLine
			}
		}

		if len(methods) != 0 {
			dp.SrcFileMethods[file] = methods
		}
	}

}

func (dp *DiffParam) CollectSrcMethodsByFileAndDiff(file string, workDir string, baseCommitID string, gitBase string) bool {
	diffContent := tools.GetFileDiffContent(workDir, baseCommitID, file)
	if diffContent == "" {
		return true
	}
	fileDiffs := tools.ParseDiffByDiffLog(diffContent, nil, nil)
	if len(fileDiffs) == 0 {
		return false
	}

	if dp.InputParam.Language == constant.LangJava {
		for file, diff := range fileDiffs {
			file = filepath.Join(gitBase, file)
			if len(diff.DiffLines) == 0 {
				continue
			}
			dp.processJavaDiff(diff, file)
		}

	} else if dp.InputParam.Language == constant.LangGo {
		for file, diff := range fileDiffs {
			file = filepath.Join(gitBase, file)
			if len(diff.DiffLines) == 0 {
				continue
			}
			dp.processGoDiff(diff, file)
		}
	}

	return false
}

func (dp *DiffParam) processGoDiff(diff tools.DiffInfo, file string) {
	var fileContext *golang.FileContext
	parser, err := golang.NewFileParserWithFilepath(file)
	if err != nil {
		logger.DebugT(1, "解析文件失败: %s", err)
		return
	}

	fileContext = parser.Parse()
	if fileContext.Methods == nil {
		logger.DebugT(1, "文件%s没有需要生成用例的方法", file)
		return
	}
	realPath := helper.GetRelativePath(file, dp.InputParam.WorkDir)
	dp.Diffs[strings.Join([]string{dp.ModuleName, realPath}, string(filepath.Separator))] = diff.DiffLines
	if diff.DiffType == 2 {
		for _, method := range fileContext.Methods {
			if dp.SrcFileMethods[file] == nil {
				dp.SrcFileMethods[file] = make(map[int]int)
			}
			dp.SrcFileMethods[file][method.Block.StartLine] = method.Block.EndLine
		}
	} else if len(diff.DiffLines) != 0 {
		methods := map[int]int{}
		diffLen := len(diff.DiffLines)
		i := 0
		sortedMethods := fileContext.Methods
		sort.Slice(sortedMethods, func(i, j int) bool {
			return sortedMethods[i].Block.StartLine < sortedMethods[j].Block.StartLine
		})
		for _, method := range sortedMethods {
			if !method.IsTestCase {

				for i < diffLen {
					if diff.DiffLines[i] < method.Block.StartLine+1 {
						i++
					} else if diff.DiffLines[i] >= method.Block.StartLine+1 && diff.DiffLines[i] <= method.Block.EndLine+1 {
						methods[method.Block.StartLine] = method.Block.EndLine
						i++
					} else if diff.DiffLines[i] > method.Block.EndLine+1 {
						break
					}
				}

				if i >= diffLen {
					break
				}
			}
		}
		if len(methods) > 0 {
			// 存在有效diff的方法
			dp.SrcFileMethods[file] = methods
		}
	}
}

func (dp *DiffParam) processJavaDiff(diff tools.DiffInfo, file string) {
	var fileContext *javaparser.FileContext
	parser, err := javaparser.NewFileParserWithFilepath(file)
	if err != nil {
		logger.DebugT(1, "解析文件失败: %s", err)
		return
	}

	fileContext = parser.Parse(nil)
	if fileContext.MainClass == nil || len(fileContext.MainClass.Methods) == 0 {
		logger.DebugT(1, "文件%s没有需要生成用例的方法", file)
		return
	}

	dp.Diffs[strings.Join([]string{fileContext.Pkg, fileContext.MainClass.Identifier}, ".")] = diff.DiffLines
	if diff.DiffType == 2 {
		for _, method := range fileContext.MainClass.Methods {
			if dp.SrcFileMethods[file] == nil {
				dp.SrcFileMethods[file] = make(map[int]int)
			}
			dp.SrcFileMethods[file][method.StartLine] = method.EndLine
		}
	} else if len(diff.DiffLines) != 0 {
		methods := map[int]int{}
		diffLen := len(diff.DiffLines)
		i := 0
		sortedMethods := fileContext.MainClass.Methods
		sort.Slice(sortedMethods, func(i, j int) bool {
			return sortedMethods[i].StartLine < sortedMethods[j].StartLine
		})
		for _, method := range sortedMethods {
			if !method.IsPrivateOrProtected() {
				if strings.Contains(method.Modifier, "@Test") {
					return
				}
				for i < diffLen {
					if diff.DiffLines[i] < method.StartLine+1 {
						i++
					} else if diff.DiffLines[i] >= method.StartLine+1 && diff.DiffLines[i] <= method.EndLine+1 {
						methods[method.StartLine] = method.EndLine
						i++
					} else if diff.DiffLines[i] > method.EndLine+1 {
						break
					}
				}

				if i >= diffLen {
					break
				}
			}
		}
		if len(methods) > 0 {
			// 存在有效diff的方法
			dp.SrcFileMethods[file] = methods
		}
	}
}

func (dp *DiffParam) GetDesiredCoverage() int {
	return dp.InputParam.DesiredCoverage
}
func (dp *DiffParam) GetWorkDir() string {
	return dp.InputParam.WorkDir
}

func (dp *DiffParam) GetLang() string {
	return dp.InputParam.GetWorkDirLanguage()
}

func (dp *DiffParam) GetRunCmd() string {
	return dp.InputParam.RunCmd
}

func (dp *DiffParam) GetIterationMax() int {
	return dp.InputParam.IterationMax
}

func (dp *DiffParam) GetRepairLLMModel() *llm.ModelConf {
	return dp.InputParam.RepairLLMModel
}

func (dp *DiffParam) GetSrcMethods() map[string]map[int]int {
	return dp.SrcFileMethods
}

func (dp *DiffParam) GetSrcMethodsCount() int {
	count := 0
	for _, methods := range dp.SrcFileMethods {
		count += len(methods)
	}
	return count
}

func (dp *DiffParam) GetTrigger() string {
	return dp.InputParam.Trigger
}

func (dp *DiffParam) GetType() string {
	return constant.GenerateForDiff
}

func (dp *DiffParam) GetUnitFramework() string {
	return dp.InputParam.UnitFramework
}

func (dp *DiffParam) GetLanguageVersion() string {
	return dp.InputParam.LanguageVersion
}

func (dp *DiffParam) GetResultPath() string {
	return dp.InputParam.ResultPath
}

func (dp *DiffParam) GetCovFilePath() string {
	return dp.InputParam.CovFile
}

func (dp *DiffParam) GetDiffs() map[string][]int {
	return dp.Diffs
}

func (dp *DiffParam) GetSaveCaseStrategy() string {
	return dp.InputParam.SaveCaseStrategy
}

func (dp *DiffParam) GetUserName() string {
	return dp.InputParam.UserName
}

func (dp *DiffParam) GetLicense() string {
	return dp.InputParam.License
}

func (dp *DiffParam) GetRepairIterMax() int {
	return dp.InputParam.RepairIterMax
}

func (dp *DiffParam) IsAutoModifyBuildScript() bool {
	return dp.InputParam.AutoModifyBuildScript
}
