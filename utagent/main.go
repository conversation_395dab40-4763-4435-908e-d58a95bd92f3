package utagent

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/filter"
	"os"
	"os/signal"
	"runtime/debug"
	"strings"
	"sync"
	"syscall"

	"github.com/sirupsen/logrus"

	"icode.baidu.com/baidu/cov/iUT/config/conf_parser"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/notify"

	"icode.baidu.com/baidu/cov/iUT/utagent/golang"
	"icode.baidu.com/baidu/cov/iUT/utagent/java"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"icode.baidu.com/baidu/cov/iUT/utagent/mongo"
)

const (
	GenerateQueueSize = 10000
	ValidQueueSize    = 50
	RepairQueueSize   = 10000
)

type UTGenerator struct {
	WorkDir         string        //工作目录
	DesiredCoverage int           // 目标覆盖率
	param           models.Params // 参数

	QueueForLLMCall    chan any // 待发送模型进行推理的队列
	QueueForValidation chan any // 待发送验证的队列
	QueueForRepair     chan any // 待修复的队列

	UTAgent UTAgent

	wg sync.WaitGroup

	CodeRepoInfo         *models.CodeRepo // 代码仓库信息
	cache                *helper.SafeMap
	ideNotifier          *notify.NotifierForIDE
	cancel               context.CancelFunc
	isSaas               bool
	sensitiveWordsFilter *filter.SensitiveWordsChecker
}

func NewUTGenerator(param models.Params) *UTGenerator {
	generator := &UTGenerator{
		WorkDir:              param.GetWorkDir(),
		DesiredCoverage:      param.GetDesiredCoverage(),
		cache:                helper.NewSafeMap(),
		param:                param,
		sensitiveWordsFilter: filter.NewSensitiveWordsChecker(param.GetLang(), param.GetWorkDir()),
	}

	if param.GetTrigger() == constant.IDETrigger || param.GetTrigger() == constant.ICODETrigger {
		// IDE状态通知
		generator.ideNotifier = notify.NewNotifierForIDE()
	}

	if param.GetLicense() != "" || !httputil.IsBaiduInnerIp() {
		generator.isSaas = true
	}

	// 外网环境check模型配置是否有效
	// 工具单独运行时检查，如POC场景下检查
	// 其他场景下不检查
	if generator.isSaas && param.GetTrigger() == constant.TOOLTrigger {
		generator.CheckConfigValid()
	}
	return generator
}

func (generator *UTGenerator) EnvPrepare(param models.Params) *UTGenerator {
	if len(param.GetSrcMethods()) == 0 {
		msg := "未收集到任何有效的被测文件，请检查配置"
		logger.Error(msg)
		generator.notifyTaskStatus(nil, constant.SKIPPED, msg)
		os.Exit(0)
	}
	mongo.Init()
	var err error
	switch param.GetLang() {
	case constant.LangJava:
		if param.GetTrigger() == constant.IDETrigger || param.GetTrigger() == constant.ICODETrigger {
			logger.Info("【PATH】:%s", os.Getenv("PATH"))
			logger.Info("【MAVEN_HOME】:%s", os.Getenv("MAVEN_HOME"))
			logger.Info("【M2_HOME】:%s", os.Getenv("M2_HOME"))
			logger.Info("【JAVA_HOME】:%s", os.Getenv("JAVA_HOME"))
			logger.Info("【HOME】:%s", os.Getenv("HOME"))
		}
		generator.UTAgent, err = java.NewUTAgent(param, generator.cache, generator.CodeRepoInfo)
		if err != nil {
			logger.Error("Java UTAgent 初始化异常: %s", err)
			generator.notifyTaskStatus(nil, constant.FAILED, err.Error())
			os.Exit(0)
		}
	case constant.LangGo:
		generator.UTAgent, err = golang.NewUTAgent(param, generator.cache, generator.CodeRepoInfo)
		if err != nil {
			logger.Error("go UTAgent 初始化异常: %s", err)
			generator.notifyTaskStatus(nil, constant.FAILED, err.Error())
			os.Exit(0)
		}
	default:
		panic("not support language")
	}
	//  全局变量赋值
	generator.UTAgent.PrepareAgentClient()
	if generator.CodeRepoInfo != nil {
		constant.RepoName = generator.CodeRepoInfo.RepoName
	}
	if constant.RepoName == "" {
		constant.RepoName = param.GetWorkDir()
	}
	constant.WorkDir = param.GetWorkDir()
	return generator
}

func (generator *UTGenerator) handleSignal() {
	sigs := make(chan os.Signal, 1)
	// 将信号通道绑定到需要捕捉的信号
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	// 在另一个 goroutine 中监听信号通道
	go func() {
		<-sigs
		logger.Warn("用户主动停止程序，提前退出... ")
		generator.Summary(constant.ABORT, "用户主动退出")
		logger.Close()
		os.Exit(0)
	}()
}

func (generator *UTGenerator) CheckConfigValid() {
	logger.Error("【准备 => 模型配置检查~_~】")
	userConfig := conf_parser.GetUserConfig()
	if valid, err := userConfig.IsValidAndAccessible(); !valid {
		logger.Error("【配置信息检查 失败 】")
		logger.ErrorT(1, "失败原因： %s", err.Error())
		os.Exit(1)
	}
	logger.InfoT(1, "LLM模型配置：")
	logger.InfoT(2, "- 生成阶段：%s", userConfig.LLMModelConfig.Gen.Model)
	logger.InfoT(2, "- 修复阶段：%s", userConfig.LLMModelConfig.Repair.Model)
	logger.Info("【配置信息检查 成功 】")
}

func (generator *UTGenerator) Run() {
	// 捕捉用户主动退出程序
	generator.handleSignal()

	// 关闭通知
	defer func() {
		if generator.ideNotifier != nil {
			generator.ideNotifier.Close()
		}
	}()

	// 异常处理
	defer func() {
		if r := recover(); r != nil {
			logger.Error("程序异常，发生panic:\n%v", r)
			logger.Error("堆栈信息：\n%s", debug.Stack())

			entry, ok := r.(*logrus.Entry)
			if ok {
				// 获取并打印 Entry 中的 Message 字段
				generator.notifyTaskStatus(nil, constant.FAILED, fmt.Sprintf("程序异常退出: %s", entry.Message))
			} else {
				generator.notifyTaskStatus(nil, constant.FAILED, constant.GetFailMessage(generator.isSaas))
			}
		}
	}()

	// 首次生成
	firstGenerateError := generator.notifyFirstGenerate()
	if firstGenerateError != nil {
		return
	}

	// 开始环境检查
	generator.notifyEnvCheckStart()
	// 验证环境是否可用
	envStatus, err := generator.UTAgent.ValidEnv()
	generator.notifyEnvResult(envStatus, err)
	if !envStatus {
		logger.Error("工程构建失败，程序退出")
		generator.notifyTaskStatus(nil, constant.FAILED, "环境检查失败")
		os.Exit(1)
	}
	// 环境检查结束
	frameworksCheck := generator.UTAgent.AutoDetectAndModifyBuildScript()
	generator.notifyFrameworkResult(frameworksCheck)
	if frameworksCheck.State == constant.FAILED {
		logger.Error("检测到缺少单测框架配置缺失，程序退出")
		generator.notifyTaskStatus(nil, constant.FAILED, "环境检查失败")
		os.Exit(1)
	}

	// 初始化队列和 WaitGroup：生成，执行验证，修复复
	generator.initQueues()
	defer generator.cleanupQueues()

	// 启动后台任务
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	generator.cancel = cancel

	generator.wg.Add(3)
	go generator.inferenceInBackground(ctx)
	go generator.validationInBackground(ctx)
	go generator.repairInBackground(ctx)

	// 开启任务
	generator.UTAgent.Call(generator.QueueForLLMCall)

	// 检查缓存并发送完成信号
	var status, errMsg string
	if generator.cache.Len() == 0 {
		status = constant.SKIPPED
		if generator.param.GetType() == constant.GenerateForMethod {
			if generator.param.GetLang() == constant.LangJava {
				errMsg = constant.SKIPPEDForMethodForJava
			} else {
				errMsg = constant.SKIPPEDForMethod
			}

		} else if generator.param.GetType() == constant.GenerateForDiff {
			errMsg = constant.SKIPPEDForDiff
		} else {
			errMsg = constant.SKIPPEDForGen
		}
		logger.Warn(" ╮（﹀_﹀）╭ 未找到待完善测试的文件和方法，提前退出 ~")
		cancel()
	} else {
		status = constant.SUCCEED
	}

	// 等待所有任务完成
	generator.wg.Wait()
	generator.Summary(status, errMsg)
}

func (generator *UTGenerator) Summary(status string, errMsg string) {
	logger.Info("【任务状态】%s %s", status, errMsg)
	generator.UTAgent.GetMemory().TotalTimer.Stop()
	summary := generator.summary()
	generator.notifyTaskStatus(summary, status, errMsg)
}

func (generator *UTGenerator) deleteFromCache(filePath string, underTest models.UnderTestInfo, reason string) {
	if generator.cache.Exists(underTest.FuncID) {
		generator.UTAgent.GetMemory().FileProcessUpdate(filePath)
		logger.Info("文件：[%s] 完成一个被测方法的处理：%s", filePath, underTest.FuncID)
		id := underTest.ID
		if reason == models.ReasonMaxIterationReached {
			logger.InfoTGreen(1, "【%s - 方法完成】%s：%s 🟠",
				underTest.Process.GetRound(),
				id,
				fmt.Sprintf("%s:%d", reason, generator.param.GetIterationMax()))
		} else if reason == models.ReasonCoverageReached {
			logger.InfoTGreen(1, "【%s - 方法完成】%s 原因：%s 🎉🎉🎉",
				underTest.Process.GetRound(),
				id,
				fmt.Sprintf("%s:%d%%", reason, generator.param.GetDesiredCoverage()))
		}

		if underTest.Process.ValidUTCase > 0 {
			// 为该被测生成的有效用例个数
			logger.InfoT(1, "【生成有效用例个数】%d", underTest.Process.ValidUTCase)
			generator.UTAgent.GetMemory().RecordValidCaseNum(filePath, underTest.Process.ValidUTCase)
		}

		// 为处理结束的单测文件上报生成
		if generator.UTAgent.GetMemory().GetFileProcess(filePath).Status == 2 && generator.UTAgent.GetMemory().GetFileProcess(filePath).ValidCaseNum > 0 {
			generator.reportGenerateForFile(filePath)
			generator.notifyUtResultSucceed(underTest, filePath)
		}

		generator.cache.Delete(underTest.FuncID)
		logger.Info("方法 %s 处理完毕，迭代次数：%d", underTest.FuncID, underTest.Process.GetIterNum(underTest.FuncID, ""))
	}

	logger.Info("当前缓存中剩余待处理的方法： %v", generator.cache.GetAll())
	if generator.cache.Len() == 0 && generator.UTAgent.GetMemory().FileProcessInQueue == 1 {
		logger.Warn(" ╮（﹀_﹀）╭ 任务执行完成， bingo ~")
		generator.cancel()
	}
}

func (generator *UTGenerator) getSrcFileTargetMethods(filePath string) string {
	srcBytes, _ := os.ReadFile(filePath)
	srcContent := strings.Split(string(srcBytes), "\n")
	srcTargetMethod := ""
	for startLine, endLine := range generator.param.GetSrcMethods()[filePath] {
		srcTargetMethod = srcTargetMethod + "\n" + strings.Join(srcContent[startLine:endLine+1], "\n")
	}

	if srcTargetMethod == "" {
		srcTargetMethod = string(srcBytes)
	}
	return strings.Trim(srcTargetMethod, "\n")
}

// 初始化队列
func (generator *UTGenerator) initQueues() {
	generator.QueueForLLMCall = make(chan any, GenerateQueueSize)
	generator.QueueForValidation = make(chan any, ValidQueueSize)
	generator.QueueForRepair = make(chan any, RepairQueueSize)
}

// 清理队列
func (generator *UTGenerator) cleanupQueues() {
	close(generator.QueueForLLMCall)
	close(generator.QueueForValidation)
	close(generator.QueueForRepair)
}
