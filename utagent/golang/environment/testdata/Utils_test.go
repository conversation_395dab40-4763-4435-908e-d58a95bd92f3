package helper

import "testing"

func Test_add(t *testing.T) {
	type args struct {
		a int
		b int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "Test add with positive numbers",
			args: args{
				a: 1,
				b: 2,
			},
			want: 3,
		},
		{
			name: "Test add with negative numbers",
			args: args{
				a: -1,
				b: -2,
			},
			want: -3,
		},
		{
			name: "Test add with zero",
			args: args{
				a: 0,
				b: 0,
			},
			want: 0,
		},
		{
			name: "Test add with large numbers",
			args: args{
				a: 1000000,
				b: 2000000,
			},
			want: 3000000,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := add(tt.args.a, tt.args.b); got != tt.want {
				t.<PERSON>("add() = %v, want %v", got, tt.want)
			}
		})
	}
}
