package helper

import (
	"encoding/base64"
	"fmt"
	"os"
)

func EncodeBase64(input string) string {
	return base64.StdEncoding.EncodeToString([]byte(input))
}

func GetUserHome() string {
	fmt.Println(os.UserHomeDir())
	fmt.Println(os.Getenv("HOME"))
	// 或者使用环境变量
	homeDirEnv, err := os.UserHomeDir()
	if err != nil {
		return ""
	}

	return homeDirEnv
}

func add(a, b int) int {
	fmt.Println("add")
	return a + b
}
func sub(a, b int) int {
	fmt.Println("su1b")
	return a - b
}
