package environment

import (
	"icode.baidu.com/baidu/cov/iUT/tools"
	"testing"
)

func TestGolangAgentRunnerClient_Run(t *testing.T) {
	testFilePath := "./testdata/Utils_test.go"
	testMethodName := "Test_add"
	client := &GolangAgentRunnerClient{
		ProjectRoot:    "./",
		UTFullFilePath: testFilePath,
		UTMethodName:   testMethodName,
	}
	client.Run()
}

func Test_runGoTest(t *testing.T) {
	type args struct {
		coveragePath string
		filePath     string
		funcName     string
		overlayFile  string
	}
	tests := []struct {
		name string
		args args
		want *tools.ShellCmdInfo
	}{
		{
			name: "testsuccess",
			args: args{
				coveragePath: "./coverage.out",
				filePath:     "/Users/<USER>/test/baidu/cov/gotest/util/Utils_test.go",
				funcName:     "TestSubSuccess",
				overlayFile:  "/Utils_test.go.overlay",
			},
			want: &tools.ShellCmdInfo{
				CmdState: 0,
			},
		}, {
			name: "testfail",
			args: args{
				coveragePath: "./coverage.out",
				filePath:     "/Users/<USER>/test/baidu/cov/gotest/util/Utils_test.go",
				funcName:     "TestSub",
				overlayFile:  "/Utils_test.go.overlay",
			},
			want: &tools.ShellCmdInfo{
				CmdState: 0,
			},
		}, {
			name: "testerror",
			args: args{
				coveragePath: "./coverage.out",
				filePath:     "/Users/<USER>/test/baidu/cov/gotest/utilerror/Utils_test.go",
				funcName:     "TestSub",
				overlayFile:  "/Utils_test.go.overlay",
			},
			want: &tools.ShellCmdInfo{
				CmdState: 0,
			},
		}, {
			name: "testpanic",
			args: args{
				coveragePath: "./coverage.out",
				filePath:     "/Users/<USER>/test/baidu/cov/gotest/utilpanic/Utils_test.go",
				funcName:     "TestSub",
				overlayFile:  "/Utils_test.go.overlay",
			},
			want: &tools.ShellCmdInfo{
				CmdState: 0,
			},
		}, {
			name: "testgotest",
			args: args{
				coveragePath: "./coverage.out",
				filePath:     "/Users/<USER>/test/baidu/cov/gotest/utilgotests/Utils_test.go",
				funcName:     "TestSub",
				overlayFile:  "/Utils_test.go.overlay",
			},
			want: &tools.ShellCmdInfo{
				CmdState: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := runGoTest(tt.args.coveragePath, tt.args.filePath, tt.args.funcName, tt.args.overlayFile)
			convertShellToRunResult(got, tt.args.funcName, tt.args.filePath)
		})
	}
}
