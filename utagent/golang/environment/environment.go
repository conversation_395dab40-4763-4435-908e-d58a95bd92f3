package environment

import (
	"errors"
	"fmt"
	"regexp"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type Environment struct {
	LocalRepoPath  string
	UnitFrameworks map[string]string
	UnitFramework  string
	MockFramework  []string

	BuildTool    string
	WorkDir      string
	RunCmd       string
	OutputParser func(string) (*models.CaseInfo, error)
}

func NewEnvironment(workDir string) *Environment {
	env := &Environment{WorkDir: workDir, RunCmd: "go mod tidy"}
	return env
}

func (e *Environment) CheckEnv() error {
	logger.Info("【准备 => 验证环境~_~】：要保证环境可正确编译, 以支持用例的执行验证")
	if e.RunCmd != "" {
		status, output := tools.GetShellStatusOutput(strings.Split(e.RunCmd, " "), e.WorkDir, false)
		if status != 0 {
			logger.ErrorT(1, "%-10s", "【环境异常】❌")
			logger.ErrorT(1, "%-10s", "验证指令："+e.RunCmd)
			logger.ErrorT(1, "%-10s", "错误信息："+strings.TrimSpace(output))
			_, out := tools.GetShellStatusOutput([]string{"go", "version"}, e.WorkDir, false)
			logger.InfoT(1, "go version: \n%s", out)
			return errors.New(fmt.Sprintf("环境校验失败：请检查工程根目录下是否能正常运行下面命令:【%s】如有报错，请解决报错后再次运行任务。", e.RunCmd))
		}
	}

	logger.InfoT(1, "【环境ok】")
	//logger.InfoT(1, "依赖缓冲路径：%s", e.LocalRepoPath)
	return nil
}

func (e *Environment) GetGoVersion() string {
	_, out := tools.GetShellStatusOutput([]string{"go", "version"}, e.WorkDir, false)
	re := regexp.MustCompile("go version go(\\S+)")
	result := re.FindStringSubmatch(out)
	if len(result) > 0 {
		return result[1]
	}
	return ""
}

// RunValid 执行用例，返回结果
func (e *Environment) RunValid(caseInfo *models.CaseInfo, passCheck bool) *models.RunResult {

	client := &GolangAgentRunnerClient{
		ProjectRoot:     e.WorkDir,
		UTFullFilePath:  caseInfo.TestFilePath,
		UTMethodName:    caseInfo.TestMethodName,
		TestFileContent: caseInfo.TestFileContent,
	}

	return client.Run()
}
