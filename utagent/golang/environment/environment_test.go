package environment

import (
	"testing"

	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

// TestCheckEnv_Success 是用于测试 CheckEnv_Success
// generated by Comate
func TestCheckEnv_Success(t *testing.T) {
	// 准备环境
	env := &Environment{
		RunCmd:  "go version",
		WorkDir: "/tmp",
		OutputParser: func(output string) (*models.CaseInfo, error) {
			return &models.CaseInfo{}, nil
		},
	}

	// 调用被测方法
	err := env.CheckEnv()

	// 验证结果
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

// TestCheckEnv_InvalidCmd 是用于测试 CheckEnv_InvalidCmd
// generated by Comate
func TestCheckEnv_InvalidCmd(t *testing.T) {
	// 准备环境
	env := &Environment{
		RunCmd:  "invalid_command",
		WorkDir: "/tmp",
		OutputParser: func(output string) (*models.CaseInfo, error) {
			return &models.CaseInfo{}, nil
		},
	}

	// 调用被测方法
	err := env.CheckEnv()

	// 验证结果
	if err == nil {
		t.Errorf("Expected an error, got nil")
	}
}
