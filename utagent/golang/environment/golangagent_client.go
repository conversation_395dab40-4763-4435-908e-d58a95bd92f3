package environment

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type GolangAgentRunnerClient struct {
	ProjectRoot     string // 项目根目录
	UTFullFilePath  string // 测试文件的完整路径
	UTMethodName    string // 测试方法名
	TestFileContent string
}

type Overlay struct {
	Replace map[string]string `json:"Replace"` // Replace 是一个键值对映射
}

func writeOverlay(srcFilePath, tempFilePath, method, overlayFile, testFileContent string) bool {
	tempFile := filepath.Join(filepath.Dir(tempFilePath), fmt.Sprintf("%s%s", method, ".go"))

	overlay := &Overlay{
		Replace: map[string]string{
			srcFilePath: tempFile,
		},
	}
	// 将结构体转为 JSON 字符串
	jsonStr, err := json.Marshal(overlay)
	if err != nil {
		logger.Error("Error:", err)
		return false
	}
	helper.WriteContent(overlayFile, string(jsonStr))
	if err := helper.WriteContent(tempFile, testFileContent); err != nil {
		return false
	}
	return true
}

func (j *GolangAgentRunnerClient) Run() *models.RunResult {
	var shellCmdInfo *tools.ShellCmdInfo
	workDir := filepath.Join(os.TempDir(), helper.StringMd5(j.ProjectRoot))
	// 检查目录是否存在
	if _, err := os.Stat(workDir); os.IsNotExist(err) {
		// 创建目录及其所有父级目录
		err := os.MkdirAll(workDir, 0755)
		if err != nil {
			logger.Error("Directory created failed")
		}
	}
	coveragePath := filepath.Join(workDir, fmt.Sprintf("%s_%s", j.UTMethodName, "coverage.out"))
	if j.UTMethodName == "" {
		coveragePath = filepath.Join(workDir, "coverage.out")
		shellCmdInfo = runGoTestWithPath(coveragePath, filepath.Dir(j.UTFullFilePath))
	} else {
		if !helper.IsFileExist(j.UTFullFilePath) {
			helper.WriteContent(j.UTFullFilePath, j.TestFileContent)
			defer os.Remove(j.UTFullFilePath)
		}
		filePath := filepath.Join(workDir, j.UTMethodName)
		overlayFile := filepath.Join(filePath, "overlay.json")
		defer os.Remove(overlayFile)
		if !writeOverlay(j.UTFullFilePath, filePath, j.UTMethodName, overlayFile, j.TestFileContent) {
			return nil
		}
		shellCmdInfo = runGoTest(coveragePath, filepath.Dir(j.UTFullFilePath), j.UTMethodName, overlayFile)
	}
	defer os.Remove(coveragePath)

	// runResult := &models.RunResult{
	//	State:              shellCmdInfo.CmdState,
	//	IsCompileSucceeded: shellCmdInfo.CmdState == 0,
	//	IsRunWithoutError:  shellCmdInfo.CmdState == 0,
	//	RunErrorMsg:        shellCmdInfo.ErrorInfo,
	//	RunResults:         make(map[string]*models.UnitTestResult),
	// }
	runResult := convertShellToRunResult(shellCmdInfo, j.UTMethodName, j.UTFullFilePath)
	if shellCmdInfo.CmdState == 0 {
		runResult.RunResults[j.UTMethodName] = &models.UnitTestResult{
			IsRunSucceed: true,
		}
	}

	if helper.IsFileExist(coveragePath) {
		runResult.ClassCoverageInfo, _ = coverage.ParseCovFile(coveragePath)
	}
	return runResult
}

func convertShellToRunResult(shellCmdInfo *tools.ShellCmdInfo, methodName, fileName string) *models.RunResult {
	runResult := &models.RunResult{
		State:              shellCmdInfo.CmdState,
		IsCompileSucceeded: true,
		IsRunWithoutError:  shellCmdInfo.CmdState == 0,
		RunErrorMsg:        shellCmdInfo.ErrorInfo,
		RunResults:         make(map[string]*models.UnitTestResult),
	}
	output := strings.TrimSpace(shellCmdInfo.Output)
	if shellCmdInfo.CmdState != 0 && len(output) > 0 {
		isRunSucceed := true
		outputLines := strings.Split(output, "\n")
		if strings.HasPrefix(outputLines[len(outputLines)-1], "FAIL") {
			runResult.State = 1
			isRunSucceed = false
		}
		if strings.Contains(output, "build failed") {
			runResult.IsCompileSucceeded = false
			runResult.Message = shellCmdInfo.ErrorInfo
			isRunSucceed = false
		}
		testFilename := filepath.Base(fileName)
		faileMsg := make([]string, 0)
		for i := 0; i < len(outputLines); i++ {
			if strings.HasPrefix(strings.TrimSpace(outputLines[i]), testFilename) {
				faileMsg = append(faileMsg, outputLines[i])
				isRunSucceed = false
			}
		}
		errorMsg := make([]string, 0)
		//line := 0
		if strings.Contains(output, "panic:") {
			isRunSucceed = false
			runResult.State = 1
			for i := 0; i < len(outputLines); i++ {
				if strings.HasPrefix(outputLines[i], "panic") && strings.Contains(outputLines[i], "recovered") {
					for ; i < len(outputLines); i++ {
						// if strings.HasPrefix(strings.TrimSpace(outputLines[i]), fileName) {
						//	parts := strings.Split(outputLines[i], " ")
						//	for _, part := range parts {
						//		if strings.HasPrefix(part, fileName) {
						//			line, _ = strconv.Atoi(strings.Split(part, ":")[1])
						//		}
						//	}
						// }
						errorMsg = append(errorMsg, outputLines[i])
					}
				}
			}
		}
		runResult.RunResults[methodName] = &models.UnitTestResult{
			Exception:      strings.Join(errorMsg, "\n"),
			FailureMessage: strings.Join(faileMsg, "\n"),
			IsRunSucceed:   isRunSucceed,
			MethodName:     methodName,
		}
	}

	return runResult
}

//	func CheckTestEnv(path string, gitRoot string) *tools.ShellCmdInfo {
//		if !strings.HasPrefix(path, gitRoot) {
//			path = filepath.Join(gitRoot, path)
//		}
//
//		cmd := "cd " + path + " && go test -c -o " + filepath.Join(os.TempDir(), "gotest.test")
//		return tools.GetShellStatusOutputError(cmd, path, true)
//	}
func runGoTestWithPath(coveragePath, filePath string) *tools.ShellCmdInfo {
	start := time.Now()
	cmd := "go test -v -gcflags %s -timeout 1m -coverprofile=%s"
	params := []string{"'all=-N -l'", coveragePath}
	cmdList, _ := tools.AssembleCmdList(cmd, params)
	// cmd := fmt.Sprintf("go  test -v -gcflags 'all=-N -l' -timeout 1m -coverprofile=%s", coveragePath)

	shellCmd := tools.GetShellStatusOutputErrorTimeOut(cmdList, filePath, true, 30)
	// 运行无异常表示修复成功，其运行PASS or FAIL 进表示用例构造
	// if len(shellCmd.ErrorInfo) > 0 {
	// 	shellCmd.CmdState = 1
	// }

	// 用例运行出现panic的将其归为失败
	if shellCmd.CmdState == 0 && strings.Contains(shellCmd.Output, "panic") {
		lines := strings.Split(shellCmd.Output, "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "panic") && strings.Contains(line, "recovered") {
				shellCmd.CmdState = 1
				break
			}
		}
	}
	logger.Debug(fmt.Sprintf("run test case cost time:[%.2f]", time.Since(start).Seconds()))

	return &shellCmd
}

func runGoTest(coveragePath, filePath string, funcName, overlayFile string) *tools.ShellCmdInfo {
	start := time.Now()
	cmd := "go test -overlay=%s -v -gcflags %s -timeout 1m -run %s -coverprofile=%s"
	params := []string{overlayFile, "'all=-N -l'", funcName, coveragePath}
	cmdList, _ := tools.AssembleCmdList(cmd, params)
	// cmd := fmt.Sprintf("go  test -overlay=%s -v -gcflags \"all=-N -l\" -timeout 1m -run  %s -coverprofile=%s",
	//	overlayFile, funcName, coveragePath)
	if !helper.IsFileExist(overlayFile) {
		cmd = "go test -v -gcflags %s -timeout 1m -run %s -coverprofile=%s"
		params = []string{"'all=-N -l'", funcName, coveragePath}
		cmdList, _ = tools.AssembleCmdList(cmd, params)
		cmdList = []string{"go", "test", "-v", "-gcflags", `"all=-N -l"`, "-timeout", "1m", "-run", funcName, "-coverprofile=" + coveragePath}
		// cmd = fmt.Sprintf("go  test -v -gcflags 'all=-N -l' -timeout 1m -run %s -coverprofile=%s",
		//	funcName, coveragePath)
	}
	dir := filePath
	if strings.HasSuffix(filePath, ".go") {
		dir = filepath.Dir(filePath)
	}
	shellCmd := tools.GetShellStatusOutputErrorTimeOut(cmdList, dir, true, 30)
	// 运行无异常表示修复成功，其运行PASS or FAIL 进表示用例构造
	// if len(shellCmd.ErrorInfo) > 0 {
	// 	shellCmd.CmdState = 1
	// }

	// 用例运行出现panic的将其归为失败
	if shellCmd.CmdState == 0 && strings.Contains(shellCmd.Output, "panic") {
		lines := strings.Split(shellCmd.Output, "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "panic") && strings.Contains(line, "recovered") {
				shellCmd.CmdState = 1
				break
			}
		}
	}
	logger.Debug(fmt.Sprintf("run test case cost time:[%.2f]", time.Since(start).Seconds()))

	return &shellCmd
}
