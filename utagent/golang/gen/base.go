package gen

import (
	"icode.baidu.com/baidu/cov/iUT/llm"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
)

func NewUtGenActionForGo(modelConf *llm.ModelConf, parser *golang.Parser, input *basegen.Input, actionName string) basegen.UtGen {
	switch actionName {
	case basegen.FirstGen:
		return NewLLMUtFirstGen(modelConf, parser, input)
	case basegen.CoverageRegen:
		return NewLLMUtReGenForCoverage(modelConf, parser, input)
	default:
		panic("unknown action name")
	}
}
