package gen

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
	"icode.baidu.com/baidu/cov/smartUT-parser/java"
	"testing"
)

func TestLLMUtReGenForCoverage_Predict(t *testing.T) {
	type fields struct {
		Model  string
		Input  *basegen.Input
		Parser *golang.Parser
	}

	root := "/Users/<USER>/workspace/comate-ut/evaluate/java-evaluate-data/xxl-job"

	parser := java.NewParser(root)

	fileContext, _ := parser.ParseFilePath(
		"/Users/<USER>/workspace/comate-ut/evaluate/java-evaluate-data/xxl-job/xxl-job-core/src/main/java/com/xxl/job/core/util/MathsUtil.java")

	method := fileContext.Classes[0].Methods[2]

	tests := []struct {
		name    string
		fields  fields
		want    string
		wantErr bool
	}{
		{"test1", fields{"llm", &basegen.Input{
			TargetMethod:    method,
			FileContext:     fileContext,
			TestFileContext: nil,
			CoverageInfo: &coverage.CoverageInfo{
				Coverage:     0.11,
				MissedLines:  []int{33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43},
				CoveredLines: []int{31, 32},
			},
		}, golang.NewParser(root)},
			"", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &LLMUtReGenForCoverage{
				Input:  tt.fields.Input,
				Parser: tt.fields.Parser,
			}
			got, err := g.Predict()
			if (err != nil) != tt.wantErr {
				t.Errorf("Predict() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}
