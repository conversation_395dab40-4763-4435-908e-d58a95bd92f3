package gen

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
)

type LLMUtReGenForCoverage struct {
	Model       *llm.ModelConf
	Input       *gen.Input
	Parser      *golang.Parser
	InputMethod *golang.Method
}

func NewLLMUtReGenForCoverage(modelConf *llm.ModelConf, parser *golang.Parser, input *gen.Input) *LLMUtReGenForCoverage {
	if modelConf == nil {
		modelConf = llm.ModelConfForInnerUtInfer(llm.DC)
	}
	return &LLMUtReGenForCoverage{
		Model:  modelConf,
		Input:  input,
		Parser: parser,
	}
}

func (g *LLMUtReGenForCoverage) buildPrompt() (string, error) {
	method := g.Input.TargetMethod.(*golang.Method)
	g.InputMethod = method
	fileContext := g.Input.FileContext.(*golang.FileContext)

	codeInfo, err := g.Parser.ParseMethod(fileContext, method)

	if err != nil {
		return "", err
	}

	builderInput := &promptbuilder.InputData{
		Language:            "Go",
		TargetMethod:        method.Identifier,
		SrcFileContent:      fmt.Sprintf("```go\n%s\n```", buildFileContent(codeInfo)),
		TargetCodeSignature: buildFocalMethodSignature(codeInfo),
		Mocks:               g.Input.Framework,
		InnerCallCode:       buildInnerCallCode(codeInfo),
		HandleTokenLimit:    nil,
	}

	builderInput.CoveredLines = ""
	for _, line := range g.Input.CoverageInfo.CoveredLines {
		builderInput.CoveredLines += fmt.Sprintf("%d, ", line)
	}
	builderInput.CoveredLines = strings.TrimSuffix(builderInput.CoveredLines, ", ")

	builderInput.MissedLines = ""
	for _, line := range g.Input.CoverageInfo.MissedLines {
		builderInput.MissedLines += fmt.Sprintf("%d, ", line)
	}
	builderInput.MissedLines = strings.TrimSuffix(builderInput.MissedLines, ", ")

	prompt, err := promptbuilder.BuildPrompt(promptbuilder.LLMReGen, builderInput, -1)

	if err != nil {
		return "", err
	}

	return prompt, nil
}

// Predict 函数是LLMUtReGenForCoverage结构体的方法，用于预测并生成测试用例内容
// 返回预测的测试用例内容字符串和错误信息（如果有的话）
func (g *LLMUtReGenForCoverage) Predict() (string, error) {
	prompt, err := g.buildPrompt()
	if err != nil {
		return "", err
	}

	tm := time.Now().UnixMilli()
	helper.SaveTmpFile(filepath.Join("regen", fmt.Sprintf("%s-regen-%d-src.txt", g.InputMethod.Identifier, tm)), prompt)
	result, err := llm.Inference(prompt, g.Model)
	if err != nil {
		return "", err
	}

	helper.SaveTmpFile(filepath.Join("regen", fmt.Sprintf("%s-regen-%d-src.txt", g.InputMethod.Identifier, tm)), result)
	return getUtContentFromLLMResult(result), nil
}

func (g *LLMUtReGenForCoverage) GetName() string {
	return "修复生成"
}

func buildInnerCallCode(codeInfo *golang.CodeInfo) map[string]string {
	innerCallCode := make(map[string]string, 0)
	for pkgName, code := range codeInfo.RelativeCodeCalled {
		innerCallCode[pkgName] = fmt.Sprintf("```go\n%s\n```", code)
	}
	return innerCallCode
}

func buildFileContent(codeInfo *golang.CodeInfo) string {
	var fileContent string
	var startLineTag = -1
	method := codeInfo.TargetMethod
	for _, line := range strings.Split(codeInfo.TargetCode, "\n") {
		if strings.Contains(line, "// focal method") {
			fileContent += "...\n"
			fileContent += fmt.Sprintf("%d %s\n", method.Block.StartLine, line)
			startLineTag = method.Block.StartLine
			continue
		}
		if startLineTag > -1 && startLineTag <= method.Block.EndLine {
			fileContent += fmt.Sprintf("%d %s\n", startLineTag+1, line)
			startLineTag++
			if startLineTag == method.Block.EndLine+1 {
				fileContent += "...\n"
			}
		} else {
			fileContent += line + "\n"
		}
	}

	return fileContent
}

func buildFocalMethodSignature(codeInfo *golang.CodeInfo) string {
	var targetCodeSignature string
	for _, signature := range codeInfo.TargetCodeSignature {
		targetCodeSignature = signature
	}

	if targetCodeSignature == "" {
		return ""
	}

	return fmt.Sprintf("```go\n" + targetCodeSignature + "\n```")
}
