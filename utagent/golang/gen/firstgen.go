package gen

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
)

type LLMUtFirstGen struct {
	Input  *gen.Input
	Model  *llm.ModelConf
	Parser *golang.Parser
}

func NewLLMUtFirstGen(modelConf *llm.ModelConf, parser *golang.Parser, input *gen.Input) *LLMUtFirstGen {
	if modelConf == nil {
		// 默认模型
		modelConf = llm.ModelConfForInnerUtInfer(llm.DC)
	}
	return &LLMUtFirstGen{
		Model:  modelConf,
		Input:  input,
		Parser: parser,
	}
}

func (g *LLMUtFirstGen) buildPrompt() string {
	method := g.Input.TargetMethod.(*golang.Method)
	fileContext := g.Input.FileContext.(*golang.FileContext)
	parser := g.Parser
	codeInfo, _ := parser.ParseMethod(fileContext, method)
	if codeInfo == nil {
		logger.DebugT(1, "Failed to parse method %s in file %s\n", method.Identifier,
			fileContext.Path)
		return ""
	}
	var testCodeInfo *golang.TestCodeInfo
	if g.Input.TestFileContext != nil {
		testCodeInfo, _ = parser.ParseExistedUT(g.Input.TestFileContext.(*golang.FileContext), codeInfo, "")
	}

	prompt, _ := golang.BuildPrompt(context.Background(), codeInfo, testCodeInfo, g.Input.Framework, llm.DCTokenLimit)
	return prompt
}

func (g *LLMUtFirstGen) Predict() (string, error) {
	if g.Input == nil {
		return "", fmt.Errorf("input is empty")
	}
	prompt := g.buildPrompt()
	result, err := llm.Inference(prompt, g.Model)
	if err != nil {
		return "", err
	}
	return getUtContentFromLLMResult(result), nil
}

func (g *LLMUtFirstGen) GetName() string {
	return "首次生成"
}
func getUtContentFromLLMResult(LLMResult string) string {
	finalContent := utils.GetCorrectCodeFromLLMResult(helper.GetUTCodeFromString(LLMResult, "go"))
	if finalContent == "" {
		return LLMResult
	}
	return finalContent
}
