package golang

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"path/filepath"
	"strings"
)

func GetTestFile(srcFile, targetMethodName string) *models.TestFile {
	// 替换路径中的目录
	utFile := srcFile
	base := filepath.Base(utFile)
	clsName := strings.TrimSuffix(base, ".go")
	testFile := matchUTFile(filepath.Dir(utFile), clsName, targetMethodName)
	return testFile
}

func GetTestFilePath(srcFile string) string {
	if srcFile == "" {
		return ""
	}
	dir := filepath.Dir(srcFile)
	base := filepath.Base(srcFile)
	clsName := strings.TrimSuffix(base, ".go")
	return fmt.Sprintf(filepath.Join(dir, fmt.Sprintf("%s_test.go", clsName)))
}

func matchUTFile(dir, clsName, methodName string) *models.TestFile {
	className := clsName
	if methodName != "" {
		className = fmt.Sprintf("%s_%s", clsName, methodName)
	}
	utFile := filepath.Join(dir, fmt.Sprintf("%s_test.go", className))
	utClsName := fmt.Sprintf("%s_test", className)

	if !helper.IsFileExist(utFile) {
		return nil
	}

	return &models.TestFile{
		TestFilePath:    utFile,
		TestFileContent: helper.ReadFile(utFile),
		TestClassName:   utClsName,
	}
}
