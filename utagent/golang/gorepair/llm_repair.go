package gorepair

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"path/filepath"
	"regexp"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/promptbuilder"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/gen"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"icode.baidu.com/baidu/cov/iUT/utagent/mongo"
)

type LLMRepairAction struct {
	Name     string
	Input    *models.RepairInput
	LLMModel *llm.ModelConf
}

// NewLLMRepairAction 创建 LLMRepairAction 实例
func NewLLMRepairAction(modelConfig *llm.ModelConf) *LLMRepairAction {
	if modelConfig == nil {
		modelConfig = llm.ModelConfForInnerUtInfer(llm.GetDefaultRepairModel())
	}
	return &LLMRepairAction{
		Name:     "LLM_REPAIR",
		LLMModel: modelConfig,
	}
}

func (l *LLMRepairAction) SetInput(input *models.RepairInput) {
	l.Input = input
}

func (l *LLMRepairAction) Call() (string, bool, string, error) {
	fileContent := l.Input.TestFileContent

	fileContent = shortenAndAddLineNo(fileContent, l.Input.TestMethodName)

	data := &promptbuilder.InputData{
		Language:         "go",
		SrcFileContent:   fmt.Sprintf("```go\n%s\n```", l.Input.SrcFileContent),
		TestFileContent:  fmt.Sprintf("```go\n%s\n```", fileContent),
		TestFilePath:     filepath.Base(l.Input.TestFilePath),
		HandleTokenLimit: nil,
		TestMethodName:   l.Input.TestMethodName,
	}

	isAssertError := false

	data.LineNo = len(strings.Split(l.Input.TestFileContent, "\n"))
	if !l.Input.RunResult.IsCompileSucceeded {
		// 编译错误信息
		data.ErrorLog += l.Input.RunResult.Message
	} else {
		// 断言错误信息
		for _, err := range l.Input.RunResult.RunResults {
			if l.Input.RunResult.IsAssertError(err.Exception) {
				isAssertError = true
			}
			data.ErrorLog += err.FailureMessage + "\n"
			if err.Exception != "" {
				data.ErrorLog += err.Exception + "\n"
			}
		}
	}

	// 构建prompt
	var buildPrompt string
	var err error

	if isAssertError {
		buildPrompt, err = promptbuilder.BuildPrompt(promptbuilder.LLMRepairAssert, data, -1)
	} else {
		buildPrompt, err = promptbuilder.BuildPrompt(promptbuilder.LLMRepair, data, -1)
	}

	// todo: 上线后删除 debug 调试
	helper.SaveTmpFile(filepath.Join(l.Input.ErrorType.Value(), fmt.Sprintf("repair-%s-llm-src.txt", l.Input.RepairId)), buildPrompt)

	if err != nil {
		return "", false, "", err
	}
	// 调用llm
	result, err := llm.Inference(buildPrompt, l.LLMModel)
	if err != nil {
		return "", false, "", err
	}

	// todo: 上线后删除 debug 调试
	helper.SaveTmpFile(filepath.Join(l.Input.ErrorType.Value(), fmt.Sprintf("repair-%s-llm-tgt.txt", l.Input.RepairId)), result)

	result = gen.GetUtContentFromLLMResult(result)
	result = helper.RemoveLineNoAndEmptyLines(result)

	helper.SaveTmpFile(filepath.Join(l.Input.ErrorType.Value(), fmt.Sprintf("repair-%s-llm-repaired.txt", l.Input.RepairId)), result)

	input := l.Input
	runErrorMsg := ""
	if input.RunResult != nil {
		runErrorMsg = input.RunResult.ErrorMessage()
	}
	mongoID := mongo.RepairCase(context.Background(), input.TestFilePath, input.TestFileContent, input.TestMethodName,
		buildPrompt, result, input.ErrorType.Value(), runErrorMsg, input.MongoID)
	return result, false, mongoID, nil
}

func (l *LLMRepairAction) GetName() string {
	return l.Name
}

func shortenAndAddLineNo(originalContent string, testCaseName string) string {
	scanner := bufio.NewScanner(strings.NewReader(originalContent))
	var builder strings.Builder

	lineNo := 0
	testCaseStartLine := 0
	foundCase := false
	skipCase := false

	testMethodPattern := regexp.MustCompile(`^func\s+Test\S+\s*\(`)
	commentPattern := regexp.MustCompile(`^\s*//`)

	for scanner.Scan() {
		lineNo++
		line := scanner.Text()

		if commentPattern.MatchString(line) {
			continue
		}

		if testMethodPattern.MatchString(line) {
			if strings.Contains(line, testCaseName) {
				// Target test case found
				testCaseStartLine = lineNo
				foundCase = true
				skipCase = false
				builder.WriteString(fmt.Sprintf("%d. %s\n", lineNo, line))
			} else {
				// Other test cases
				if !skipCase {
					builder.WriteString("\t...\n\t// 已省略无关用例\n\t...\n")
				}
				skipCase = true
			}
			continue
		}

		if skipCase {
			continue
		}

		if !foundCase {
			builder.WriteString(fmt.Sprintf("\t%s\n", line))
		} else if lineNo >= testCaseStartLine {
			builder.WriteString(fmt.Sprintf("%d. %s\n", lineNo, line))
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Error scanning file: %v", err)
		return originalContent
	}

	if !foundCase {
		return originalContent
	}

	return builder.String()
}
