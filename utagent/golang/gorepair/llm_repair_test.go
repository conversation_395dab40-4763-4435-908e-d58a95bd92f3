package gorepair

import (
	"fmt"
	"testing"
)

func TestShortenAndAddLineNo(t *testing.T) {
	content := `package utils

import (
	"context"
	"testing"

	"golang.org/x/text/language"
)

func Test1()(t *testing.T) {
	111111111
}

func TestIsLocaleEN(t *testing.T) {
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsLocaleEN(tt.args.ctx); got != tt.want {
				t.Errorf("IsLocaleEN() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test1()(t *testing.T) {
	22222222222222
}

func Test9999()(t *testing.T) {
	22222222222222
}
`
	result := shortenAndAddLineNo(content, "TestIsLocaleEN")
	fmt.Println(result)
}
