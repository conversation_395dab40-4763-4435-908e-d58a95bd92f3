package golang

import (
	"testing"

	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/utagent/golang/environment"
)

// TestAutoDetectAndModifyBuildScript 是用于测试 AutoDetectAndModifyBuildScript
// generated by Comate
func TestAutoDetectAndModifyBuildScript(t *testing.T) {
	utAgent := &UTAgent{
		Environment: &environment.Environment{},
	}
	result := utAgent.AutoDetectAndModifyBuildScript()
	if result.State != constant.SUCCEED {
		t.<PERSON>rf("Expected state to be SUCCEED, but got %s", result.State)
	}
	if len(result.Tools) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 tool, but got %d", len(result.Tools))
	}
	if result.Tools[0].Name != "go" {
		t.<PERSON><PERSON>rf("Expected tool name to be 'go', but got %s", result.Tools[0].Name)
	}
}
