package golang

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/file_processor"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/iUT/utagent/golang/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/merge/types"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/config/conf_parser"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/llm"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/repair"
	"icode.baidu.com/baidu/cov/iUT/utagent/golang/environment"
	"icode.baidu.com/baidu/cov/iUT/utagent/java/bug_detect"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
)

type UTAgent struct {
	Parser      *golang.Parser
	Environment *environment.Environment
	Memory      *base.Memory
	goMemory    *GoMemory
	RepairAct   *repair.Agent

	WorkDir       string
	RunCmd        string
	FileMethods   map[string]map[int]int
	FileDiffLines map[string][]int

	MaxIterations       int
	RepairMaxIterations int
	DesiredCoverage     int

	llmModelForInfer *llm.ModelConf
	cache            *helper.SafeMap
	resultPath       string
	isParsedCoverage bool
	genType          string
	trigger          string
	codeRepoInfo     *models.CodeRepo
	userParams       *models.Params

	totalFunctionCount int
}

func (u *UTAgent) GetTestFileContentOnlySuccess(result *models.UtFileResult, underTest models.UnderTestInfo) string {
	memoryGenUtCases := u.Memory.GetGeneratedUnitTest(result.TestFilePath)

	var succMethods []string
	for _, c := range memoryGenUtCases {
		if c.Bug != nil {
			continue
		}
		succMethods = append(succMethods, c.CaseName)
	}
	userExistFile := GetTestFile(underTest.FilePath, underTest.FuncName)
	fileParser, err := golang.NewFileParserWithContent(result.TestFileContent)
	if err != nil {
		return ""
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse()
	if llmTestFileContext == nil || len(llmTestFileContext.Methods) > 0 {
		return ""
	}
	if userExistFile != nil {
		mergeResult := file_processor.Merge(underTest.FuncName, underTest.PackageName, llmTestFileContext,
			succMethods, userExistFile.TestFilePath, userExistFile.TestFileContent)
		return mergeResult.MergedResult
	} else {
		testFilePath := GetTestFilePath(underTest.FilePath)
		mergeResult := file_processor.Merge(underTest.FuncName, underTest.PackageName, llmTestFileContext,
			succMethods, testFilePath, "")
		return mergeResult.MergedResult
	}
}

func NewUTAgent(params models.Params, cache *helper.SafeMap, codeRepoInfo *models.CodeRepo) (*UTAgent, error) {
	utAgent := UTAgent{
		Parser:              golang.NewParser(params.GetWorkDir()),
		Environment:         environment.NewEnvironment(params.GetWorkDir()),
		WorkDir:             params.GetWorkDir(),
		RunCmd:              params.GetRunCmd(),
		FileMethods:         params.GetSrcMethods(),
		MaxIterations:       params.GetIterationMax(),
		RepairMaxIterations: params.GetRepairIterMax(),
		DesiredCoverage:     params.GetDesiredCoverage(),
		Memory:              base.NewMemory(),
		cache:               cache,
		resultPath:          params.GetResultPath(),
		genType:             params.GetType(),
		codeRepoInfo:        codeRepoInfo,
		trigger:             params.GetTrigger(),
		userParams:          &params,
		goMemory:            NewGoMemory(),
	}

	if len(params.GetSrcMethods()) == 0 {
		return nil, fmt.Errorf("未收集到任何待生成用例的go源文件")
	}

	if params.GetType() == constant.GenerateForDiff {
		utAgent.Memory.Diffs = params.GetDiffs()
		logger.Info("【Diff解析完成】 ✅ ")
		logger.InfoT(1, "Diff变更行范围信息如下：")
		for k, v := range utAgent.Memory.Diffs {
			logger.InfoT(2, "- %s: %v", k, v)
		}
	}

	utAgent.Memory.TotalTimer.Start()

	userConfig := conf_parser.GetUserConfig()
	repairLLMModel := params.GetRepairLLMModel()
	if userConfig != nil && userConfig.LLMModelConfig != nil {
		if userConfig.LLMModelConfig.Repair != nil {
			repairLLMModel = userConfig.LLMModelConfig.Repair
		}
		if userConfig.LLMModelConfig.Gen != nil {
			utAgent.llmModelForInfer = userConfig.LLMModelConfig.Gen
		}
	} else {
		utAgent.llmModelForInfer = llm.ModelConfForInnerUtInfer(llm.DC)
	}

	extraParams := &llm.ExtraParams{
		UserName:      params.GetUserName(),
		License:       params.GetLicense(),
		RequestSource: "UTAgent_" + params.GetTrigger(),
	}

	utAgent.llmModelForInfer.ExtraPrams = extraParams
	if repairLLMModel == nil {
		repairLLMModel = llm.ModelConfForInnerUtInfer(llm.DS25)
	}
	repairLLMModel.ExtraPrams = extraParams

	Symbols := make(map[string]string)
	for k, _ := range utAgent.Parser.Project.Pkgs {
		Symbols[k[strings.LastIndex(k, ".")+1:len(k)]] = k
	}
	utAgent.RepairAct = repair.NewAgent("go", Symbols, repairLLMModel)

	if params.GetCovFilePath() != "" {
		// 解析已有覆盖率文件，获取初始覆盖率
		coverages, err := coverage.ParseCovFile(params.GetCovFilePath())
		if err != nil {
			logger.Warn("解析已有覆盖率文件失败，错误信息为: %s", err)
			logger.Warn("开始执行已有用例来获取初始覆盖率 ~")
		} else {
			logger.Info("初始覆盖率解析成功，原始文件路径: %s", params.GetCovFilePath())
			utAgent.Memory.InitOriginalCoverage(coverages)
			utAgent.isParsedCoverage = true
		}
	}

	return &utAgent, nil
}

func (u *UTAgent) AutoDetectAndModifyBuildScript() *models.FrameworkCheck {
	// go无需进行构建脚本的修改
	logger.Info("单测工具检查：")
	var frameworkCheck = new(models.FrameworkCheck)
	frameworkCheck.State = constant.SUCCEED
	frameworkCheck.Tools = []models.Tool{
		{
			Name:    "go",
			Version: u.Environment.GetGoVersion(),
		},
	}
	return frameworkCheck
}

func (u *UTAgent) Call(queue chan<- any) {
	u.PrintSrcMethods()
	logger.Info("【开始 => 目标覆盖率%.2f%%】", float64(u.DesiredCoverage))

	// 遍历文件,发起单测的首次生成
	totalFileNum := len(u.FileMethods)

	curFileNum := 0
	for f, methods := range u.FileMethods {
		f = filepath.ToSlash(f)
		curFileNum++
		u.Memory.FileProcessInQueue = float64(curFileNum) / float64(totalFileNum)

		u.Memory.FileTimerStart(f)
		fileContext := u.getFileContext(f)
		if fileContext == nil {
			logger.DebugT(1, "【文件解析】 %s解析失败，跳过该文件生成\n", f)
			continue
		}
		// 构建被测信息
		underTest := models.UnderTestInfo{
			Language:          constant.LangGo,
			PackageName:       fileContext.Pkg,
			ClassName:         fileContext.FullPkg,
			ClassFullName:     filepath.ToSlash(filepath.Join(fileContext.FullPkg, filepath.Base(f))),
			FilePath:          f,
			RelativeFilePath:  filepath.ToSlash(helper.GetRelativePath(f, u.WorkDir)),
			TargetFileContext: fileContext,
		}

		process := models.Process{
			TotalNum:   totalFileNum,
			CurrentNum: curFileNum,
		}

		if !u.isParsedCoverage {
			// 未解析初始覆盖率，通过执行已有用例，刷新当前覆盖情况
			testFile := GetTestFile(fileContext.Path, "")
			if testFile != nil {
				if testFile.TestFilePath == f {
					continue
				}
				underTest.TestFilePath = testFile.TestFilePath
				u.RunValidAndUpdateCoverage(&models.CaseInfo{
					TestFilePath:    testFile.TestFilePath,
					TestFileContent: testFile.TestFileContent,
					TestClassName:   testFile.TestClassName,
					UnderTest:       underTest,
				})
			}
		}

		logger.InfoT(1, "【覆盖率检查】%s: %s，当前覆盖率为%.2f%%", process.GetFileProcess(),
			underTest.RelativeFilePath, u.Memory.GetFileOriginCoverage(underTest.ClassFullName).GetCoveragePercent())
		var includeMethodStartLn []int
		for start, _ := range methods {
			includeMethodStartLn = append(includeMethodStartLn, start)
		}
		genFuncNum := u.genForFile(fileContext, underTest, includeMethodStartLn, queue, process)
		u.Memory.GetFileProcess(underTest.FilePath).TotalFunc = genFuncNum
		u.Memory.FileTimerPause(f)
	}
}

func (u *UTAgent) genForFile(fileContext *golang.FileContext, underTest models.UnderTestInfo, includeMethods []int, queue chan<- any, process models.Process) int {
	var testFileContext *golang.FileContext
	if underTest.TestFilePath != "" {
		testFileContext, _ = u.Parser.ParseFilePath(underTest.TestFilePath)
	}

	totalFunc := 0
	if fileContext != nil {
		underTest.TestFileContext = testFileContext
		for _, method := range fileContext.Methods {
			if method.IsTestCase {
				// 是否是单侧
				continue
			}
			if len(includeMethods) != 0 && !helper.InSlice(includeMethods, method.Block.StartLine) {
				continue
			}

			underTest.TargetMethod = method
			underTest.ID = fmt.Sprintf("%d#%s#%s_%d", time.Now().UnixNano(), underTest.ClassFullName, method.Identifier, method.Block.StartLine)
			underTest.FuncName = method.Identifier
			underTest.FuncID = fmt.Sprintf("%d_%s_%d", u.totalFunctionCount+1, method.Identifier, method.Block.StartLine)

			underTest.Process = &models.Process{
				TotalNum:   process.TotalNum,
				CurrentNum: process.CurrentNum,

				MaxIterNum:   u.MaxIterations,
				MaxRepairNum: u.RepairMaxIterations,
				Cache:        u.cache,
			}

			if u.CallLLMGen(queue, underTest, "") {
				u.totalFunctionCount++
				totalFunc++
				u.cache.Inc(underTest.FuncID)
			}
		}
	}

	return totalFunc
}

func (u *UTAgent) getFileContext(filePath string) *golang.FileContext {
	fileContext, _ := u.Parser.ParseFilePath(filePath)
	fileContext.Path = filePath
	if fileContext != nil {
		u.goMemory.FileContexts[strings.Join([]string{fileContext.Pkg, fileContext.Path}, ".")] = fileContext
	}

	return fileContext
}
func (u *UTAgent) PrepareAgentClient() {}

func (u *UTAgent) ValidEnv() (bool, error) {
	if u.Environment == nil {
		return true, nil
	}
	if err := u.Environment.CheckEnv(); err != nil {
		return false, err
	}

	return true, nil
}

func (u *UTAgent) DetectSrcBug(caseInfo models.CaseInfo, results *models.RunResult) (bool, string) {
	return false, ""
	//codeInfo, _ := u.Parser.ParseMethod(caseInfo.UnderTest.TargetFileContext.(*golang.FileContext),
	//	caseInfo.UnderTest.TargetMethodsName.(*golang.Method))
	//
	//return bug_detect.IsSrcBug(&models.RepairInput{
	//	SrcFileContent:  codeInfo.TargetCode,
	//	TestFileContent: caseInfo.TestFileContent,
	//	TestFilePath:    fmt.Sprintf("%s.go", caseInfo.TestClassName),
	//	RunResult:       results,
	//	TestFramework:   caseInfo.UnderTest.TestFramework,
	//})
}

// GetFileTimer 函数返回给定文件路径对应的计时器对象指针
// 如果不存在则返回nil
// 参数：
//
//	u *UTAgent - 指向UTAgent结构体的指针
//	filePath string - 文件路径
//
// 返回值：
//
//	*helper.Timer - 指向计时器对象的指针
func (u *UTAgent) GetFileTimer(filePath string) *helper.Timer {
	return u.Memory.GetFileTimer(filePath)
}

func (u *UTAgent) RunValid(caseInfo *models.CaseInfo) *models.RunResult {
	// 针对返回的单测进行处理，逐个进行验证，并写回文件
	// 这里先省略文件的处理，假定只有一个用例
	return u.Environment.RunValid(caseInfo, true)
}

// RunValidAndUpdateCoverage 首次运行，刷新当前用例的代码覆盖情况，并做记忆
func (u *UTAgent) RunValidAndUpdateCoverage(caseInfo *models.CaseInfo) {
	runResult := u.Environment.RunValid(caseInfo, false)
	if runResult == nil {
		return
	}
	u.Memory.InitOriginalCoverage(runResult.ClassCoverageInfo)
}

func (u *UTAgent) Repair(repairInfo *models.RepairInfo) (*models.RepairResult, string, error) {
	caseInfo := repairInfo.CaseInfo
	codeInfo, _ := u.Parser.ParseMethod(caseInfo.UnderTest.TargetFileContext.(*golang.FileContext),
		caseInfo.UnderTest.TargetMethod.(*golang.Method))
	if codeInfo == nil {
		return nil, "", fmt.Errorf("parse method failed")
	}

	targetCode := codeInfo.TargetCode

	input := models.RepairInput{
		SrcFileContent:  targetCode,
		TestFileContent: repairInfo.CaseInfo.TestFileContent,
		TestFileName:    filepath.Base(repairInfo.CaseInfo.TestFilePath),
		TestFilePath:    repairInfo.CaseInfo.TestFilePath,
		TestMethodName:  repairInfo.CaseInfo.TestMethodName,

		RunResult:     repairInfo.RunResult,
		TestFramework: repairInfo.CaseInfo.UnderTest.TestFramework,
		MethodId:      caseInfo.UnderTest.ID,
	}
	if input.TestFramework == "" {
		input.TestFramework = u.Environment.UnitFramework
	}
	return u.RepairAct.Execute(input)
}

func (u *UTAgent) GetMemory() *base.Memory {
	return u.Memory
}

// CallLLMGen 调用LLMGen方法生成测试用例
// 参数：
//
//	queue chan<- any - 用于将生成的测试用例写入队列的通道
//	underTest models.UnderTestInfo - 待生成测试用例的方法信息
//
// 返回值：
//
//	bool - 是否需要继续生成测试用例，返回false说明无需再生成
func (u *UTAgent) CallLLMGen(queue chan<- any, underTest models.UnderTestInfo, testMethodName string) bool {
	targetMethod := underTest.TargetMethod.(*golang.Method)
	coverageInfo := u.GetRangeCoverage(underTest.ClassFullName, targetMethod.Block.StartLine+1,
		targetMethod.Block.EndLine+1)
	var actionName string

	iterNum := underTest.Process.IncrIteration(underTest.FuncID)

	if coverageInfo.GetCoverage() > 0 {
		if len(coverageInfo.MissedLines) == 0 {
			// 方法级别覆盖率已100%
			logger.InfoT(2, "【用例生成 round#%d】%s: %s 100%%覆盖，跳过生成", iterNum,
				underTest.Process.GetFileProcess(),
				underTest.ID)
			return false
		}
		// 以覆盖率为牵引，重新生成
		actionName = basegen.CoverageRegen
		logger.DebugT(2, "【用例生成 round#%d】%s: 以覆盖率为牵引为方法%s生成用例，剩余 %d 行未被覆盖",
			iterNum,
			underTest.Process.GetFileProcess(),
			underTest.ID,
			len(coverageInfo.MissedLines))
	} else {
		// 从0开始生成
		actionName = basegen.FirstGen
		logger.DebugT(2, "【用例生成 round#%d】%s: 为方法%s生成用例",
			iterNum,
			underTest.Process.GetFileProcess(),
			underTest.ID)
	}

	// 不指定模型，由实例内部默认
	underTest.LLMGenAction = gen.NewUtGenActionForGo(u.llmModelForInfer, u.Parser, &basegen.Input{
		TargetMethod:    underTest.TargetMethod.(*golang.Method),
		FileContext:     underTest.TargetFileContext.(*golang.FileContext),
		TestFileContext: underTest.TestFileContext.(*golang.FileContext),
		CoverageInfo:    coverageInfo,
		Framework:       underTest.TestFramework,
		MethodId:        underTest.ID,
		TestFilePath:    underTest.TestFilePath,
	}, actionName)

	go func() {
		queue <- underTest
	}()

	return true
}

func (u *UTAgent) CollectCoverageAndIsUp(newCoverage map[string]*coverage.CoverageInfo, cls string) (bool, *coverage.CoverageInfo) {
	oldCoverage := u.Memory.GetFileCoverage(cls)
	if newCoverage == nil || newCoverage[cls] == nil || len(newCoverage[cls].CoveredLines) == 0 {
		return false, oldCoverage
	}

	if oldCoverage == nil || coverage.IsCoverageUp(newCoverage[cls], oldCoverage) {
		u.Memory.UpdateCoverage(newCoverage)
		return true, u.Memory.GetFileCoverage(cls)
	}

	return false, oldCoverage
}

func (u *UTAgent) GetRunCmd() string {
	return u.Environment.RunCmd
}

func (u *UTAgent) GetFileCoverage(cls string) *coverage.CoverageInfo {
	cov := u.Memory.GetFileCoverage(cls)
	if cov == nil {
		return &coverage.CoverageInfo{}
	}
	return cov
}

func (u *UTAgent) GetFileCoverageByDiff(cls string) *coverage.CoverageInfo {
	if diffs, ok := u.Memory.Diffs[cls]; ok && len(diffs) > 0 {
		cov := u.Memory.GetIncFileCov(cls, diffs, constant.CurrentCoverage)
		if cov != nil {
			return cov
		}
	}
	return &coverage.CoverageInfo{}
}

func (u *UTAgent) GetRangeCoverage(classFullPath string, startLine int, endLine int) *coverage.CoverageInfo {
	return u.Memory.GetRangeCoverage(classFullPath, startLine, endLine)
}

func (u *UTAgent) PrintSrcMethods() {
	logger.Info("【待测文件列表】")
	for f, m := range u.FileMethods {
		if len(m) == 0 {
			logger.InfoT(1, "文件：%s", f)
		} else {
			logger.InfoT(1, "文件：%s，待测方法数：%d", f, len(m))
		}
	}
}

func getCls(workDir, f string, parser *golang.Parser, fileCoverage map[string]*base.CoverageRecord) string {

	var cls string
	if len(parser.Project.Model) > 0 {
		fileContext, _ := parser.ParseFilePath(f)
		cls = filepath.ToSlash(filepath.Join(fileContext.FullPkg, filepath.Base(f)))
	} else {
		relateFilePath := strings.TrimPrefix(f, workDir)
		if strings.HasPrefix(relateFilePath, "/") {
			relateFilePath = relateFilePath[1:]
		}
		for file, _ := range fileCoverage {
			if strings.HasSuffix(file, relateFilePath) {
				if len(cls) == 0 {
					cls = file
				}
				if len(file) < len(cls) {
					cls = file
				}
			}
		}
	}
	return cls

}

func (u *UTAgent) AssertReason(caseInfo models.CaseInfo, results *models.RunResult) (bool, string) {
	codeInfo, _ := u.Parser.ParseMethod(caseInfo.UnderTest.TargetFileContext.(*golang.FileContext),
		caseInfo.UnderTest.TargetMethod.(*golang.Method))

	return bug_detect.IsSrcBug(&models.RepairInput{
		SrcFileContent:  codeInfo.TargetCode,
		TestFileContent: caseInfo.TestFileContent,
		TestFilePath:    fmt.Sprintf("%s.java", caseInfo.TestClassName),
		RunResult:       results,
		TestFramework:   caseInfo.UnderTest.TestFramework,
	})
}

func (u *UTAgent) GetTestContent(underTestInfo models.UnderTestInfo) string {
	codeInfo, _ := u.Parser.ParseMethod(underTestInfo.TargetFileContext.(*golang.FileContext),
		underTestInfo.TargetMethod.(*golang.Method))
	if codeInfo == nil {
		return ""
	}
	return codeInfo.TargetCode
}

func (u *UTAgent) MergeLLmResultWithTestFile(underTest models.UnderTestInfo, result *models.UtFileResult) string {
	memoryGenUtCases := u.GetMemory().GeneratedUnitTest[result.TestFilePath]
	var succMethods []string
	for _, c := range memoryGenUtCases {
		if c.Bug != nil {
			continue
		}
		succMethods = append(succMethods, c.CaseName)
	}
	userExistFile := GetTestFile(underTest.FilePath, underTest.FuncName)
	fileParser, err := golang.NewFileParserWithContent(result.TestFileContent)
	if err != nil {
		return ""
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse()
	if llmTestFileContext == nil || llmTestFileContext.Methods == nil {
		return ""
	}
	if userExistFile != nil {
		mergeResult := file_processor.Merge(underTest.FuncName, underTest.PackageName, llmTestFileContext, succMethods, userExistFile.TestFilePath, userExistFile.TestFileContent)
		return mergeResult.MergedResult
	} else {
		mergeResult := file_processor.Merge(underTest.FuncName, underTest.PackageName, llmTestFileContext, succMethods, underTest.TestFilePath, userExistFile.TestFileContent)
		return mergeResult.MergedResult
	}
}

func (u *UTAgent) GetTestMethod(caseInfo *models.CaseInfo) (interface{}, []*models.Method) {
	// 解析新生成的UT，获取用例类名及用例方法列表
	fileParser, err := golang.NewFileParserWithContent(caseInfo.TestFileContent)
	if err != nil {
		return nil, nil
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse()
	if llmTestFileContext == nil || llmTestFileContext.Methods == nil {
		return nil, nil
	}

	methods := llmTestFileContext.Methods
	var newMethods []*models.Method
	className := llmTestFileContext.Pkg
	for _, m := range methods {
		newMethods = append(newMethods, &models.Method{
			IsTestCase: m.IsTestCase, //是否是测试用例
			Body:       m.Block.Content,

			Identifier: m.Identifier, //方法名
			ClassName:  className,
		})
	}
	return llmTestFileContext, newMethods
}

func (u *UTAgent) MergeLLMResponseToUserTestFile(fileContext interface{}, method *models.Method, caseInfo *models.CaseInfo, testFile *models.TestFile) (result string, err error) {
	var llmTestFileContext *golang.FileContext
	var ok bool
	if llmTestFileContext, ok = fileContext.(*golang.FileContext); !ok {
		return "", fmt.Errorf("LLM测试文件解析失败")
	}

	if testFile == nil {
		testFile = &models.TestFile{}
	}

	if testFile.TestFilePath == "" {
		if caseInfo.TestFilePath != "" {
			testFile.TestFilePath = caseInfo.TestFilePath
			testFile.TestFileContent = helper.ReadFile(testFile.TestFilePath)
		} else {
			testFile.TestFilePath = GetTestFilePath(caseInfo.UnderTest.FilePath)
			testFile.TestFileContent = helper.ReadFile(testFile.TestFilePath)
		}

		if caseInfo.TestClassName != "" {
			testFile.TestClassName = caseInfo.TestClassName
		} else {
			testFile.TestClassName = method.ClassName
		}
	}

	// 合并指定用例到用户已有用例文件中
	mergeResult := file_processor.Merge(caseInfo.UnderTest.FuncName, caseInfo.UnderTest.PackageName,
		llmTestFileContext, []string{method.Identifier}, testFile.TestFilePath, testFile.TestFileContent)

	if mergeResult != nil && mergeResult.MergeStatus.Status == types.SUCCESS {
		result = mergeResult.MergeStatus.SubStatus
		switch mergeResult.MergeStatus.SubStatus {
		case types.SubStatusNoChange:
			err = fmt.Errorf("重复用例，跳过验证")
		case types.SubStatusChanged:
			caseInfo.TestFileContent = mergeResult.MergedResult
		case types.SubStatusNewFile:
			// 需要新文件保存，因此代码中的类信息相应需做更新
			newTestClassName := rebuildClassName(filepath.Dir(testFile.TestFilePath),
				caseInfo.UnderTest.ClassName+"_"+caseInfo.UnderTest.FuncName, 0) + "_test"
			newContent := mergeResult.MergedResult
			caseInfo.TestClassName = newTestClassName
			caseInfo.TestFileContent = newContent
			caseInfo.TestFilePath = filepath.Join(filepath.Dir(testFile.TestFilePath),
				newTestClassName+".go")
			testFile.TestClassName = newTestClassName
			testFile.TestFilePath = caseInfo.TestFilePath
			testFile.TestFileContent = "" // 清空内容, 因为新文件没有成功的用例，新创建的
		default:
			err = fmt.Errorf("未知的合并状态[%s]，请分析", result)
			return
		}
	} else {
		err = fmt.Errorf("合并失败，其他错误，请分析")
	}
	return
}

func (u *UTAgent) GetFullClsNameByFilePath(filePath string) string {
	return getCls(u.WorkDir, filePath, u.Parser, u.Memory.Coverages)
}

func rebuildClassName(dir, clsName string, suffix int) string {
	var name string
	if suffix > 0 {
		name = fmt.Sprintf("%s_%d", clsName, suffix)
	} else {
		name = clsName
	}

	if !helper.IsFileExist(filepath.Join(dir, name+"_test.go")) {
		return name
	} else {
		return rebuildClassName(dir, clsName, suffix+1)
	}
}
