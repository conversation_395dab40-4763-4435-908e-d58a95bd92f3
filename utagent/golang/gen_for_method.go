package golang

import (
	"errors"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/file_processor"
	basegen "icode.baidu.com/baidu/cov/iUT/utagent/components/gen"
	"icode.baidu.com/baidu/cov/iUT/utagent/golang/gen"
	"icode.baidu.com/baidu/cov/smartUT-parser/golang"
)

func (u *UTAgent) FirstGenerateForMethod() *base.FirstGeneratedInfoForMethod {
	// 首次生成，生成后把内容缓存下来
	for f, methods := range u.FileMethods {
		for startLn, _ := range methods {
			fileContext := u.getFileContext(f)
			if fileContext == nil {
				continue
			}
			for _, m := range fileContext.Methods {
				if m.Block.StartLine == startLn {
					testFilePath := GetTestFilePath(f)
					genInput := &basegen.Input{
						TargetMethod: m,
						FileContext:  fileContext,
					}
					if testFilePath != "" {
						genInput.TestFileContext, _ = u.Parser.ParseFilePath(testFilePath)
					}
					llmService := gen.NewLLMUtFirstGen(u.llmModelForInfer, u.Parser, genInput)
					result, err := llmService.Predict()
					if err != nil {
						logger.Error("首次生成失败，错误信息为: %s", err)
						return nil
					}

					firstGeneratedInfoForMethod := &base.FirstGeneratedInfoForMethod{
						OriginLLMResult: result,
					}

					testContent, testFilePath, methodsNeedMerge, err := processLLMResult(result, m, genInput.TestFileContext, fileContext)
					firstGeneratedInfoForMethod.TargetMethodName = m.Identifier
					firstGeneratedInfoForMethod.MergedUtFileContent = testContent
					firstGeneratedInfoForMethod.GenCaseNameList = methodsNeedMerge
					firstGeneratedInfoForMethod.TestFilePath = testFilePath
					firstGeneratedInfoForMethod.FirstGenerateError = err
					return firstGeneratedInfoForMethod
				}
			}
		}
	}
	return nil
}

func processLLMResult(llmResult string, targetMethod *golang.Method,
	TestFileContext interface{}, SrcFileContext *golang.FileContext) (testContent, testFilePath string, methodsNeedMerge []string, LLMResultError error) {
	fileParser, err := golang.NewFileParserWithContent(llmResult)
	if err != nil {
		return
	}
	// 新生成单测解析时，增加解析条件
	llmTestFileContext := fileParser.Parse()
	if llmTestFileContext == nil || llmTestFileContext.Methods == nil {
		LLMResultError = errors.New("模型首次生成的结果超长导致截断，无法继续进行后续的执行验证。")
		return
	}

	var caseNum int
	for _, m := range llmTestFileContext.Methods {
		if m.IsTestCase {
			caseNum++
			methodsNeedMerge = append(methodsNeedMerge, m.Identifier)
		}
	}

	srcFilePath := SrcFileContext.Path

	if TestFileContext != nil && TestFileContext.(*golang.FileContext) != nil {
		testFileContext := TestFileContext.(*golang.FileContext)
		mergeResult := file_processor.Merge(targetMethod.Identifier, SrcFileContext.Pkg, llmTestFileContext, methodsNeedMerge, testFileContext.Path, testFileContext.FileContent)
		testContent = mergeResult.MergedResult
		testFilePath = testFileContext.Path
	} else {
		testContent = llmResult
		testFilePath = GetTestFilePath(srcFilePath)
	}
	return
}
