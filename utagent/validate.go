package utagent

import (
	"context"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/utagent/java"
	"path"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"sync"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	"icode.baidu.com/baidu/cov/iUT/utagent/components/file_processor"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"icode.baidu.com/baidu/cov/iUT/utagent/mongo"
)

type FileLockManager struct {
	locks map[string]*sync.Mutex
	mu    sync.Mutex

	lockCount map[string]int
}

func NewFileLockManager() *FileLockManager {
	return &FileLockManager{
		locks:     make(map[string]*sync.Mutex),
		lockCount: make(map[string]int),
	}
}

func (m *FileLockManager) GetLock(file string) *sync.Mutex {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, ok := m.lockCount[file]; !ok {
		m.lockCount[file] = 1
	} else {
		m.lockCount[file]++
	}

	standardPath, err := filepath.Abs(filepath.Clean(file))
	if err != nil {
		logger.Error("Failed to standardize file path: %s, error: %v", file, err)
		return &sync.Mutex{}
	}

	if lock, exists := m.locks[standardPath]; exists {
		return lock
	}

	newLock := &sync.Mutex{}
	m.locks[standardPath] = newLock
	return newLock
}

func (m *FileLockManager) ReleaseLock(file string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	standardPath, err := filepath.Abs(filepath.Clean(file))
	if err != nil {
		return
	}

	if _, ok := m.lockCount[standardPath]; !ok {
		return
	} else {
		m.lockCount[standardPath]--
		if m.lockCount[standardPath] == 0 {
			delete(m.lockCount, standardPath)
			delete(m.locks, standardPath)
		}
	}

}

func (generator *UTGenerator) validationInBackground(ctx context.Context) {
	defer generator.wg.Done()

	maxParallelValidations := helper.GetAvailableMemory()
	if generator.param.GetLang() == constant.LangGo {
		maxParallelValidations = 1
	}
	logger.InfoT(1, fmt.Sprintf("【结果验证】最大并行验证数：%d", maxParallelValidations))

	semaphore := make(chan struct{}, maxParallelValidations)
	defer close(semaphore)

	//var fileValidationLocks sync.Map
	var workerWg sync.WaitGroup
	lockerMannager := NewFileLockManager()

	// 启动多个并发工作线程
	for i := 0; i < maxParallelValidations; i++ {
		workerWg.Add(1)
		//pID := i
		go func() {
			defer workerWg.Done()
			for {
				select {
				case <-ctx.Done():
					//logger.Info("【线程%d】 : 退出验证", pID)
					return
				case utContent, ok := <-generator.QueueForValidation:
					logger.Info("【验证】队列长度%v", len(generator.QueueForValidation))
					func() {
						defer func() {
							if r := recover(); r != nil {
								buf := make([]byte, 60*1024)
								n := runtime.Stack(buf, false)
								logger.Error("验证程序异常，发生panic:\n%v\n%s", r, debug.Stack())
								logger.Debug("堆栈信息：\n%s", string(buf[:n]))
								logger.ErrorT(1, "【修复 】处理消息时发生异常：%v", r)
							}
						}()

						if !ok {
							logger.Error("QueueForValidation closed, exiting...")
							return
						}
						if utContent == nil {
							logger.Error("Received nil from QueueForValidation, skipping...")
							return
						}

						caseInfo, ok := utContent.(models.CaseInfo)
						if !ok {
							logger.Error("Received invalid data from QueueForValidation, skipping...")
							return
						}
						filePath := caseInfo.UnderTest.FilePath

						if generator.checkCoverageAndExit(caseInfo.UnderTest) {
							return
						}

						// 控制协程个数
						semaphore <- struct{}{}
						defer func() { <-semaphore }()

						// 获取文件锁
						lock := lockerMannager.GetLock(filePath)
						lock.Lock()
						defer func() {
							lock.Unlock()
							lockerMannager.ReleaseLock(filePath)
						}()

						// 启动一个验证任务
						logger.Info("【验证】开始验证: %s - %s", caseInfo.UnderTest.FuncID, caseInfo.TestMethodName)
						if generator.param.GetType() == constant.GenerateForDiff {
							generator.notifyLoadingMsg(fmt.Sprintf(constant.LoadingMsgForVerify, path.Base(caseInfo.UnderTest.FilePath)))
						}

						generator.ValidateCase(&caseInfo, filePath)
						if caseInfo.TestMethodName == "" {
							// 非修复验证
							logger.Info("【验证结束】%s 本次迭代验证完毕", caseInfo.UnderTest.FuncID)
							caseInfo.UnderTest.Process.SetCurrentIterExecuted()
						}
						generator.checkEnterNextIteration(caseInfo.UnderTest)
					}()

				default:
					time.Sleep(1 * time.Second)
					//logger.Info("[验证对列]剩余长度：%d", len(generator.QueueForValidation))
				}
			}
		}()
	}

	workerWg.Wait()
}

// saveCase 函数用于保存测试用例信息到文件中。
//
// 参数:
//
//	testFile: 需要保存的测试文件信息。
//	caseInfo: 测试用例信息。
//	oldCoverage: 旧的代码覆盖率。
func (generator *UTGenerator) saveCase(testFile *models.TestFile, caseInfo models.CaseInfo, oldCoverage float64) {
	// IDE场景下不写入用户文件，写入缓存中进行记录
	generator.UTAgent.GetMemory().UpdateTmpValidTestFile(caseInfo.UnderTest.FilePath, testFile)
	if generator.param.GetTrigger() == constant.IDETrigger {
		return
	}
	if err := file_processor.Save(testFile.TestFileContent, testFile.TestFilePath); err == nil {
		// 有写入时，记录写入的单测文件路径
		generator.UTAgent.GetMemory().SaveTestFilePath(caseInfo.UnderTest.FilePath, testFile.TestFilePath)

		// ICODE场景下，有写入时通知调用方
		//generator.notifyUtResult(caseInfo.UnderTest, testFile.TestFilePath, "")
		logger.InfoTGreen(1, "【Round#%d - 存储】覆盖率提升，写入文件%s, 覆盖率%0.2f%% -> %0.2f%% 📈",
			caseInfo.UnderTest.Process.GetIterNum(caseInfo.UnderTest.FuncID, caseInfo.TestMethodName),
			helper.GetRelativePath(testFile.TestFilePath, generator.WorkDir),
			oldCoverage,
			generator.UTAgent.GetFileCoverage(caseInfo.UnderTest.ClassFullName).GetCoveragePercent())
	} else {
		logger.ErrorT(1, "【写入文件错误】", err)
	}
}

func (generator *UTGenerator) handleSaveCaseStrategy(results *models.RunResult, caseInfo models.CaseInfo) (needSave bool,
	caseStatus string, reason string) {
	compilePass, runPass := results.CollectRunResult(caseInfo.TestMethodName)
	caseStatus = constant.RunFail
	if generator.param.GetSaveCaseStrategy() == "" || generator.param.GetSaveCaseStrategy() == models.SaveRunPassCaseStrategy {
		if compilePass && runPass {
			caseStatus = constant.RunPass
		} else if compilePass {
			caseStatus = constant.AssertFail
		}
		return compilePass && runPass, caseStatus, ""
	} else {
		if compilePass && runPass {
			// 编译通过且断言通过
			needSave = true
			caseStatus = constant.RunPass
		} else if compilePass {
			// 编译通过，断言失败
			isSrcBug, bugReason := generator.UTAgent.DetectSrcBug(caseInfo, results)
			if isSrcBug {
				needSave = true
				caseStatus = constant.AssertFail
				reason = bugReason
			} else {
				needSave = false
				caseStatus = constant.AssertFail
			}
		} else {
			// 编译失败，断言失败
			needSave = false
		}
	}
	return
}

func (generator *UTGenerator) ValidateCase(caseInfo *models.CaseInfo, filePath string) {
	generator.UTAgent.GetFileTimer(filePath).Start()
	defer generator.UTAgent.GetFileTimer(filePath).Pause()

	// 当测试用例不完整，只有方法结构时，返回的llmTestFileContext为nil
	llmTestFileContext, methods := generator.UTAgent.GetTestMethod(caseInfo)
	if llmTestFileContext == nil {
		caseInfo.TestFilePath = java.GetTestFilePath(filePath)
	}

	executeNum := 0
	for _, method := range methods {
		if !method.IsTestCase || (caseInfo.TestMethodName != "" && method.Identifier != caseInfo.TestMethodName) {
			continue
		}

		// 包含敏感词，不修复，直接终止
		if generator.sensitiveWordsFilter != nil && generator.sensitiveWordsFilter.Filter(method.Body) {
			// 如果用例包含敏感信息，大概率之后还是会生成敏感用例，所以终止该用例的修复
			logger.InfoT(2, "%s#%s 检测到敏感词, 终止该场景修复！", caseInfo.TestClassName, method.Identifier)
			caseInfo.UnderTest.Process.DeleteTestMethodIteration(caseInfo.UnderTest.FuncID, method.Identifier)
			continue
		}
		executeNum++
		repairInfo, succeedCaseNum, validCaseNum, coverageReached := generator.validateAndCollectRepairInfos(llmTestFileContext, *caseInfo, method)
		caseInfo.UnderTest.Process.ValidUTCase += validCaseNum

		// 如果覆盖率达标，不再修复其它，直接终止，并退出
		if coverageReached {
			logger.InfoT(2, "%s#%s 覆盖率达标，终止该场景用例修复！", method.ClassName, method.Identifier)
			// 终止该被测其他用例的修复
			caseInfo.UnderTest.Process.EndRepairInteraction(caseInfo.UnderTest.FuncID)
			caseInfo.UnderTest.Process.EndProcess()
			break
		}

		if succeedCaseNum != 0 {
			logger.InfoT(2, "%s#%s 用例正确，终止该场景用例修复！", method.ClassName, method.Identifier)
			caseInfo.UnderTest.Process.DeleteTestMethodIteration(caseInfo.UnderTest.FuncID, method.Identifier)
			caseInfo.UnderTest.SucceedTestMethods = append(caseInfo.UnderTest.SucceedTestMethods, method.Identifier)
		} else if repairInfo != nil {
			logger.InfoT(2, "%s#%s 用例准备进入修复", method.ClassName, method.Identifier)
			// 说明有多个用例待验证，需要及时修复
			if !caseInfo.UnderTest.Process.IsRepairInteractionFinished(caseInfo.UnderTest.FuncID, repairInfo.CaseInfo.TestMethodName) {
				// 首次修复
				repairCount := caseInfo.UnderTest.Process.IncrRepairIteration(caseInfo.UnderTest.FuncID, method.Identifier)
				generator.cache.Inc(caseInfo.UnderTest.FuncID)
				logger.Info("%s#%s 用例进入第%d轮修复. 被测方法：%s", method.ClassName, method.Identifier, repairCount,
					caseInfo.UnderTest.FuncID)
				go func() {
					generator.QueueForRepair <- *repairInfo
				}()
			} else {
				// 达到最大修复次数
				logger.InfoT(2, "%s#%s 达到最大修复次数，终止该场景用例修复！", method.ClassName, method.Identifier)
				caseInfo.UnderTest.Process.DeleteTestMethodIteration(caseInfo.UnderTest.FuncID, method.Identifier)
			}
		} else {
			logger.InfoT(2, "%s#%s 用例错误，未收集到运行错误信息, 终止该场景用例修复！", method.ClassName, method.Identifier)
			caseInfo.UnderTest.Process.DeleteTestMethodIteration(caseInfo.UnderTest.FuncID, method.Identifier)
		}
	}

	if executeNum == 0 {
		logger.ErrorT(1, "【结果验证】%s 未找到有效的测试方法，终止该场景验证！", caseInfo.UnderTest.FuncID)
		logger.Error(caseInfo.TestFileContent)
		if caseInfo.TestMethodName != "" {
			// 修复验证失败处理
			caseInfo.UnderTest.Process.DeleteTestMethodIteration(caseInfo.UnderTest.FuncID, caseInfo.TestMethodName)
		}
	}

}

// 解析运行结果，返回待修复用例及覆盖率是否达标信息

func (generator *UTGenerator) validateAndCollectRepairInfos(llmTestFileContext interface{}, caseInfo models.CaseInfo, m *models.Method) (*models.RepairInfo, int, int, bool) {
	u := generator.UTAgent
	// 获取被测文件的旧有覆盖率
	oldCoverage := u.GetFileCoverage(caseInfo.UnderTest.ClassFullName).GetCoveragePercent()

	round := caseInfo.UnderTest.Process.GetIterNum(caseInfo.UnderTest.FuncID, m.Identifier)

	isRepairAction := false
	field := fmt.Sprintf("【验证 Round#%d】%s: ",
		round,
		caseInfo.UnderTest.Process.GetFileProcess())
	// 修复后验证，还是首次验证判断
	if caseInfo.RepairResult.RepairID != "" {
		isRepairAction = true
		field = fmt.Sprintf("【修复后验证 Round#%d】%s: ",
			round,
			caseInfo.UnderTest.Process.GetFileProcess())
	}

	t := helper.NewTimer()
	l := logger.NewProcessLogger()
	defer l.Close()

	isSucceed := false
	isValid := false

	defer func() {
		if isRepairAction {
			tag := "失败 👎"
			if isSucceed {
				u.GetMemory().Summary.RepairSucceedCaseNum.Add(1)
				if isValid {
					u.GetMemory().Summary.RepairValidCaseNum.Add(1)
				}

				tag = "成功👍"
				u.GetMemory().Summary.UpdateRepair(caseInfo.RepairResult.RepairTool, true)
			}

			l.EndWithColor(logger.Repair, 2, "%s %s#%s，已完成用例 %s 的验证, 修复%s 耗时 %s",
				field,
				caseInfo.UnderTest.ClassFullName,
				caseInfo.UnderTest.FuncName,
				m.Identifier,
				tag,
				helper.FormatMS(t.Duration()),
			)
		}
	}()

	t.Start()
	var testFile models.TestFile // 存储验证成功的最新合并文件信息
	// 先读取memory中存储的tmp内容，再从用户文件中读取
	if tmpTestFile := u.GetMemory().GetTmpValidTestFile(caseInfo.UnderTest.FilePath); tmpTestFile != nil {
		testFile = *tmpTestFile
	}

	caseInfo.TestMethodBody = m.Body
	caseInfo.TestMethodName = m.Identifier
	result, err := generator.UTAgent.MergeLLMResponseToUserTestFile(llmTestFileContext, m, &caseInfo, &testFile)

	if err != nil {
		l.EndT(2, "%s 用例%s#%s 合并失败，失败原因：%s %s",
			field,
			caseInfo.TestClassName, caseInfo.TestMethodName, result, err.Error())
		mongo.MergeFailCase(context.Background(), caseInfo.MongoID, err.Error())
		return nil, 0, 0, false
	}

	// 合并成功，开始验证
	l.InfoT(2, "%s 用例%s#%s 合并成功，合并结果：%s, 开始验证", field, caseInfo.TestClassName, caseInfo.TestMethodName, result)
	u.GetMemory().Summary.GeneratedCaseNum.Add(1)

	caseInfo.TestMethodName = m.Identifier
	caseInfo.TestFilePath = testFile.TestFilePath

	res := u.RunValid(&caseInfo)
	isPass := res.IsRunPass()
	if isPass {
		u.GetMemory().Summary.PassCaseNum.Add(1)
	}
	mongo.UpdateStatus(context.Background(), caseInfo.MongoID, isPass)
	t.Pause()

	needSaveCase, caseStatus, bugReason := generator.handleSaveCaseStrategy(res, caseInfo)
	if needSaveCase {
		isSucceed = true
		// 收集覆盖率
		if isUp, _ := u.CollectCoverageAndIsUp(res.ClassCoverageInfo, caseInfo.UnderTest.ClassFullName); isUp {
			// 记录已生成的有效的case
			isValid = true
			generatedCase := models.GenUnitTestCase{
				CaseName: caseInfo.TestMethodName,
			}
			if caseStatus != constant.RunPass {
				// 断言失败的case，保存case详情
				errorBug := res.CollectErrorBug(caseInfo.TestMethodName, filepath.Base(caseInfo.TestFilePath))
				l.InfoT(2, "%s 用例%s#%s 断言错误，收集bug：%v",
					field, caseInfo.TestClassName, caseInfo.TestMethodName, errorBug)
				generatedCase.Bug = errorBug
				if bugReason != "" {
					generatedCase.Bug.BugMsg += "\n" + bugReason
				}
			}
			// 更新memory
			u.GetMemory().AddGeneratedValidCaseDetail(testFile.TestFilePath, generatedCase)
			u.GetMemory().Summary.ValidCaseNum.Add(1)

			// 有valid用例后，format结果文件
			testFile.TestFileContent = models.FormatContentWithTool(generator.param.GetLang(), caseInfo.TestFileContent, testFile.TestFilePath)
			generator.notifyUtResult(caseInfo.UnderTest, testFile.TestFilePath, testFile.TestFileContent)
			// 提前保存case
			generator.saveCase(&testFile, caseInfo, oldCoverage)

			if generator.IsCoverageReached(caseInfo.UnderTest, float64(generator.DesiredCoverage)) {
				caseInfo.UnderTest.Process.SetIterNum(caseInfo.UnderTest.FuncID, caseInfo.UnderTest.Process.MaxIterNum)
				// 覆盖率达到要求，退出，无需后续修复
				l.EndWithColor(logger.Exec, 2, "%s %s#%s，已完成用例 %s 的验证, 覆盖率已达标，提前退出，耗时 %s",
					field,
					caseInfo.UnderTest.ClassFullName,
					caseInfo.UnderTest.FuncName,
					caseInfo.TestMethodName,
					helper.FormatMS(t.Duration()),
				)
				return nil, 1, 1, true
			}
			l.EndT(2, "%s 用例%s#%s 结果: %s, 保存: %v, 覆盖率提升 ⬆️",
				field, caseInfo.TestClassName, caseInfo.TestMethodName, base.GetTag(caseStatus), needSaveCase)
			return nil, 1, 1, false
		} else {
			l.EndT(2, "%s 用例%s#%s 结果: %s, 保存: false, 覆盖率未提升 ",
				field, caseInfo.TestClassName, caseInfo.TestMethodName, base.GetTag(caseStatus))
			return nil, 1, 0, false
		}
	} else {
		l.EndT(2, "%s 用例%s#%s 结果: %s, 保存: %v",
			field, caseInfo.TestClassName, caseInfo.TestMethodName, base.GetTag(caseStatus), needSaveCase)
	}

	repairInfo := &models.RepairInfo{
		RunResult: res,
		CaseInfo:  caseInfo,
	}
	return repairInfo, 0, 0, false
}

func (generator *UTGenerator) IsCoverageReached(underTest models.UnderTestInfo, goal float64) bool {
	targetMethod := underTest.GetTargetMethod()
	classFullPath := underTest.ClassFullName
	u := generator.UTAgent
	if generator.param.GetType() == constant.GenerateForDiff {
		// 文件覆盖率达标or增量覆盖率达标or被测方法级覆盖率达100%
		return u.GetFileCoverage(classFullPath).IsCoverageReached(goal) ||
			u.GetFileCoverageByDiff(classFullPath).IsCoverageReached(goal) ||
			u.GetMemory().GetRangeCoverage(classFullPath, targetMethod.StartLine, targetMethod.EndLine).IsCoverageReached(100)
	}
	// 文件覆盖率达标or方法级覆盖率达标
	return u.GetFileCoverage(classFullPath).IsCoverageReached(goal) ||
		u.GetMemory().GetRangeCoverage(classFullPath, targetMethod.StartLine, targetMethod.EndLine).IsCoverageReached(100)
}

func (generator *UTGenerator) checkEnterNextIteration(underTest models.UnderTestInfo) {
	logger.InfoT(1, "【验证结束 %s】检查是否进入下一轮大迭代：%s", underTest.Process.GetRound(), underTest.FuncID)
	generator.cache.Dec(underTest.FuncID)

	if !underTest.Process.IsRepairRoundFinished(underTest.FuncID) {
		logger.InfoT(2, "被测方法 【%s】尚未完整结束修复的子迭代", underTest.FuncID)
		return
	}

	if underTest.Process.IsInteractionFinished(underTest.FuncID) {
		generator.deleteFromCache(underTest.FilePath, underTest, models.ReasonMaxIterationReached)
		return
	}

	if generator.UTAgent.CallLLMGen(generator.QueueForLLMCall, underTest, "") {
		// 放入模型推理队列，流程加一
		logger.InfoT(2, "被测方法【%s】进入新一轮大迭代", underTest.FuncID)
		generator.cache.Inc(underTest.FuncID)
	} else {
		// 唯一失败原因为覆盖率达标，因此终止该被测方法的测试生成
		logger.InfoT(2, "被测方法【%s】覆盖率达标，提前终止迭代", underTest.FuncID)
		underTest.Process.EndProcess()
		generator.deleteFromCache(underTest.FilePath, underTest, models.ReasonCoverageReached)
	}
}
