package llm

import (
	"errors"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"net"
	"strings"
	"time"
)

const DCPrompt = `<|BOS|><|SYS|>
你是一位乐于助人、受人尊敬且诚实的Comate代码助手，对代码和软件设计有深入的了解，基于Baidu开发的DeepCoder模型构建。在保证安全的情况下，始终尽可能地回答有帮助的问题。您的答案不应包含任何有害、不道德、种族主义、性别歧视、有毒、危险或非法内容。请确保您的回答在社会上没有偏见并且本质上是积极的。

如果问题没有任何意义，或者实际上不连贯，请解释原因，而不是回答不正确的问题。如果您不知道问题的答案，请不要分享虚假信息。

<|USER|>
%s
<|AGENT|>`

type DCService struct {
	endpoint  string
	modelConf *ModelConf
}

func NewDCService(modelConf *ModelConf) *DCService {
	endpoint := modelConf.EndPoint
	if !strings.HasPrefix(endpoint, "http") {
		endpoint = "http://" + endpoint
	}
	m := DCService{
		endpoint:  endpoint,
		modelConf: modelConf,
	}
	return &m
}

type DCData struct {
	Parameters *DCParams `json:"parameters"`
	Inputs     string    `json:"inputs"`
}

type DCParams struct {
	TopP           float32  `json:"top_p"`
	Temperature    float32  `json:"temperature"`
	PenaltyScore   float32  `json:"penalty_score"`
	TopK           float32  `json:"top_k"`
	MaxNewTokens   int      `json:"max_new_tokens"`
	ReturnFullText bool     `json:"return_full_text"`
	Stop           []string `json:"stop"`
}

func NewDCParams(hyperParams *HyperParam) *DCParams {
	params := &DCParams{
		TopP:           0.95,
		Temperature:    0.7,
		PenaltyScore:   1.0,
		TopK:           10,
		MaxNewTokens:   4000,
		ReturnFullText: false,
		Stop:           []string{"EOT"},
	}
	if hyperParams != nil {
		if hyperParams.Temperature > 0 {
			params.Temperature = hyperParams.Temperature
		}
		if hyperParams.TopP > 0 {
			params.TopP = hyperParams.TopP
		}
		if hyperParams.PenaltyScore > 0 {
			params.PenaltyScore = hyperParams.PenaltyScore
		}
	}
	return params
}

func (m *DCService) Inference(prompt string) (string, error) {
	data := &DCData{
		Parameters: NewDCParams(m.modelConf.HyperParam),
		Inputs:     fmt.Sprintf(DCPrompt, prompt),
	}

	url := m.endpoint + "/generate"
	resp, err := httputil.HTTPPostBody(
		url,
		data,
		httputil.WithRetryCount(0),
		httputil.WithTimeout(300*time.Second),
		httputil.WithHeader("Content-Type", "application/json"))

	if err != nil {
		//fmt.Println(resp, err)
		return "", err
	}
	if resp["error_msg"] != nil {
		logger.Debug("request %s error: %v", m.modelConf.Model, resp["error_msg"])
		// 返回错误信息(超长时也会报错，需要将超长的错误信息返回给用户)
		return "", errors.New(resp["error_msg"].(string))
	}
	result := resp["generated_text"].(string)
	return result, err
}

func (m *DCService) IsAccess() error {
	host := m.endpoint
	if strings.HasPrefix(m.endpoint, "http://") {
		host = strings.TrimPrefix(m.endpoint, "http://")
	}

	if strings.HasPrefix(m.endpoint, "https://") {
		host = strings.TrimPrefix(m.endpoint, "https://")
	}

	ip, port, _ := net.SplitHostPort(host)
	if httputil.IsIPReachable(ip, port, 100*time.Millisecond) {
		return nil
	}
	return fmt.Errorf("[%s] endpoint %s is not reachable", m.modelConf.Model, host)
}
