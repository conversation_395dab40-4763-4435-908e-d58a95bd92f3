package llm

import (
	"icode.baidu.com/baidu/cov/iUT/logger"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
)

type UTRequestBody struct {
	LLMType        string      `json:"LLMType"`
	Prompt         string      `json:"Prompt"`
	LLMHyperParams *HyperParam `json:"LLMHyperParams"`
	UserName       string      `json:"userName"`
	License        string      `json:"license"`
	RequestSource  string      `json:"requestSource"`
}

// UTInferService 百度厂内支持的单测推理服务
type UTInferService struct {
	serviceURL string
	modelParam *ModelConf
}

func NewUTService(param *ModelConf) *UTInferService {
	return &UTInferService{
		modelParam: param,
		serviceURL: param.EndPoint,
	}
}

func (s *UTInferService) Inference(prompt string) (string, error) {
	body := UTRequestBody{
		LLMType:        s.modelParam.Model,
		Prompt:         prompt,
		LLMHyperParams: s.modelParam.HyperParam,
	}

	extraParams := s.modelParam.ExtraPrams

	if extraParams != nil {
		body.UserName = extraParams.UserName
		body.License = extraParams.License
		body.RequestSource = extraParams.RequestSource
	}

	resp, err := httputil.HTTPPostBody(
		s.serviceURL,
		body,
		httputil.WithRetryCount(0),
		httputil.WithTimeout(300*time.Second),
		httputil.WithHeader("Content-Type", "application/json"))

	if err != nil {
		//fmt.Println(resp, err)
		logger.Error("inference error: %v", err)
		return "", err
	}
	result := resp["data"].(string)
	return result, err

}

func (s *UTInferService) IsAccess() error {
	return nil
}
