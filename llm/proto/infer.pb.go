// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.27.3
// source: infer.proto

package ctrl

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FetchStrategy int32

const (
	// 根据 request 数量拉取请求
	FetchStrategy_ByRequest FetchStrategy = 0 // 默认值
	// 根据 token 数量拉取请求
	FetchStrategy_ByToken FetchStrategy = 1
)

// Enum value maps for FetchStrategy.
var (
	FetchStrategy_name = map[int32]string{
		0: "ByRequest",
		1: "ByToken",
	}
	FetchStrategy_value = map[string]int32{
		"ByRequest": 0,
		"ByToken":   1,
	}
)

func (x FetchStrategy) Enum() *FetchStrategy {
	p := new(FetchStrategy)
	*p = x
	return p
}

func (x FetchStrategy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchStrategy) Descriptor() protoreflect.EnumDescriptor {
	return file_infer_proto_enumTypes[0].Descriptor()
}

func (FetchStrategy) Type() protoreflect.EnumType {
	return &file_infer_proto_enumTypes[0]
}

func (x FetchStrategy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchStrategy.Descriptor instead.
func (FetchStrategy) EnumDescriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{0}
}

type ContentType int32

const (
	ContentType_WENXIN ContentType = 0
	ContentType_TRITON ContentType = 1
)

// Enum value maps for ContentType.
var (
	ContentType_name = map[int32]string{
		0: "WENXIN",
		1: "TRITON",
	}
	ContentType_value = map[string]int32{
		"WENXIN": 0,
		"TRITON": 1,
	}
)

func (x ContentType) Enum() *ContentType {
	p := new(ContentType)
	*p = x
	return p
}

func (x ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_infer_proto_enumTypes[1].Descriptor()
}

func (ContentType) Type() protoreflect.EnumType {
	return &file_infer_proto_enumTypes[1]
}

func (x ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContentType.Descriptor instead.
func (ContentType) EnumDescriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{1}
}

// 拉取请求时，需要给出模型参数
type ModelFetchRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模型全局唯一id
	ModelId []string `protobuf:"bytes,1,rep,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	// 一次返回的最大请求数
	MaxRequestNum int32          `protobuf:"varint,2,opt,name=max_request_num,json=maxRequestNum,proto3" json:"max_request_num,omitempty"`
	Strategy      FetchStrategy  `protobuf:"varint,3,opt,name=strategy,proto3,enum=language_inference.FetchStrategy" json:"strategy,omitempty"`
	ByTokenParams *ByTokenParams `protobuf:"bytes,4,opt,name=by_token_params,json=byTokenParams,proto3" json:"by_token_params,omitempty"`
}

func (x *ModelFetchRequestParams) Reset() {
	*x = ModelFetchRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelFetchRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelFetchRequestParams) ProtoMessage() {}

func (x *ModelFetchRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelFetchRequestParams.ProtoReflect.Descriptor instead.
func (*ModelFetchRequestParams) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{0}
}

func (x *ModelFetchRequestParams) GetModelId() []string {
	if x != nil {
		return x.ModelId
	}
	return nil
}

func (x *ModelFetchRequestParams) GetMaxRequestNum() int32 {
	if x != nil {
		return x.MaxRequestNum
	}
	return 0
}

func (x *ModelFetchRequestParams) GetStrategy() FetchStrategy {
	if x != nil {
		return x.Strategy
	}
	return FetchStrategy_ByRequest
}

func (x *ModelFetchRequestParams) GetByTokenParams() *ByTokenParams {
	if x != nil {
		return x.ByTokenParams
	}
	return nil
}

type ByTokenParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 可用的 block 数量
	BlockNum int32 `protobuf:"varint,1,opt,name=block_num,json=blockNum,proto3" json:"block_num,omitempty"`
	// 每个 block 能支持的 token 数量
	BlockSize int32 `protobuf:"varint,2,opt,name=block_size,json=blockSize,proto3" json:"block_size,omitempty"`
	// 每个 query 需要给输出预留的 token 数量
	DecTokenNum int32 `protobuf:"varint,3,opt,name=dec_token_num,json=decTokenNum,proto3" json:"dec_token_num,omitempty"`
}

func (x *ByTokenParams) Reset() {
	*x = ByTokenParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ByTokenParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ByTokenParams) ProtoMessage() {}

func (x *ByTokenParams) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ByTokenParams.ProtoReflect.Descriptor instead.
func (*ByTokenParams) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{1}
}

func (x *ByTokenParams) GetBlockNum() int32 {
	if x != nil {
		return x.BlockNum
	}
	return 0
}

func (x *ByTokenParams) GetBlockSize() int32 {
	if x != nil {
		return x.BlockSize
	}
	return 0
}

func (x *ByTokenParams) GetDecTokenNum() int32 {
	if x != nil {
		return x.DecTokenNum
	}
	return 0
}

type ModelFetchRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 获取到的请求数组
	Requests []*ModelInferRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
}

func (x *ModelFetchRequestResult) Reset() {
	*x = ModelFetchRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelFetchRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelFetchRequestResult) ProtoMessage() {}

func (x *ModelFetchRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelFetchRequestResult.ProtoReflect.Descriptor instead.
func (*ModelFetchRequestResult) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{2}
}

func (x *ModelFetchRequestResult) GetRequests() []*ModelInferRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

// 无需关心SendResponse的返回值
type ModelSendResponseResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModelSendResponseResult) Reset() {
	*x = ModelSendResponseResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSendResponseResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSendResponseResult) ProtoMessage() {}

func (x *ModelSendResponseResult) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSendResponseResult.ProtoReflect.Descriptor instead.
func (*ModelSendResponseResult) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{3}
}

type ModelInferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模型唯一id，
	ModelId string `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	// 请求唯一id，切勿包含 #
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// 可用于跟踪同一请求，多次推理的应答，可选
	TraceId string `protobuf:"bytes,3,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// 语言模型输入，各模型不同，
	Input string `protobuf:"bytes,4,opt,name=input,proto3" json:"input,omitempty"`
	// 租户信息，可选
	TenantId string `protobuf:"bytes,5,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// 输入类型，取值 triton、wenxin，默认值是 wenxin
	InputType ContentType `protobuf:"varint,6,opt,name=input_type,json=inputType,proto3,enum=language_inference.ContentType" json:"input_type,omitempty"`
}

func (x *ModelInferRequest) Reset() {
	*x = ModelInferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferRequest) ProtoMessage() {}

func (x *ModelInferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferRequest.ProtoReflect.Descriptor instead.
func (*ModelInferRequest) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{4}
}

func (x *ModelInferRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelInferRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ModelInferRequest) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *ModelInferRequest) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *ModelInferRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *ModelInferRequest) GetInputType() ContentType {
	if x != nil {
		return x.InputType
	}
	return ContentType_WENXIN
}

type ModelInferResponseList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseList []*ModelInferResponse `protobuf:"bytes,1,rep,name=response_list,json=responseList,proto3" json:"response_list,omitempty"`
}

func (x *ModelInferResponseList) Reset() {
	*x = ModelInferResponseList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferResponseList) ProtoMessage() {}

func (x *ModelInferResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferResponseList.ProtoReflect.Descriptor instead.
func (*ModelInferResponseList) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{5}
}

func (x *ModelInferResponseList) GetResponseList() []*ModelInferResponse {
	if x != nil {
		return x.ResponseList
	}
	return nil
}

type ModelInferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求唯一id，切勿包含 #
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// 返回的句子id，表示第几句，用于去重和排序
	SentenceId int32 `protobuf:"varint,2,opt,name=sentence_id,json=sentenceId,proto3" json:"sentence_id,omitempty"`
	// 语言模型输出
	Output string `protobuf:"bytes,3,opt,name=output,proto3" json:"output,omitempty"`
	// 模型唯一id
	ModelId string `protobuf:"bytes,4,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	// 可用于跟踪同一请求，多次推理的应答
	TraceId string `protobuf:"bytes,5,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// 租户信息，可选
	TenantId string `protobuf:"bytes,6,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// 输出类型，取值 triton、wenxin，默认值是 wenxin
	OutputType ContentType `protobuf:"varint,7,opt,name=output_type,json=outputType,proto3,enum=language_inference.ContentType" json:"output_type,omitempty"`
}

func (x *ModelInferResponse) Reset() {
	*x = ModelInferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_infer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelInferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInferResponse) ProtoMessage() {}

func (x *ModelInferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_infer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInferResponse.ProtoReflect.Descriptor instead.
func (*ModelInferResponse) Descriptor() ([]byte, []int) {
	return file_infer_proto_rawDescGZIP(), []int{6}
}

func (x *ModelInferResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ModelInferResponse) GetSentenceId() int32 {
	if x != nil {
		return x.SentenceId
	}
	return 0
}

func (x *ModelInferResponse) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

func (x *ModelInferResponse) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelInferResponse) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *ModelInferResponse) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *ModelInferResponse) GetOutputType() ContentType {
	if x != nil {
		return x.OutputType
	}
	return ContentType_WENXIN
}

var File_infer_proto protoreflect.FileDescriptor

var file_infer_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x22, 0xe6, 0x01, 0x0a, 0x17, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x75, 0x6d,
	0x12, 0x3d, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x49, 0x0a, 0x0f, 0x62, 0x79, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x42, 0x79,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0d, 0x62, 0x79, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x6f, 0x0a, 0x0d, 0x42, 0x79,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x64, 0x65, 0x63, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x64, 0x65, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e, 0x75, 0x6d, 0x22, 0x5c, 0x0a, 0x17, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x41, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0xdb, 0x01, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x65, 0x0a, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x81, 0x02, 0x0a, 0x12, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x2b, 0x0a,
	0x0d, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x0d,
	0x0a, 0x09, 0x42, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x42, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x10, 0x01, 0x2a, 0x25, 0x0a, 0x0b, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x4e,
	0x58, 0x49, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x52, 0x49, 0x54, 0x4f, 0x4e, 0x10,
	0x01, 0x32, 0xd2, 0x03, 0x0a, 0x14, 0x47, 0x52, 0x50, 0x43, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x65, 0x0a, 0x10, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x12, 0x25,
	0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30,
	0x01, 0x12, 0x6f, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x6c, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a,
	0x2b, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x28, 0x01,
	0x12, 0x74, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x28, 0x01, 0x42, 0x13, 0x5a, 0x11, 0x2e, 0x2e, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x74, 0x72, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_infer_proto_rawDescOnce sync.Once
	file_infer_proto_rawDescData = file_infer_proto_rawDesc
)

func file_infer_proto_rawDescGZIP() []byte {
	file_infer_proto_rawDescOnce.Do(func() {
		file_infer_proto_rawDescData = protoimpl.X.CompressGZIP(file_infer_proto_rawDescData)
	})
	return file_infer_proto_rawDescData
}

var file_infer_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_infer_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_infer_proto_goTypes = []interface{}{
	(FetchStrategy)(0),              // 0: language_inference.FetchStrategy
	(ContentType)(0),                // 1: language_inference.ContentType
	(*ModelFetchRequestParams)(nil), // 2: language_inference.ModelFetchRequestParams
	(*ByTokenParams)(nil),           // 3: language_inference.ByTokenParams
	(*ModelFetchRequestResult)(nil), // 4: language_inference.ModelFetchRequestResult
	(*ModelSendResponseResult)(nil), // 5: language_inference.ModelSendResponseResult
	(*ModelInferRequest)(nil),       // 6: language_inference.ModelInferRequest
	(*ModelInferResponseList)(nil),  // 7: language_inference.ModelInferResponseList
	(*ModelInferResponse)(nil),      // 8: language_inference.ModelInferResponse
}
var file_infer_proto_depIdxs = []int32{
	0,  // 0: language_inference.ModelFetchRequestParams.strategy:type_name -> language_inference.FetchStrategy
	3,  // 1: language_inference.ModelFetchRequestParams.by_token_params:type_name -> language_inference.ByTokenParams
	6,  // 2: language_inference.ModelFetchRequestResult.requests:type_name -> language_inference.ModelInferRequest
	1,  // 3: language_inference.ModelInferRequest.input_type:type_name -> language_inference.ContentType
	8,  // 4: language_inference.ModelInferResponseList.response_list:type_name -> language_inference.ModelInferResponse
	1,  // 5: language_inference.ModelInferResponse.output_type:type_name -> language_inference.ContentType
	6,  // 6: language_inference.GRPCInferenceService.ModelStreamInfer:input_type -> language_inference.ModelInferRequest
	2,  // 7: language_inference.GRPCInferenceService.ModelFetchRequest:input_type -> language_inference.ModelFetchRequestParams
	8,  // 8: language_inference.GRPCInferenceService.ModelSendResponse:input_type -> language_inference.ModelInferResponse
	7,  // 9: language_inference.GRPCInferenceService.ModelSendResponseList:input_type -> language_inference.ModelInferResponseList
	8,  // 10: language_inference.GRPCInferenceService.ModelStreamInfer:output_type -> language_inference.ModelInferResponse
	4,  // 11: language_inference.GRPCInferenceService.ModelFetchRequest:output_type -> language_inference.ModelFetchRequestResult
	5,  // 12: language_inference.GRPCInferenceService.ModelSendResponse:output_type -> language_inference.ModelSendResponseResult
	5,  // 13: language_inference.GRPCInferenceService.ModelSendResponseList:output_type -> language_inference.ModelSendResponseResult
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_infer_proto_init() }
func file_infer_proto_init() {
	if File_infer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_infer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelFetchRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_infer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ByTokenParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_infer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelFetchRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_infer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSendResponseResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_infer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_infer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferResponseList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_infer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelInferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_infer_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_infer_proto_goTypes,
		DependencyIndexes: file_infer_proto_depIdxs,
		EnumInfos:         file_infer_proto_enumTypes,
		MessageInfos:      file_infer_proto_msgTypes,
	}.Build()
	File_infer_proto = out.File
	file_infer_proto_rawDesc = nil
	file_infer_proto_goTypes = nil
	file_infer_proto_depIdxs = nil
}
