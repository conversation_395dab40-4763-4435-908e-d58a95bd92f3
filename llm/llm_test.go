package llm

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"io"
	"os"
	"testing"
)

func TestInference(t *testing.T) {
	logrus.SetOutput(io.Discard)
	logrus.SetLevel(logrus.ErrorLevel)
	type args struct {
		prompt    string
		modelName string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		//{"test_ec2",
		//	args{"今天北京天气",
		//		EC2},
		//	false,
		//},
		//{"test_dc",
		//	args{"今天天气",
		//		DC},
		//	false,
		//},
		{"test_eb",
			args{"今天北京天气",
				EB35},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var config *ModelConf
			if tt.args.modelName == EC2 || tt.args.modelName == DC || tt.args.modelName == DC2 {
				config = ModelConfForInnerUtInfer(tt.args.modelName)
			} else if tt.args.modelName == EB35 {
				config = ModelConfForQianfan(EB35)
			}
			got, err := Inference(tt.args.prompt, config)
			fmt.Println(got)
			if (err != nil) != tt.wantErr {
				t.Errorf("Inference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCheckModelConfigValid(t *testing.T) {
	type args struct {
		modelConf *ModelConf
	}
	tests := []struct {
		name    string
		args    args
		want    error
		wantErr bool
	}{
		{
			name: "model not support",
			args: args{
				modelConf: &ModelConf{
					Model: "abc",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "model not in qianfan list",
			args: args{
				modelConf: &ModelConf{
					Model:    "abc",
					EndPoint: "abc",
					IsInner:  false,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "model in qianfan list",
			args: args{
				modelConf: &ModelConf{
					Model:   "ERNIE-Speed-8K",
					IsInner: false,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "model in qianfan list",
			args: args{
				modelConf: &ModelConf{
					Model:    EC2Sft,
					IsInner:  false,
					EndPoint: "33.22.4.5:80",
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CheckModelConfigAccess(tt.args.modelConf)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckModelConfigAccess() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test1(t *testing.T) {
	logrus.SetOutput(io.Discard)
	//original := os.Stderr
	var logger = &logrus.Logger{
		Out:       os.Stderr,
		Formatter: new(logrus.TextFormatter),
		Hooks:     make(logrus.LevelHooks),
		Level:     logrus.InfoLevel,
	}
	//logger.SetOutput(io.Discard)
	os.Stderr = io.Discard.(*os.File)
	//os.Stderr = os.NewFile(0, os.DevNull)
	logger.Info("hello")
	logger.Info("sssssssssssss")
}
