package llm

import (
	"fmt"
	"github.com/baidubce/bce-qianfan-sdk/go/qianfan"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
	"os"
	"time"
)

const (
	DC         = "java_llm"
	DC2        = "llm"
	EB4        = "ERNIE-4.0-8K"
	EB35       = "ERNIE-Bot"
	EB35Inner  = "eb35"
	EC2        = "ec2"
	ERNIESpeed = "ERNIE-Speed-8K"
	DS25       = "ds25"
	EC3        = "ec3"
	DSV3       = "ds_v3"
)

// 走单测服务请求的模型
var innerModel = []string{DC, DC2, EC2, EC3, EB35Inner, DS25, DSV3}

const (
	DefaultGenModel    = DC
	DefaultRegenModel  = DS25
	DefaultRepairModel = DS25

	DefaultModel = DS25
)

func GetDefaultGenModel() string {
	if os.Getenv("DEFAULT_GEN_MODEL") != "" {
		return os.Getenv("DEFAULT_GEN_MODEL")
	}
	return DefaultGenModel
}

func GetDefaultRegenModel() string {
	if os.Getenv("DEFAULT_REGEN_MODEL") != "" {
		return os.Getenv("DEFAULT_REGEN_MODEL")
	}
	return DefaultRegenModel
}

func GetDefaultRepairModel() string {
	if os.Getenv("DEFAULT_REPAIR_MODEL") != "" {
		return os.Getenv("DEFAULT_REPAIR_MODEL")
	}
	return DefaultRepairModel
}

func GetDefaultModel() string {
	return DefaultModel
}

// for poc 使用
const (
	DeepCode = "deepcode"
	EC2Sft   = "ernie-code2-sft"
)

const (
	EBTokenLimit = 10000
	ECTokenLimit = 10000
	DCTokenLimit = 14000
)

type BaseLLMService interface {
	// Inference 推理
	Inference(prompt string) (string, error)
	IsAccess() error
}

func getLLMService(modelConf *ModelConf) BaseLLMService {
	if modelConf == nil {
		// default model
		return NewUTService(ModelConfForInnerUtInfer(DefaultModel))
	}

	if !modelConf.IsInner {
		if qianfan.ChatModelEndpoint[modelConf.Model] != "" {
			// modelname在千帆列表中
			return NewQFService(modelConf)
		} else if modelConf.EndPoint != "" {
			if modelConf.Model == EC2Sft {
				return NewEC2InferCenter(modelConf)
			} else if modelConf.Model == DeepCode {
				return NewDCService(modelConf)
			} else {
				panic(fmt.Sprintf("model [%s] not support", modelConf.Model))
			}
		} else {
			panic(fmt.Sprintf("model [%s] not support", modelConf.Model))
		}
	} else {
		if utils.InSlice(innerModel, modelConf.Model) {
			//modelConf = ModelConfForInnerUtInfer(modelConf.Model)
			return NewUTService(modelConf)
		}
		//modelConf = ModelConfForQianfan(modelConf.Model)
		return NewQFService(modelConf)
	}
}

// Inference 函数根据给定的prompt和模型名称，使用指定的模型进行推理并返回结果。
//
// prompt: 字符串类型，表示用于推理的输入文本。
// modelName: 字符串类型，表示用于推理的模型名称。
//
// 返回值：
// - string：表示推理的结果，为字符串类型。
// - error：如果推理过程中发生错误，则返回非零的错误码；否则返回nil。
func Inference(prompt string, modelConf *ModelConf) (string, error) {
	var llmService = getLLMService(modelConf)
	//logger.Debug("inference with model %s, prompt: %s", modelConf.Model, prompt)
	result, err := llmService.Inference(prompt)
	if err != nil {
		time.Sleep(1 * time.Second)
		return Inference(prompt, modelConf) //retry one time
	}

	return result, err
}

// CheckModelConfigAccess 校验模型配置是否有效
//
// 参数：
// modelConf *ModelConf 指向模型配置的指针
//
// 返回值：
// error 返回校验结果，若配置无效则返回错误信息，否则返回nil
func CheckModelConfigAccess(modelConf *ModelConf) error {
	service := getLLMService(modelConf)
	if service == nil {
		return fmt.Errorf("model [%s] not support", modelConf.Model)
	}
	return service.IsAccess()
}
