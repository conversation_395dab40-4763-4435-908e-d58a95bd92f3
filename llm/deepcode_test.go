package llm

import (
	"reflect"
	"testing"
)

func TestDCService_Inference(t *testing.T) {
	type fields struct {
		url       string
		modelConf *ModelConf
	}
	type args struct {
		content string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "test1",
			fields: fields{
				url: "http://10.14.63.24:8998",
				modelConf: &ModelConf{HyperParam: &HyperParam{
					TopP: 0.95, Temperature: 0.7, PenaltyScore: 1.0, TopK: 10,
				},
					Model: "llm"},
			},
			args: args{
				content: "hello",
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &DCService{
				endpoint:  tt.fields.url,
				modelConf: tt.fields.modelConf,
			}
			got, err := m.Inference(tt.args.content)
			if (err != nil) != tt.wantErr {
				t.Errorf("Inference() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("Inference() got = %v, want %v", got, tt.want)
			}
		})
	}
}
