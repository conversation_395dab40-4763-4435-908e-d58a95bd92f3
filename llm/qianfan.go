package llm

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"icode.baidu.com/baidu/cov/iUT/logger"

	"github.com/baidubce/bce-qianfan-sdk/go/qianfan"
)

// QFService 支持qianfan提供的所有模型服务
// 支持的模型列表：https://cloud.baidu.com/doc/WENXINWORKSHOP/s/Zlt2agedu
type QFService struct {
	qfClient    *qianfan.ChatCompletion
	modelParam  *ModelConf
	historyChat []qianfan.ChatCompletionMessage
}

func NewQFService(modelParam *ModelConf) *QFService {
	qianfan.SetLogLevel(logrus.ErrorLevel) // 调用qianfan组件时，设置日志级别为error
	qianfan.GetConfig().AK = modelParam.AK
	qianfan.GetConfig().SK = modelParam.SK
	if modelParam.RetryCount == 0 {
		modelParam.RetryCount = 1
	}
	qfClient := qianfan.NewChatCompletion(
		qianfan.WithModel(modelParam.Model),
		qianfan.WithEndpoint(modelParam.EndPoint),
		qianfan.WithLLMRetryCount(modelParam.RetryCount),
	)

	return &QFService{
		modelParam: modelParam,
		qfClient:   qfClient,
	}
}

func (qs *QFService) Inference(prompt string) (string, error) {
	messages := []qianfan.ChatCompletionMessage{
		qianfan.ChatCompletionUserMessage(prompt),
	}
	logger.Debug(fmt.Sprintf("Post: %v", messages))
	ctx := context.Background()
	resp, err := qs.qfClient.Do(
		ctx,
		&qianfan.ChatCompletionRequest{
			MaxOutputTokens: 2048,
			Messages:        messages,
		},
	)
	if err != nil {
		return "", err
	}

	result := resp.Result
	return result, nil
}

// InferenceWithMultiChat 通过多次对话进行推理
//
// 参数:
//
//	prompt string: 用户输入的提示文本
//
// 返回值:
//
//	string: 推理结果
//	error: 如果发生错误，则返回错误信息；否则返回nil
func (qs *QFService) InferenceWithMultiChat(prompt string) (string, error) {
	query := qianfan.ChatCompletionUserMessage(prompt)
	// 将提示文本封装为对话消息，并将其添加到历史对话记录中
	qs.historyChat = append(qs.historyChat, query)
	ctx := context.Background()
	resp, err := qs.qfClient.Do(
		ctx,
		&qianfan.ChatCompletionRequest{
			MaxOutputTokens: 2048,
			Messages:        qs.historyChat,
		},
	)
	if err != nil {
		return "", err
	}

	result := resp.Result
	// 将推理结果封装为对话消息，并将其添加到历史对话记录中
	qs.historyChat = append(qs.historyChat, qianfan.ChatCompletionAssistantMessage(result))
	return result, nil
}

func (qs *QFService) IsAccess() error {
	if qianfan.ChatModelEndpoint[qs.modelParam.Model] == "" {
		return fmt.Errorf("[qianfan] model %s not found", qs.modelParam.Model)
	}

	if qs.modelParam.AK == "" || qs.modelParam.SK == "" {
		return fmt.Errorf("[qianfan] AK or SK is empty")
	}

	return nil
}
