package llm

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"net"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	pb "icode.baidu.com/baidu/cov/iUT/llm/proto"
	"icode.baidu.com/baidu/cov/iUT/logger"
)

type Input4EC2 struct {
	Text string `json:"text"`
	//SeqLen         int     `json:"seq_len"` // 最大生成长度，默认1024
	ReqId          string  `json:"req_id"`
	ModelId        string  `json:"model_id"`
	MinDecLen      int     `json:"min_dec_len"`
	Temperature    float32 `json:"temperature"`     // 默认0.7
	TopP           float32 `json:"top_p"`           // 默认0.7
	PenaltyScore   float32 `json:"penalty_score"`   // 默认0，不惩罚
	FrequencyScore float32 `json:"frequency_score"` // 默认0
}

func (i *Input4EC2) ToString() string {
	b, _ := json.Marshal(i)
	return string(b)
}

type Output4EC2 struct {
	ReqId  string `json:"req_id"`
	Result string `json:"result"`
	ErrNo  int    `json:"err_no"`
	ErrMsg string `json:"err_msg"`
	IsEnd  int    `json:"is_end"`
	SentId int    `json:"sent_id"`
}

func generateReqId(content string) string {
	final := []byte(fmt.Sprintf("%d-%s", time.Now().Unix(), content))
	return fmt.Sprintf("%x", md5.Sum(final))
}

func newInferRequest(prompt string, modelID string, hyperParams *HyperParam) *pb.ModelInferRequest {
	requestId := generateReqId(prompt)

	// 请求模型的默认参数
	input := &Input4EC2{
		Text:         prompt,
		ReqId:        requestId,
		ModelId:      modelID,
		MinDecLen:    1,
		Temperature:  hyperParams.Temperature,
		TopP:         hyperParams.TopP,
		PenaltyScore: hyperParams.PenaltyScore,
	}

	return &pb.ModelInferRequest{
		ModelId:   input.ModelId,
		RequestId: input.ReqId,
		Input:     input.ToString(),
		TenantId:  "",
	}
}

type EC2InferCenter struct {
	Endpoint    string
	ctx         context.Context
	ModelId     string
	HyperParams *HyperParam
}

func NewEC2InferCenter(modelConf *ModelConf) *EC2InferCenter {
	ctx := context.Background()
	hp := modelConf.HyperParam
	if hp == nil {
		hp = defaultHyperParam()
	}
	return &EC2InferCenter{
		ctx:         ctx,
		Endpoint:    modelConf.EndPoint,
		ModelId:     modelConf.Model,
		HyperParams: hp,
	}
}

func (c *EC2InferCenter) GetConn() (*grpc.ClientConn, error) {
	conn, err := grpc.Dial(c.Endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			// ping 超时时长
			Timeout: 60 * time.Second,
			// 如果没有活动流， 则每隔多久发一次Ping
			Time: 50 * time.Second,
		}))

	if err != nil {
		logger.Error("dial conn err: %v", err)
		return nil, err
	}
	return conn, nil
}

func (c *EC2InferCenter) Inference(prompt string) (string, error) {
	request := newInferRequest(prompt, c.ModelId, c.HyperParams)
	conn, err := c.GetConn()
	if err != nil {
		return "", err
	}
	defer conn.Close()
	infer, err := pb.NewGRPCInferenceServiceClient(conn).ModelStreamInfer(c.ctx, request)

	if err != nil {
		return "", err
	}

	response, err := infer.Recv()
	if err != nil {
		return "", err
	}

	if response != nil && response.Output != "" {
		var output = new(Output4EC2)
		err = json.Unmarshal([]byte(response.Output), output)
		if err != nil {
			return "", err
		}
		return output.Result, nil
	}

	return "", nil
}

func (c *EC2InferCenter) IsAccess() error {
	ip, port, _ := net.SplitHostPort(c.Endpoint)
	if httputil.IsIPReachable(ip, port, 100*time.Millisecond) {
		return nil
	}
	return fmt.Errorf("[%s] endpoint %s is not reachable", c.ModelId, c.Endpoint)
}
