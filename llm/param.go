package llm

import (
	"icode.baidu.com/baidu/cov/iUT/config"
)

// ModelConf 模型参数
type ModelConf struct {
	Model      string
	EndPoint   string
	AK         string
	SK         string
	RetryCount int  `toml:"RetryCount,omitempty"`
	IsInner    bool `toml:"IsInner,omitempty"` // 是否为内部调用
	HyperParam *HyperParam
	ExtraPrams *ExtraParams `toml:"ExtraPrams,-"` // 额外参数，如用户等，用于记录信息

	RetryTimes int
}

type ExtraParams struct {
	UserName      string // 用户名
	License       string // 用户协议
	RequestSource string // 请求来源
}

// HyperParam 调用 LLM 时的参数, 目前参数字段参考文心千帆 API 定义
// 通用模型超参
type HyperParam struct {
	// Temperature 随机性, 较高的数值会使输出更加随机, 而较低的数值会使其更加集中和确定
	Temperature float32 `json:"temperature,omitempty"`

	// TopP 影响输出文本的多样性，取值越大，生成文本的多样性越强
	TopP float32 `json:"top_p,omitempty"`

	TopK float32 `json:"top_k,omitempty"`

	// PenaltyScore 通过对已生成的token增加惩罚，减少重复生成的现象
	PenaltyScore float32 `json:"penalty_score,omitempty"`

	System string `json:"system,omitempty"`

	// UserID 表示最终用户的唯一标识符，可以监视和检测滥用行为，防止接口恶意调用
	UserID string `json:"user_id,omitempty"`

	// Stop 生成停止标识，当模型生成结果以 stop 中某个元素结尾时，停止文本生成。
	// 说明：
	// （1）每个元素长度不超过 20 字符
	// （2）最多4个元素
	Stop []string `json:"stop,omitempty"`
}

func defaultHyperParam() *HyperParam {
	return &HyperParam{
		Temperature:  0.7,
		TopP:         1.0,
		PenaltyScore: 1,
	}
}

func userHyperParam() *HyperParam {
	// 从用户配置文件中读取超参设置
	return nil
}

func ModelConfForInnerUtInfer(modelName string) *ModelConf {
	return &ModelConf{
		EndPoint:   getLLMGenerateURL(),
		RetryCount: 3,
		Model:      modelName,
		IsInner:    true,

		RetryTimes: 2,
	}
}

func getLLMGenerateURL() string {
	return config.GetHost() + config.GenerateAPIPath
}

func ModelConfForQianfan(modelName string) *ModelConf {
	return &ModelConf{
		RetryCount: 3,
		Model:      modelName,
		AK:         "RaUEYSphnAtKqmmEsqIH9LP1",
		SK:         "IhyNhmnw8svSzasx2DrwLO6BQRherJpg",
		IsInner:    true,
	}
}
