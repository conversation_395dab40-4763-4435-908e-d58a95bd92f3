package notify

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/tools/coverage"
	"icode.baidu.com/baidu/cov/iUT/utagent/base"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

type NotifierForIDE struct {
	Data            *models.JsonRpcData
	close           chan bool
	lastUpdateAt    int64
	stateUpdate     bool
	currentProgress int64
}

// NewNotifierForIDE ide通知消息组件
func NewNotifierForIDE() *NotifierForIDE {
	data := models.NewJsonRpcData()
	data.Params.Status = constant.RUNNING
	data.Params.Process = &models.TaskProcess{
		TaskStart: "",
	}
	notifier := &NotifierForIDE{
		Data:         data,
		lastUpdateAt: time.Now().UnixMilli(),
		close:        make(chan bool),
	}
	notifier.start()
	return notifier
}

// Start
//
//	心跳信息，周期性（30s），探活数据发送
func (notify *NotifierForIDE) start() {
	notify.outputStdout()
	go func() {
		for {
			select {
			case <-notify.close:
				return
			default:
				now := time.Now().UnixMilli()
				if now-notify.lastUpdateAt > 1000*5 {
					notify.outputStdout()
				}
				time.Sleep(time.Second * 1)
			}
		}
	}()
}

func (notify *NotifierForIDE) Close() {
	notify.close <- true
}

func (notify *NotifierForIDE) outputStdout() {
	now := time.Now().UnixMilli()
	if now-notify.lastUpdateAt < 500 {
		// 防止输出太快，导致IDE engine无法正确读取message
		// 当距离上次更新小于500ms时，强制sleep一下
		time.Sleep(time.Millisecond * 500)
	}
	notify.Data.Print()
	notify.lastUpdateAt = time.Now().UnixMilli()
}

// delayOutputStdout 延迟输出标准输出流
//
// 该函数用于延迟输出标准输出流，使IDE在适当的时候一次性获取全部输出信息。
// 调用该函数后，NotifierForIDE的stateUpdate字段将被设置为true，表示状态已更新。
func (notify *NotifierForIDE) delayOutputStdout() {
	notify.stateUpdate = true
}

func (notify *NotifierForIDE) GetProcessData() *models.TaskProcess {
	return notify.Data.Params.Process
}

func (notify *NotifierForIDE) UpdateEnvCheckStatus(status bool, errorMsg string) {
	processData := notify.Data.Params.Process
	if !status {
		processData.EnvCheck.EnvState = constant.UNUSABLE
		processData.EnvCheck.ErrorMsg = &models.EnvErrorMsg{
			Msg:           errorMsg,
			RepairSuggest: "",
		}
		notify.Data.Params.Status = constant.FAILED
		notify.Data.Params.Message = errorMsg
	} else {
		processData.EnvCheck.EnvState = constant.USABLE
		processData.FrameworkCheck = &models.FrameworkCheck{
			State: constant.RUNNING,
		}
	}
	notify.outputStdout()
}

func (notify *NotifierForIDE) EnvCheckStart() {
	notify.Data.Params.Process.EnvCheck = new(models.EnvironmentData)
	notify.Data.Params.Process.EnvCheck.EnvState = constant.RUNNING
	notify.outputStdout()
}

func (notify *NotifierForIDE) UpdateTaskExpectDesc(desc string) {
	notify.Data.Params.Process.TaskStart += desc
	notify.outputStdout()
}

func (notify *NotifierForIDE) UpdateSummary(summary *models.TaskSummary, status string, errMsg string) {
	notify.Data.Params.TaskSummary = summary
	notify.Data.Params.Status = status
	notify.Data.Params.Message = errMsg
	notify.Data.Params.Process.LoadingMsg = "" // 结束后清空loadingMsg
	notify.outputStdout()
}

func (notify *NotifierForIDE) UpdateTaskProcessCoverage(memory *base.Memory) {
	if notify.Data.Params.TaskProcessCovDate == nil {
		covered, total := memory.GetProjectCoverage("origin")
		notify.Data.Params.TaskProcessCovDate = &models.TaskProcessData{
			OldCoverage:  fmt.Sprintf("%0.2f%%", helper.Divide(covered, total, 4)*100),
			OldCoverLine: covered,
			OldTotalLine: total,
		}
	}
	covered, total := memory.GetProjectCoverage("current")
	notify.Data.Params.TaskProcessCovDate.CurrentCoverLine = covered
	notify.Data.Params.TaskProcessCovDate.CurrentTotalLine = total
	notify.Data.Params.TaskProcessCovDate.CurrentCoverage = fmt.Sprintf("%0.2f%%",
		helper.Divide(covered, total, 4)*100)

	if notify.Data.Params.TaskProcessCovDate.CurrentTotalLine > notify.Data.Params.TaskProcessCovDate.OldTotalLine {
		// 如果当前行数比原来的行数多，则需要重新计算原来的覆盖率
		// 以防覆盖率总值减少的情况
		notify.Data.Params.TaskProcessCovDate.OldCoverage = fmt.Sprintf("%0.2f%%",
			helper.Divide(notify.Data.Params.TaskProcessCovDate.OldCoverLine,
				notify.Data.Params.TaskProcessCovDate.CurrentTotalLine, 4)*100)
	}

	cur, _ := strconv.ParseFloat(strings.TrimSuffix(notify.Data.Params.TaskProcessCovDate.CurrentCoverage, "%"), 64)
	old, _ := strconv.ParseFloat(strings.TrimSuffix(notify.Data.Params.TaskProcessCovDate.OldCoverage, "%"), 64)

	if delta := cur - old; delta > 0 {
		notify.Data.Params.TaskProcessCovDate.IncreaseRate = fmt.Sprintf("%0.2f%%", delta)
	} else {
		notify.Data.Params.TaskProcessCovDate.IncreaseRate = constant.PercentZero
	}
	notify.delayOutputStdout()
}

func (notify *NotifierForIDE) NotifyUtResult(underTest models.UnderTestInfo, testFilePath, testFileContent string,
	memory *base.Memory, coverage *coverage.CoverageInfo) {
	result := &models.UtFileResult{
		State:           constant.RUNNING,
		TestFileContent: testFileContent,
		TestFilePath:    testFilePath,
		SrcFilePath:     underTest.FilePath,
	}

	result.Meta.FileCoverage = fmt.Sprintf("%0.2f%%", coverage.GetCoveragePercent())

	memoryGenUtCases := memory.GetGeneratedUnitTest(testFilePath)
	result.Meta.GeneratedTestMethodCount = len(memoryGenUtCases)
	for _, c := range memoryGenUtCases {
		result.Meta.GeneratedTestMethodName = append(result.Meta.GeneratedTestMethodName, c.CaseName)
		if c.Bug != nil {
			result.Bugs = append(result.Bugs, c)
		} else {
			result.Meta.GeneratedRunPassMethodCount++
		}
	}
	result.Meta.ProcessedSrcMethodName = []string{underTest.FuncName}
	result.Meta.ProcessedSrcMethodCount = memory.GetFileProcess(underTest.FilePath).ExecutedFunc

	summary := &models.TaskSummary{}
	for _, p := range memory.GetAllFileProcess() {
		summary.SrcMethodCount += p.TotalFunc
		summary.ProcessedSrcMethodCount += p.ExecutedFunc
		summary.SucceedSrcMethodCount += p.SucceedFunc
		summary.GeneratedTestMethodCount += p.ValidCaseNum
	}

	notify.Data.Params.TaskSummary = summary
	notify.UpdateUtResult(result)
}

func (notify *NotifierForIDE) UpdateUtResult(result *models.UtFileResult) {
	if len(notify.Data.Params.UtFileResult) == 0 {
		notify.Data.Params.UtFileResult = []*models.UtFileResult{result}
	} else {
		originResult := notify.Data.Params.UtFileResult[0]
		if result.TestFilePath == originResult.TestFilePath {
			originResult.TestFileContent = result.TestFileContent
			for _, name := range result.Meta.ProcessedSrcMethodName {
				if !helper.InSlice(originResult.Meta.ProcessedSrcMethodName, name) {
					originResult.Meta.ProcessedSrcMethodName = append(originResult.Meta.ProcessedSrcMethodName, name)
				}
			}
			originResult.Meta.GeneratedTestMethodName = result.Meta.GeneratedTestMethodName
			originResult.Meta.FileCoverage = result.Meta.FileCoverage
			originResult.Meta.ProcessedSrcMethodCount = result.Meta.ProcessedSrcMethodCount
			originResult.Meta.GeneratedTestMethodCount = result.Meta.GeneratedTestMethodCount
			originResult.Meta.GeneratedRunPassMethodCount = result.Meta.GeneratedRunPassMethodCount
			originResult.Bugs = result.Bugs
		} else {
			notify.Data.Params.UtFileResult[0] = result
		}
	}
	// 通知IDE立刻更新
	notify.outputStdout()
}

func (notify *NotifierForIDE) Notify() {
	notify.outputStdout()
}
