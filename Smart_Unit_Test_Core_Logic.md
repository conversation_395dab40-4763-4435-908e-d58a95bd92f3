# 智能化单测生成核心逻辑 (Prompt素材)

## 核心执行流程

### 1. 代码分析与策略选择
```
步骤1：分析目标代码
- 解析方法签名、参数类型、返回值
- 识别方法内的分支结构、循环、异常处理
- 提取依赖的类、接口、外部调用
- 分析方法的复杂度和业务逻辑

步骤2：评估当前覆盖状态
- 检查是否已有测试用例
- 分析现有测试的覆盖情况
- 识别未覆盖的代码路径和分支
- 确定需要补充的测试场景

步骤3：选择生成策略
- 无测试覆盖 → 首次全面生成
- 部分覆盖 → 针对性补充生成  
- 覆盖不足 → 边界条件和异常场景生成
```

### 2. 智能测试生成
```
步骤1：构建测试上下文
- 收集目标方法的完整实现代码
- 获取相关依赖类的接口定义
- 分析方法的输入输出关系
- 识别需要Mock的外部依赖

步骤2：设计测试场景
- 正常流程测试：典型输入的预期输出
- 边界条件测试：极值、空值、边界值
- 异常场景测试：非法输入、异常抛出
- 业务逻辑测试：特定业务规则验证

步骤3：生成测试代码
- 创建测试类和测试方法
- 准备测试数据和Mock对象
- 调用目标方法并收集结果
- 添加断言验证预期结果
```

### 3. 验证与优化
```
步骤1：执行测试验证
- 编译生成的测试代码
- 运行测试并收集执行结果
- 统计新的代码覆盖率
- 记录测试执行中的错误

步骤2：分析覆盖效果
- 计算覆盖率提升幅度
- 识别仍未覆盖的代码路径
- 评估测试用例的质量
- 判断是否达到预期目标

步骤3：迭代优化决策
- 覆盖率达标 → 完成生成
- 覆盖率不足且未达最大迭代次数 → 继续优化
- 出现错误 → 进入错误修复流程
```

## 关键判断逻辑

### 1. 生成策略判断
```
IF 方法无任何测试覆盖:
    策略 = "首次全面生成"
    重点 = "基本功能验证 + 主要分支覆盖"
    
ELIF 方法有部分测试但覆盖率 < 目标值:
    策略 = "针对性补充生成"  
    重点 = "未覆盖分支 + 边界条件 + 异常场景"
    
ELIF 方法覆盖率达标但质量不高:
    策略 = "质量优化生成"
    重点 = "断言完善 + 测试数据优化 + 可读性提升"
```

### 2. 测试场景优先级
```
优先级1 - 核心业务逻辑:
- 方法的主要功能路径
- 关键的业务规则验证
- 重要的计算逻辑测试

优先级2 - 边界和异常:
- 输入参数的边界值测试
- 空值、null值的处理测试
- 异常情况的抛出和处理

优先级3 - 性能和兼容:
- 大数据量的性能测试
- 不同输入类型的兼容性
- 并发场景的安全性测试
```

### 3. 错误修复逻辑
```
编译错误处理:
- 检查import语句是否完整
- 验证类名、方法名是否正确
- 确认变量类型和作用域
- 修复语法错误和拼写错误

运行时错误处理:
- 添加null值检查和保护
- 修正Mock对象的配置
- 调整测试数据的准备
- 处理资源访问和权限问题

断言错误处理:
- 重新分析方法的预期行为
- 调整断言的期望值
- 完善测试数据的准备
- 考虑方法的副作用影响
```

## 质量控制要点

### 1. 测试用例质量标准
```
完整性检查:
✓ 是否覆盖了所有主要分支
✓ 是否包含边界条件测试
✓ 是否处理了异常场景
✓ 是否验证了业务逻辑

正确性检查:
✓ 测试数据是否合理有效
✓ Mock配置是否正确
✓ 断言是否准确反映预期
✓ 测试逻辑是否清晰

可维护性检查:
✓ 测试方法命名是否清晰
✓ 测试代码是否易于理解
✓ 是否遵循项目代码规范
✓ 是否包含必要的注释
```

### 2. 覆盖率评估标准
```
行覆盖率目标:
- 简单方法: >= 90%
- 复杂方法: >= 80%  
- 工具方法: >= 95%

分支覆盖率目标:
- 条件分支: >= 85%
- 异常分支: >= 70%
- 循环分支: >= 80%

路径覆盖率目标:
- 主要路径: 100%
- 异常路径: >= 60%
- 边界路径: >= 75%
```

## 实用生成模板

### 1. 基础测试结构模板
```
测试类结构:
- 类名: {目标类名}Test
- 包名: 与目标类相同，但在test目录下
- 注解: 根据测试框架添加必要注解

测试方法结构:
- 方法名: test{目标方法名}_{测试场景}
- 注解: @Test 等测试框架注解
- 结构: Given-When-Then 或 Arrange-Act-Assert

测试数据准备:
- 使用Builder模式构建复杂对象
- 提取常用测试数据为常量
- 使用工厂方法创建Mock对象
```

### 2. 常见场景处理模板
```
空值处理模板:
@Test(expected = IllegalArgumentException.class)
public void test{MethodName}_WithNullInput() {
    // Given: null input
    // When: call method with null
    // Then: expect exception
}

边界值测试模板:
@Test
public void test{MethodName}_WithBoundaryValues() {
    // Given: boundary values (min, max, zero)
    // When: call method with boundary values  
    // Then: verify expected behavior
}

异常场景模板:
@Test
public void test{MethodName}_WhenExceptionOccurs() {
    // Given: setup to trigger exception
    // When: call method
    // Then: verify exception handling
}
```

### 3. Mock使用指导
```
Mock创建原则:
- 只Mock外部依赖，不Mock被测试类
- 使用@Mock注解或Mockito.mock()创建
- 在@Before或@BeforeEach中初始化Mock

Mock行为配置:
- when().thenReturn() 配置返回值
- when().thenThrow() 配置异常抛出
- verify() 验证方法调用次数和参数

Mock最佳实践:
- Mock的行为要符合真实依赖的契约
- 避免过度Mock导致测试脆弱
- 使用ArgumentCaptor捕获复杂参数
```

## 迭代优化策略

### 1. 覆盖率不足时的优化
```
分析未覆盖代码:
- 识别未执行的代码行
- 分析未覆盖的原因（条件不满足、异常未触发等）
- 设计能触发未覆盖代码的测试场景

补充测试用例:
- 针对性地设计测试数据
- 调整Mock行为触发不同分支
- 添加异常场景的测试用例
```

### 2. 测试质量不高时的优化
```
断言优化:
- 使用更具体的断言方法
- 验证对象的关键属性而非整个对象
- 添加自定义断言提高可读性

测试数据优化:
- 使用更真实的业务数据
- 减少测试数据的复杂性
- 提高测试数据的可维护性
```


