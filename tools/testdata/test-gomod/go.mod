module icode.baidu.com/baidu/cov/smartUT-parser

go 1.19

require (
	github.com/antlabs/strsim v0.0.3
	github.com/deckarep/golang-set/v2 v2.6.0
	github.com/dlclark/regexp2 v1.11.2
	github.com/go-enry/go-enry/v2 v2.8.8
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826
	github.com/smacker/go-tree-sitter v0.0.0-20240625050157-a31a98a7c0f6
	github.com/smartystreets/goconvey v1.8.1
	github.com/stretchr/testify v1.9.0
	github.com/toolkits/file v0.0.0-20160325033739-a5b3c5147e07
	golang.org/x/text v0.16.0
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d
)

require (
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/go-enry/go-oniguruma v1.2.1 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
