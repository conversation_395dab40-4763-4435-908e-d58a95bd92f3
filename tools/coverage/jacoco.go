package coverage

import (
	"encoding/xml"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
)

func ParseJacocoXML(jacocoXmlPath string) (map[string]*CoverageInfo, error) {
	xmlFile, err := os.Open(jacocoXmlPath)
	if err != nil {
		return nil, err
	}
	defer xmlFile.Close()

	byteValue, _ := ioutil.ReadAll(xmlFile)
	var report Report
	xml.Unmarshal(byteValue, &report)

	coverageInfos := make(map[string]*CoverageInfo, 0)

	for _, pkg := range report.Packages {
		for _, src := range pkg.SourceFiles {
			linesCovered := make([]int, 0)
			linesMissed := make([]int, 0)
			BranchesMissed := make([]int, 0)
			BranchesCovered := make([]int, 0)

			lineLn := len(src.Lines)
			for index, line := range src.Lines {
				if line.Ci > 0 {
					linesCovered = append(linesCovered, line.Number)
				} else {
					linesMissed = append(linesMissed, line.Number)
				}

				if line.Cb > 0 || line.Mb > 0 {
					if line.Cb > 0 {
						// true分支覆盖，继续判定下一条语句是否覆盖
						if line.Mb == 0 {
							BranchesCovered = append(BranchesCovered, line.Number)
							if index+1 < lineLn && src.Lines[index+1].Ci == 0 {
								src.Lines[index+1].Ci = 1
							}
							continue
						} else if index+1 < lineLn {
							if src.Lines[index+1].Ci > 0 {
								BranchesCovered = append(BranchesCovered, line.Number)
								continue
							}
						}
					}

					BranchesMissed = append(BranchesMissed, line.Number)
				}
			}

			if len(linesCovered) > 0 {
				coverageInfo := &CoverageInfo{
					CoveredLines:    linesCovered,
					MissedLines:     linesMissed,
					MissedBranches:  BranchesMissed,
					CoveredBranches: BranchesCovered,
				}
				coverageInfo.GetCoverage()
				coverageInfos[strings.ReplaceAll(filepath.Join(pkg.Name,
					strings.TrimSuffix(src.Name, ".java")), string(filepath.Separator), ".")] = coverageInfo

			}
		}
	}

	return coverageInfos, nil
}
