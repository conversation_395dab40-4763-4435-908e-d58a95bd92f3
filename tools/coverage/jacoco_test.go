package coverage

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseJacocoXML(t *testing.T) {
	tests := []struct {
		name      string
		xmlFile   string
		expected  int
		expectErr bool
	}{
		{
			name:     "valid xml file",
			xmlFile:  "/Users/<USER>/git/baidu/bugbye/bcaandroid/target/site/jacoco/jacoco.xml",
			expected: 34,
		},
		{
			name:      "non-existent file",
			xmlFile:   "./testdata/jacoco-report-non-existent.xml",
			expected:  0,
			expectErr: true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			actual, err := ParseJacocoXML(test.xmlFile)
			if test.expectErr {
				assert.Error(t, err, "ParseJacocoXML should return an error")
			} else {
				assert.NoError(t, err, "ParseJacocoXML should not return an error")
				assert.Equal(t, test.expected, len(actual), "ParseJacocoXML should return the expected coverage info")
			}
		})
	}
}

func TestCoverageInfo_GetCoverage(t *testing.T) {
	tests := []struct {
		name             string
		coveredLines     []int
		missedLines      []int
		expectedCoverage float64
	}{
		{
			name:             "no lines covered",
			coveredLines:     nil,
			missedLines:      nil,
			expectedCoverage: 0,
		},
		{
			name:             "no lines covered",
			coveredLines:     nil,
			missedLines:      []int{1, 2, 3},
			expectedCoverage: 0,
		},
		{
			name:             "all lines covered",
			coveredLines:     []int{1, 2, 3},
			missedLines:      nil,
			expectedCoverage: 100,
		},
		{
			name:             "some lines covered",
			coveredLines:     []int{1},
			missedLines:      []int{2, 3, 4},
			expectedCoverage: 25,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			c := CoverageInfo{
				CoveredLines: test.coveredLines,
				MissedLines:  test.missedLines,
			}
			assert.Equal(t, test.expectedCoverage, c.GetCoverage())
		})
	}
}

func TestCoverageInfo_GetCoveragePercent(t *testing.T) {
	tests := []struct {
		name             string
		coveredLines     []int
		missedLines      []int
		expectedCoverage float64
	}{
		{
			name:             "no lines covered",
			coveredLines:     nil,
			missedLines:      nil,
			expectedCoverage: 0,
		},
		{
			name:             "no lines covered",
			coveredLines:     nil,
			missedLines:      []int{1, 2, 3},
			expectedCoverage: 0,
		},
		{
			name:             "all lines covered",
			coveredLines:     []int{1, 2, 3},
			missedLines:      nil,
			expectedCoverage: 100,
		},
		{
			name:             "some lines covered",
			coveredLines:     []int{1},
			missedLines:      []int{2, 3, 4},
			expectedCoverage: 25,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			c := CoverageInfo{
				CoveredLines: test.coveredLines,
				MissedLines:  test.missedLines,
			}
			fmt.Println(c.GetCoveragePercent())
			assert.Equal(t, test.expectedCoverage, c.GetCoveragePercent())
		})
	}
}
