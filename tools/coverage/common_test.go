package coverage

import "testing"

func TestIsUp(t *testing.T) {
	tests := []struct {
		name                             string
		newCoveredLines, oldCoveredLines []int
		want                             bool
	}{
		{
			name:            "no old covered lines",
			newCoveredLines: []int{1, 2, 3},
			oldCoveredLines: []int{},
			want:            true,
		},
		{
			name:            "no new covered lines",
			newCoveredLines: []int{},
			oldCoveredLines: []int{1, 2, 3},
			want:            false,
		},
		{
			name:            "no coverage increase",
			newCoveredLines: []int{1, 2},
			oldCoveredLines: []int{1, 2, 3},
			want:            false,
		},
		{
			name:            "coverage increased",
			newCoveredLines: []int{1, 2, 3},
			oldCoveredLines: []int{1, 3, 4},
			want:            true,
		},
		{
			name:            "covered lines in new but not in old",
			newCoveredLines: []int{1, 2, 3, 4},
			oldCoveredLines: []int{1, 3, 4},
			want:            true,
		},
		{
			name:            "oldCoveredLines is null",
			newCoveredLines: []int{1, 2, 3, 4},
			oldCoveredLines: nil,
			want:            true,
		},
		{
			name:            "same list",
			newCoveredLines: []int{1, 2, 3, 4},
			oldCoveredLines: []int{1, 2, 3, 4},
			want:            false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isUp(tt.newCoveredLines, tt.oldCoveredLines); got != tt.want {
				t.Errorf("IsCoverageUp() = %v, want %v", got, tt.want)
			}
		})
	}
}
