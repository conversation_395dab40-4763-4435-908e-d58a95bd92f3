package coverage

import (
	"bufio"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"os"
	"sort"
	"strconv"
	"strings"
)

// parseCoverageFile 解析覆盖率文件并返回 CoverageInfo
func parseGoCoverageFile(filePath string) (map[string]*CoverageInfo, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	coverageInfosMap := make(map[string]*CoverageInfo)
	for scanner.Scan() {
		line := scanner.Text()

		// 跳过模式行
		if strings.HasPrefix(line, "mode:") {
			continue
		}

		// 按行解析覆盖信息
		parts := strings.Split(line, " ")
		if len(parts) != 3 {
			continue
		}

		fileRange := parts[0]
		// 获取行范围
		fileName := strings.Split(fileRange, ":")[0]

		coverageInfo := coverageInfosMap[fileName]
		if coverageInfo == nil {
			coverageInfo = &CoverageInfo{}
			coverageInfosMap[fileName] = coverageInfo
		}
		coveredFlag := parts[2]

		// 获取行范围
		rangeParts := strings.Split(fileRange, ":")[1]
		startEnd := strings.Split(rangeParts, ",")
		startLine, _ := strconv.Atoi(strings.Split(startEnd[0], ".")[0])
		endLine, _ := strconv.Atoi(strings.Split(startEnd[1], ".")[0])

		// 根据覆盖标志分类
		for i := startLine; i <= endLine; i++ {
			if coveredFlag == "1" {
				coverageInfo.CoveredLines = append(coverageInfo.CoveredLines, i)
			} else {
				coverageInfo.MissedLines = append(coverageInfo.MissedLines, i)
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}
	for _, v := range coverageInfosMap {
		v.CoveredLines = helper.RemoveDuplicates(v.CoveredLines)
		v.MissedLines = helper.RemoveElementsWithInAnotherArr(helper.RemoveDuplicates(v.MissedLines), v.CoveredLines)
		sort.Ints(v.MissedLines)
		sort.Ints(v.CoveredLines)

		v.Coverage = float64(len(v.CoveredLines)) / float64(len(v.CoveredLines)+len(v.MissedLines)) * 100
	}

	return coverageInfosMap, nil
}
