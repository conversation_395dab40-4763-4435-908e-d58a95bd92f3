package coverage

func IsCoverageUp(newCover, oldCover *CoverageInfo) bool {
	if newCover == nil {
		return false
	}

	if len(newCover.CoveredLines) != 0 {
		if oldCover == nil {
			return true
		}
	}

	if isUp(newCover.CoveredLines, oldCover.CoveredLines) {
		return true
	} else if isUp(newCover.CoveredBranches, oldCover.CoveredBranches) {
		return true
	} else if isUp(newCover.MissedBranches, newCover.MissedBranches) {
		return true
	}

	return false
}

func isUp(newCoveredLines, oldCoveredLines []int) bool {
	if len(newCoveredLines) == 0 {
		return false
	} else if len(oldCoveredLines) == 0 && len(newCoveredLines) > 0 {
		return true
	}

	i, j := 0, 0
	n, m := len(oldCoveredLines), len(newCoveredLines)

	// 采用双指针的方式，比较两个列表
	for i < n && j < m {
		if oldCoveredLines[i] == newCoveredLines[j] {
			// 如果元素相等，移动两个指针
			i++
			j++
		} else if oldCoveredLines[i] < newCoveredLines[j] {
			// 如果第一个列表的元素小于第二个列表的元素，移动第一个列表的指针
			i++
		} else {
			// 如果第一个列表的元素大于第二个列表的元素，说明第二个列表的当前元素不在第一个列表中
			return true
		}
	}

	// 如果第二个列表还有剩余元素，说明这些元素不在第一个列表中
	if j < m {
		return true
	}

	return false

}
