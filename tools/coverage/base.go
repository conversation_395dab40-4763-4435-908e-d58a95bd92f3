package coverage

import (
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/helper"
)

type Report struct {
	Packages []Package `xml:"package"`
}

type Package struct {
	Name        string       `xml:"name,attr"`
	Classes     []Class      `xml:"class"`
	SourceFiles []SourceFile `xml:"sourcefile"`
}

type Class struct {
	Name           string `xml:"name,attr"`
	SourceFileName string `xml:"sourcefilename,attr"`
}

type SourceFile struct {
	Name  string `xml:"name,attr"`
	Lines []Line `xml:"line"`
}

type Line struct {
	Number int `xml:"nr,attr"`
	Ci     int `xml:"ci,attr"`
	Mb     int `xml:"mb,attr"`
	Cb     int `xml:"cb,attr"`
}

type ClassCoverage struct {
	LinesCovered, LinesMissed []int
}

// CoverageInfo 覆盖率信息
type CoverageInfo struct {
	Coverage float64 `json:"coverage"`

	MissedLines  []int `json:"missed_lines"`
	CoveredLines []int `json:"covered_lines"`

	MissedBranches  []int `json:"missed_branches"`  // 这里做个特殊处理，如果分支的true分支未覆盖则认为未覆盖
	CoveredBranches []int `json:"covered_branches"` // 这里做个特殊处理，如果分支的true分支覆盖则认为覆盖
}

func (c *CoverageInfo) GetCoveragePercent() float64 {
	if c == nil {
		return 0
	}

	if len(c.MissedLines)+len(c.CoveredLines) > 0 {
		return helper.Divide(len(c.CoveredLines), len(c.MissedLines)+len(c.CoveredLines), 4) * 100.00
	}
	return 0
}

func (c *CoverageInfo) GetCoverage() float64 {
	if c == nil {
		return 0
	}
	if len(c.MissedLines)+len(c.CoveredLines) > 0 {
		c.Coverage = helper.Divide(len(c.CoveredLines), len(c.MissedLines)+len(c.CoveredLines), 4) * 100
	}
	return c.Coverage
}

func (c *CoverageInfo) GetIncCoverage(diffLines []int) (int, int) {
	// 没有覆盖率统计 或者不是增量
	if c == nil || diffLines == nil {
		return 0, 0
	}

	if len(diffLines) > 0 {
		return len(helper.Intersect(c.CoveredLines, diffLines)), len(helper.Intersect(c.CoveredLines, diffLines)) +
			len(helper.Intersect(c.MissedLines, diffLines))
	} else {
		return len(c.CoveredLines), len(c.MissedLines) + len(c.CoveredLines)
	}
}

func (c *CoverageInfo) Merge(other *CoverageInfo) {
	originalMissedLines := c.MissedLines
	originalCoveredLines := c.CoveredLines
	originalMissedBranches := c.MissedBranches
	originalCoveredBranches := c.CoveredBranches
	oldLnLen := len(originalMissedLines) + len(originalCoveredLines)
	oldBrLen := len(originalMissedBranches) + len(originalCoveredBranches)

	c.MissedLines = helper.RemoveAndSort(c.MissedLines, other.CoveredLines)
	c.CoveredLines = helper.MergeAndSort(c.CoveredLines, other.CoveredLines)
	c.MissedBranches = helper.RemoveAndSort(c.MissedBranches, other.CoveredBranches)
	c.CoveredBranches = helper.MergeAndSort(c.CoveredBranches, other.CoveredBranches)

	if oldLnLen != len(c.MissedLines)+len(c.CoveredLines) || oldBrLen != len(c.MissedBranches)+len(c.CoveredBranches) {
		fmt.Println("merge error")
	}
}

func (c *CoverageInfo) IsCoverageReached(desired float64) bool {
	return c.GetCoveragePercent() >= desired
}

// GetMethodCoverage 根据指定的行号范围获取代码覆盖率信息
//
// 参数：
//
//	c *CoverageInfo：包含代码覆盖率信息的CoverageInfo对象指针
//	startLn int：行号范围的起始行号
//	endLn int：行号范围的结束行号
//
// 返回值：
//
//	*CoverageInfo：根据指定行号范围生成的新的CoverageInfo对象指针，包含被覆盖行号列表、未被覆盖行号列表以及覆盖率信息
func (c *CoverageInfo) GetMethodCoverage(startLn, endLn int) *CoverageInfo {
	coveredLines := helper.FindRangeByValues(c.CoveredLines, startLn, endLn)
	missedLines := helper.FindRangeByValues(c.MissedLines, startLn, endLn)
	return &CoverageInfo{
		CoveredLines: coveredLines,
		MissedLines:  missedLines,
		Coverage:     helper.Divide(len(coveredLines), len(coveredLines)+len(missedLines), 4),
	}
}
