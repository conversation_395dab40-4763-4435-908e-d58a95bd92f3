package tools

import (
	"errors"
	"path/filepath"
	"strings"

	"github.com/waigani/diffparser"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
)

func GetGitBranch(codeBase string) (string, error) {
	retCode, output := GetShellStatusOutput([]string{"git", "branch", "--show-current"}, codeBase, false)
	if retCode != 0 {
		return "", errors.New("命令运行失败： git symbolic-ref --short HEAD")
	}
	return strings.TrimSpace(output), nil
}

func GetGitRemoteCommitId(codeBase, branch string) (string, error) {
	cmdList := []string{"git", "rev-parse", "HEAD^"}
	retCode, output := GetShellStatusOutput(cmdList, codeBase, false)
	if retCode != 0 {
		return "", errors.New("命令运行失败： " + strings.Join(cmdList, " "))
	}
	return strings.TrimSpace(output), nil
}

func IsGitRepo(workDir string) bool {
	retCode, _ := GetShellStatusOutput([]string{"git", "status"}, workDir, false)
	return retCode == 0
}

func GetFileDiffContent(workDir string, baseCommitID string, filePath string) string {
	cmd := `git diff %s "%s"`
	params := []string{baseCommitID, filePath}
	cmdList, _ := AssembleCmdList(cmd, params)
	retCode, output := GetShellStatusOutput(cmdList, workDir, false)
	if retCode != 0 {
		logger.Warn("diff错误：获取从基准版本%s，到当前版本针对文件%s内容失败", baseCommitID, filePath)
	}

	return output
}

func GetDiffFiles(workDir, baseCommitID, cmd string) ([]string, string) {
	gitDir, err := GetGitRoot(workDir)
	if err != nil {
		logger.Warn("diff错误：获取从基准版本%s，到当前版本的内容失败", baseCommitID)
	}

	cmdList, _ := AssembleCmdList(cmd, nil)
	retCode, output := GetShellStatusOutput(cmdList, workDir, false)
	if retCode != 0 {
		logger.Fatal("diff错误：获取从基准版本%s，到当前版本的内容失败", baseCommitID)
	}

	files := make([]string, 0)
	for _, file := range strings.Split(output, "\n") {
		if file != "" {
			files = append(files, filepath.Join(gitDir, file))
		}
	}

	return files, gitDir
}

func GetUntrackedFiles(workDir string) []string {
	cmd := []string{"git", "ls-files", "--others", "--exclude-standard"}
	retCode, output := GetShellStatusOutput(cmd, workDir, false)
	if retCode != 0 || output == "" {
		return nil
	}
	files := make([]string, 0)
	for _, file := range strings.Split(output, "\n") {
		if strings.TrimSpace(file) != "" {
			files = append(files, filepath.Join(workDir, file))
		}
	}
	return files
}

type DiffInfo struct {
	DiffType  diffparser.DiffLineMode
	NewName   string
	OldName   string
	DiffLines []int
}

func ParseDiffs(content string) map[string]DiffInfo {
	diffInfos := make(map[string]DiffInfo)
	diff, err := diffparser.Parse(content)
	if err != nil {
		logger.Debug("文件错误: git diff日志无法解析: %v", err)
		return nil
	}

	for _, file := range diff.Files {
		diffInfo := new(DiffInfo)
		diffInfo.NewName = file.NewName
		if file.OrigName != "" {
			diffInfo.OldName = file.OrigName
		} else {
			diffInfo.OldName = diffInfo.NewName
		}

		for _, hunk := range file.Hunks {
			for _, line := range hunk.NewRange.Lines {
				if line.Mode == 0 && line.Content != "" && ValidDiffLine(line.Content) {
					diffInfo.DiffType = line.Mode
					diffInfo.DiffLines = append(diffInfo.DiffLines, line.Number)
				}
			}
		}
		diffInfos[diffInfo.NewName] = *diffInfo
	}

	return diffInfos
}

func ValidDiffLine(lineContent string) bool {
	pureContent := strings.TrimSpace(lineContent)
	// 删除注释行
	if strings.HasPrefix(pureContent, "//") || strings.HasPrefix(pureContent, "#") ||
		pureContent == "{" || pureContent == "}" {
		return false
	}
	return true
}

func ParseDiffByDiffLog(diffContent string, validExts []string, antMatcher *helper.AntMatcher) map[string]DiffInfo {
	finalDiffInfos := make(map[string]DiffInfo)
	diffInfos := ParseDiffs(diffContent)
	if diffInfos == nil {
		logger.DebugT(1, "文件错误：解析文件%s发生错误", diffContent)
		return nil
	}

	for k, _ := range diffInfos {
		if len(validExts) != 0 && !helper.InSlice(validExts, filepath.Ext(k)) {
			logger.Debug("\t%-10s: %s", "[Invalid]", k)
		} else if antMatcher != nil && !helper.StringAntMatch(k, antMatcher) {
			logger.Debug("\t%-10s: %s", "[Invalid]", k)
		} else {
			logger.Debug("\t%-10s: %s", "", k)
			finalDiffInfos[k] = diffInfos[k]
		}
	}

	return finalDiffInfos
}

func IsValidCommitID(codeBase string, commitID string) bool {
	cmdList := []string{"git", "show", "-s", commitID}
	if retCode, _ := GetShellStatusOutput(cmdList, codeBase, false); retCode != 0 {
		return false
	}
	return true
}

func GetGitRoot(workDir string) (string, error) {
	cmdList := []string{"git", "rev-parse", "--show-toplevel"}
	retCode, output := GetShellStatusOutput(cmdList, workDir, false)
	if retCode != 0 {
		return "", errors.New("获取git根目录失败: " + output)
	}
	return strings.TrimSpace(output), nil
}

type CommitInfo struct {
	CommitID  string
	Committer string
}

func GetCodeCommitInfo(gitRoot string) (*CommitInfo, error) {
	retCode, output := GetShellStatusOutput([]string{"git", "log", "-n", "1"}, gitRoot, false)
	if retCode != 0 {
		return nil, errors.New("命令运行失败： git log -n 1")
	}
	commitInfo := new(CommitInfo)
	outputs := strings.Split(string(output), "\n")
	for _, o := range outputs {
		words := strings.Split(o, " ")
		if len(words) > 0 {
			if words[0] == "commit" {
				commitInfo.CommitID = words[1]
			} else if words[0] == "Author:" {
				commitInfo.Committer = words[1]
			}
		}
	}
	return commitInfo, nil
}

// IsCommitInRemote checks if the given commit ID is in any remote branch
func IsCommitInRemote(workDir string, commitID string) bool {
	GetShellStatusOutput([]string{"git", "fetch"}, workDir, false)
	cmdList, _ := AssembleCmdList("git branch -r --contains %s", []string{commitID})
	status, output := GetShellStatusOutput(cmdList, workDir, false)
	if status == 0 && len(output) > 0 {
		return true
	}

	return false
}
