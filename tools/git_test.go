package tools

import "testing"

func Test_isCommitInRemote(t *testing.T) {
	tests := []struct {
		name     string
		commitID string
		want     bool
	}{
		{
			name:     "commit does not exists in remote",
			commitID: "abc123",
			want:     false,
		},
		{
			name:     "commit exist in remote",
			commitID: "ca56c43639da68d040ee61e2e216942516b053ae",
			want:     true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsCommitInRemote(".", tt.commitID); got != tt.want {
				t.Errorf("isCommitInRemote() = %v, want %v", got, tt.want)
			}
		})
	}
}
