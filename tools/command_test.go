package tools

import (
	"fmt"
	"reflect"
	"strings"
	"testing"
)

func TestAssembleCmdList(t *testing.T) {
	type args struct {
		cmd    string
		params []string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "Test AssembleCmdList with valid input",
			args: args{
				cmd:    "echo %s %d",
				params: []string{"hello", "123"},
			},
			want:    []string{"echo", "hello", "123"},
			wantErr: false,
		},
		{
			name: "Test AssembleCmdList with invalid input",
			args: args{
				cmd:    "echo %s %d",
				params: []string{"hello"},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := AssembleCmdList(tt.args.cmd, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("AssembleCmdList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("AssembleCmdList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetShellStatusOutputErrorTimeOut(t *testing.T) {
	cmd := fmt.Sprintf(" go  test -v -gcflags 'all=-N -l' -timeout 1m -coverprofile=%s", "cov.out")
	shellCmd := GetShellStatusOutputErrorTimeOut(strings.Split(cmd, " "), ".", true, 30)
	fmt.Println(shellCmd.Output)
}
