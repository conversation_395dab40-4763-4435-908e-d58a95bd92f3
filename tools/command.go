package tools

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/logger"
)

type ShellCmdInfo struct {
	CmdState  int // 0,表示执行没有异常，1表示cmd执行异常
	Output    string
	ErrorInfo string
	cmd       string
}

// Print Print 打印ShellCmdInfo结构体的信息，包括状态码、命令、输出和错误信息。
// 参数：无
// 返回值：无
func (s ShellCmdInfo) Print(msg string) {
	if s.CmdState != 0 {
		logger.Info("run command status %d,cmd %s, output %s, error %s", s.CmdState, s.cmd, s.Output, s.ErrorInfo)
	} else {
		if msg != "" {
			logger.Debug(msg)
		}
		logger.Debug("run command status %d,cmd %s, output %s, error %s", s.CmdState, s.cmd, s.Output, s.ErrorInfo)
	}
}

// PrintTimeCost 打印运行时间成本，参数为time.Duration类型的cost，用于记录命令执行所需时间
func (s ShellCmdInfo) PrintTimeCost(cost time.Duration) {
	logger.Debug("run command status %d,cmd %s,cost time %s, output %s, error %s", s.CmdState, s.cmd, cost, s.Output, s.ErrorInfo)
}

func GetShellStatusOutput(cmdList []string, workDir string, print bool) (int, string) {
	cmd := GetCommandBaseOnOs(nil, cmdList)

	if print {
		logger.Info("start to execute cmd in workdir %s:\n%s", workDir, cmd)
	}

	if workDir != "" {
		cmd.Dir = filepath.Clean(workDir)
	}
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil && print {
		logger.Info("execute cmd failed with error:%s, %v", cmdList, err)
		return cmd.ProcessState.ExitCode(), out.String()
	}
	return cmd.ProcessState.ExitCode(), out.String()
}

// GetShellStatusOutputErrorTimeOut GetShellStatusOutputErrorTimeOut 获取shell状态输出错误超时函数
// cmdStr string - 需要执行的命令字符串
// workDir string - 工作目录，默认为空字符串
// print bool - 是否打印日志，默认为true
// 返回值 ShellCmdInfo - shell命令信息结构体，包含以下字段：
// CmdState int - 命令状态码，如果命令执行失败则为-1
// RunState int - 运行状态码，如果命令执行失败则为-1
// Output string - 命令执行后的输出内容
// ErrorInfo string - 命令执行过程中遇到的错误信息
// cmd string - 原始命令字符串
// ⚠️注意：传入的cmdList必须按照参数的组织形态进行正确的分割，否则可能导致命令执行异常失败。
func GetShellStatusOutputErrorTimeOut(cmdList []string, workDir string, print bool, timeout int) ShellCmdInfo {
	if print {
		logger.Debug("start to execute cmd in workdir %s:\n%s", workDir, cmdList)
	}
	// 创建一个带有超时的 Context
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	// 创建一个 Command 对象
	cmd := GetCommandBaseOnOs(ctx, cmdList)

	if workDir != "" {
		cmd.Dir = workDir
	}
	var out bytes.Buffer
	var errInfo bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &errInfo

	// 启动命令
	err := cmd.Start()
	if err != nil {
		return ShellCmdInfo{}
	}

	// 使用 Wait 来等待命令的完成，并检查错误
	done := make(chan error, 1)
	go func() {
		done <- cmd.Wait()
	}()

	select {
	case <-ctx.Done():
		// 超时发生，中断命令执行
		err := cmd.Process.Kill()
		errInfo.WriteString(fmt.Sprintf("Command timed out, kill with error: %v\n", err))
		if err != nil && !strings.Contains(err.Error(), "process already finished") {
			logger.Warn("failed to kill command: %v\n", err)
		}
	case err := <-done:
		// 命令执行完成
		if err != nil {
			logger.Warn("execute cmd (%s) failed with error: %v，out info：%s \n"+
				"error info ：%s\n", cmd, err, out.String(), errInfo.String())
		}
	}

	return ShellCmdInfo{
		CmdState:  cmd.ProcessState.ExitCode(),
		Output:    out.String(),
		ErrorInfo: errInfo.String(),
		cmd:       strings.Join(cmdList, " "),
	}
}

func GetCommandBaseOnOs(ctx context.Context, cmdList []string) *exec.Cmd {
	if IsWindows() {
		if ctx != nil {
			cmdArgs := append([]string{"/C"}, cmdList...)
			return exec.CommandContext(ctx, "cmd", cmdArgs...)
		} else {
			cmdArgs := append([]string{"/C"}, cmdList...)
			return exec.Command("cmd", cmdArgs...)
		}
	} else {
		if ctx != nil {
			return exec.CommandContext(ctx, "bash", "-c", strings.Join(cmdList, " "))
		} else {
			return exec.Command("bash", "-c", strings.Join(cmdList, " "))
		}
	}
}

func AssembleCmdList(cmd string, params []string) ([]string, error) {
	if (strings.Count(cmd, "%s") + strings.Count(cmd, "%d")) != len(params) {
		err := errors.New(fmt.Sprintf("命令参数个数不匹配:\ncmd:%s\nparams:%s", cmd, strings.Join(params, ",")))
		logger.Error(err.Error())
		return nil, err
	}

	cmdList := strings.Split(cmd, " ")
	index := 0
	var resCmd []string
	for _, c := range cmdList {
		if strings.Contains(c, "%s") || strings.Contains(c, "%d") {
			// windows cmd 不支持单引号
			if IsWindows() && strings.HasPrefix(params[index], "'") {
				params[index] = strings.Replace(params[index], "'", "", -1)
			}
			c = fmt.Sprintf(c, params[index])
			resCmd = append(resCmd, c)
			index++
		} else if c != "" {
			resCmd = append(resCmd, c)
		}
	}

	return resCmd, nil
}
