package statistics

import (
	"errors"
	"fmt"
	"github.com/waigani/diffparser"
	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/tools"
	"net/url"
	"path/filepath"
	"strings"

	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
)

const ComateAcceptOnlineURL = "https://comate.now.baidu-int.com/api/generate"
const ComateAcceptSandboxURL = "https://comate-test.now.baidu-int.com/api/generate"

// cov上报采纳接口
var covUploadGenSandboxURL = "http://smart-ut.test.cov.appspace.baidu.com/api/report/batchUT/generate"
var covUploadGenOnlineURL = "http://10.11.60.160:8200/api/report/batchUT/generate"

type ReportGenerationParam struct {
	CodeRepo   *models.CodeRepo `json:"code_repo"`
	Trigger    string           `json:"trigger"`
	SrcContent string           `json:"src_content"`
	UtAbsPath  string           `json:"ut_abs_path"`
	UtContent  string           `json:"test_content"`
	CaseNum    int              `json:"case_num"`
}

type ReportCovUTFileInfo struct {
	//CaseName   string `json:"case_name"`
	CaseLines  int    `json:"case_lines"`
	CaseNum    int    `json:"case_num"`
	ComateUuid string `json:"comate_uuid"`
	UserName   string `json:"user_name"`
}

// 用于调用comate采纳接口，获取uuid
// 文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/PU5CIeen_Fh2il#anchor-55dcc1c0-8b0d-11ed-aced-6b04a74ad59f
type GenerateParam struct {
	Uuid    string `json:"uuid"`
	Content string `json:"content"`
}

func (report *ReportGenerationParam) getURL() string {
	if config.IsDebugEnv() {
		return ComateAcceptSandboxURL
	}
	return ComateAcceptOnlineURL
}

func (report *ReportGenerationParam) ReportGenerationForFile() error {
	if httputil.IsBaiduInnerIp() && report.Trigger != constant.IDETrigger && report.CodeRepo != nil {
		trigger := strings.ToLower(report.Trigger)
		if trigger == "" {
			trigger = "tool"
		}

		crTag := "merge"
		if report.CodeRepo.IsCRCommit {
			crTag = "cr"
		}

		utRelativePath := strings.TrimPrefix(strings.TrimPrefix(report.UtAbsPath, report.CodeRepo.GitRoot), string(filepath.Separator))
		diffCode := getDiffCode(report.CodeRepo, utRelativePath, report.UtContent)

		generate := map[string]any{
			"path":                   utRelativePath,
			"repo":                   report.CodeRepo.RepoName,
			"username":               report.CodeRepo.Committer,
			"content":                url.QueryEscape(report.SrcContent), // 被测函数函数体
			"row":                    1,
			"col":                    1,
			"ide":                    strings.ToLower(trigger),
			"model":                  "UTAgent",
			"function":               crTag,
			"generatedContent":       url.QueryEscape(diffCode), // 对函数体进行编码，能够解决函数体中出现%v导致接口报500的问题
			"originGeneratedContent": url.QueryEscape(diffCode),
			"multiline":              true,
			//"accepted":               false, // 在comate接口上报生成，而不是上报采纳
		}

		comateRes, err := httputil.PostForm(report.getURL(), generate)
		if err != nil {
			logger.Error("report generate error", err)
		}
		resData := GenerateParam{}
		if err := helper.MarshalToObject(comateRes["data"], &resData); err != nil || comateRes["status"] != "OK" {
			return errors.New(fmt.Sprintf("上报生成失败，uuid is %s。请联系Comate为您解决问题", resData.Uuid))
		}
		logger.Info("report generation success")

		testCase := &ReportCovUTFileInfo{
			ComateUuid: resData.Uuid,
			UserName:   report.CodeRepo.Committer,
			CaseLines:  len(strings.Split(diffCode, "\n")),
			CaseNum:    report.CaseNum,
		}

		isSuccess := CovUploadGenForFile(report.CodeRepo, utRelativePath, []*ReportCovUTFileInfo{testCase})
		if !isSuccess {
			return errors.New("上报生成失败，请联系Comate为您解决问题")
		}
	}
	return nil
}

func getDiffCode(codeRepo *models.CodeRepo, utRelativePath, testContent string) string {
	diffContent := ""

	text := tools.GetFileDiffContent(codeRepo.GitRoot, "", utRelativePath)
	diff, err := diffparser.Parse(text)
	if err != nil {
		return testContent
	}

	for _, file := range diff.Files {
		if file.OrigName == utRelativePath || file.NewName == utRelativePath {
			for _, hunk := range file.Hunks {
				for _, line := range hunk.WholeRange.Lines {
					if line.Mode == 0 {
						diffContent = diffContent + line.Content + "\n"
					}
				}
			}
		}
	}
	if diffContent == "" {
		diffContent = testContent
	}

	return diffContent
}

func CovUploadGenForFile(codeRepo *models.CodeRepo, relativeUtFilePath string,
	testCases []*ReportCovUTFileInfo) bool {
	//baseCommitID := codeRepo.BaseCommitID
	//
	//// 如果是merge后用例生成，则baseCommitID为merge后的commitID
	//if !codeRepo.IsCRCommit {
	//	baseCommitID = codeRepo.CommitID
	//}
	var generate map[string]any
	generate = map[string]any{
		"repo":   codeRepo.RepoName,
		"branch": codeRepo.Branch,
		//"commit_id":      codeRepo.CommitID,
		//"base_commit_id": baseCommitID,
		"gen_commit_id": codeRepo.CommitID, // 统一为：用最新一次commit作为上报生成的commit
		"author":        codeRepo.Committer,
		"case_file":     relativeUtFilePath, //"core/common/diff_test.go",
		"test_cases":    testCases,
	}

	return covUploadGen(generate, getReportUrl())
}

func getReportUrl() string {
	if config.IsDebugEnv() {
		return covUploadGenSandboxURL
	} else {
		return covUploadGenOnlineURL
	}
}

func covUploadGen(generate map[string]any, url string) bool {
	covRes, err := httputil.HTTPPostBody(url, generate)
	if covRes["result"] != "SUCCESS" || err != nil {
		testCases := generate["test_cases"].([]*ReportCovUTFileInfo)
		testCase := testCases[0]
		logger.Info("Cov upload error，uuid is %s。请联系开发人员********************", testCase.ComateUuid)
		return false
	}
	return true
}
