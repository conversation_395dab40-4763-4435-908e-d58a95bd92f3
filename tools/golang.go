package tools

import (
	"golang.org/x/mod/modfile"
	"icode.baidu.com/baidu/cov/iUT/helper"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"os"
)

func GetGoModuleNameByWorkDir(workDir string) string {
	// 解析go.mod文件，获取模块名称
	modFiles, _ := helper.FindFileByName(workDir, "go.mod")
	if len(modFiles) == 0 {
		return ""
	}
	modFile, err := os.ReadFile(helper.FindOneOuterFile(modFiles))
	if err != nil {
		return ""
	}
	// 解析go.mod文件
	modData, err := modfile.Parse("", modFile, nil)
	if err != nil {
		logger.Warn("Error parsing go.mod:", err)
		return ""
	}
	return modData.Module.Mod.Path
}
