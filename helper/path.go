package helper

import (
	"path/filepath"
	"strings"
)

func GetRelativePath(abspath, gitPath string) string {
	// 使用filepath.Clean()函数来处理路径中的任何不规范的斜杠等字符
	abspath = filepath.Clean(abspath)
	gitPath = filepath.Clean(gitPath)

	// 确保gitPath是目录路径（以斜杠结尾）
	if !strings.HasSuffix(gitPath, string(filepath.Separator)) {
		gitPath += string(filepath.Separator)
	}

	// 使用strings.TrimPrefix()函数删除gitPath并获取相对路径
	relativePath := strings.TrimPrefix(abspath, gitPath)

	return strings.TrimLeft(relativePath, string(filepath.Separator))
}
