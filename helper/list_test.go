package helper

import (
	"reflect"
	"testing"
)

func TestInSlice(t *testing.T) {
	var s1 = []string{"a", "b", "c"}
	var s2 = []int{1, 2, 3}
	if !InSlice(s1, "a") {
		t.<PERSON><PERSON>("InSlice string not work")
	}
	if !InSlice(s2, 2) {
		t.<PERSON>("InSlice int not work")
	}
	if InSlice(s1, "d") {
		t.<PERSON>("InSlice string not work")
	}
	if InSlice(s2, 4) {
		t.<PERSON><PERSON>("InSlice int not work")
	}
}

func TestIntersect(t *testing.T) {
	type args struct {
		slice1 []string
		slice2 []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "intersect",
			args: args{
				slice1: []string{"a", "b", "c"},
				slice2: []string{"b", "c", "d"},
			},
			want: []string{"b", "c"},
		},
		{
			name: "no intersect",
			args: args{
				slice1: []string{"a", "b", "c"},
				slice2: []string{"d", "e", "f"},
			},
			want: []string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Intersect(tt.args.slice1, tt.args.slice2); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Intersect() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUnion(t *testing.T) {
	type args struct {
		a []string
		b []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "union",
			args: args{
				a: []string{"a", "b", "c"},
				b: []string{"b", "c", "d"},
			},
			want: []string{"a", "b", "c", "d"},
		},
		{
			name: "no union",
			args: args{
				a: []string{"a", "b", "c"},
				b: []string{"d", "e", "f"},
			},
			want: []string{"a", "b", "c", "d", "e", "f"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Union(tt.args.a, tt.args.b); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Union() = %v, want %v", got, tt.want)
			}
		})
	}
}

//
//func TestShuffle(t *testing.T) {
//	type args struct {
//		list []string
//	}
//	tests := []struct {
//		name string
//		args args
//		want []string
//	}{
//		{
//			name: "shuffle",
//			args: args{
//				list: []string{"a", "b", "c", "d", "e"},
//			},
//			want: []string{"a", "b", "c", "d", "e"},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			Shuffle(tt.args.list)
//			if reflect.DeepEqual(tt.args.list, tt.want) {
//				t.Errorf("Shuffle() = %v, want %v", tt.args.list, tt.want)
//			}
//		})
//	}
//}
