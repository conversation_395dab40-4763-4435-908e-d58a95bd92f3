package helper

import (
	"archive/tar"
	"archive/zip"
	"bufio"
	"bytes"
	"compress/gzip"
	"encoding/hex"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/config"
	"icode.baidu.com/baidu/cov/iUT/helper/constant"
	"icode.baidu.com/baidu/cov/smartUT-parser/utils"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/gabriel-vasile/mimetype"
	"github.com/toolkits/file"
	"github.com/vibrantbyte/go-antpath/antpath"
	"icode.baidu.com/baidu/cov/iUT/logger"
)

func IsDir(path string) bool {
	fileInfo, err := os.Stat(path)
	if err == nil {
		return fileInfo.IsDir()
	}
	return false
}

func IsFile(path string) bool {
	if fileInfo, err := os.Stat(path); err == nil {
		return !fileInfo.IsDir()
	}
	return false
}

const (
	XML      = "3C3F786D6C"
	INFO     = "544E3A"
	OUT      = "6D6F64"
	EXEC1007 = "01C0C01007"
	COV81321 = "42756C6C73"
	COV8939  = "432D436F76"

	MimeTGZ   = "application/gzip"
	MimeTAR   = "application/x-tar"
	MimeZIP   = "application/zip"
	MimeXML   = "text/xml; charset=utf-8"
	MimeHTML  = "text/html; charset=utf-8"
	MimePLAIN = "text/plain; charset=utf-8"
)

func GuessFileCompressType(filepath string) (string, error) {
	mimeType, err := mimetype.DetectFile(filepath)
	return mimeType.String(), err
}

func GetFileHeader(filePath string, typeLen int) string {
	f, err := os.Open(filePath)
	if err != nil {
		logger.Panic("open file %s error: %v", filePath, err)
	}
	fSrc, _ := io.ReadAll(f)
	src := fSrc[:typeLen]

	res := bytes.Buffer{}
	if src == nil || len(src) <= 0 {
		return ""
	}
	temp := make([]byte, 0)
	for _, v := range src {
		sub := v & 0xFF
		hv := hex.EncodeToString(append(temp, sub))
		if len(hv) < 2 {
			res.WriteString(strconv.FormatInt(int64(0), 10))
		}
		res.WriteString(hv)
	}
	return res.String()
}

func IsFileExist(src string) bool {
	if src == "" {
		return false
	}
	_, err := os.Stat(src)
	return !os.IsNotExist(err)
}

func GetFileInfo(src string) os.FileInfo {
	var fileInfo os.FileInfo
	var e error

	if fileInfo, e = os.Stat(src); e != nil {
		return nil
	}
	return fileInfo
}

func GetFileContent(path string) ([]byte, error) {
	fi, err := os.Open(path)
	defer fi.Close()
	if err != nil {
		return nil, err
	}
	fd, err := io.ReadAll(fi)
	return fd, nil
}

func IsSymbolLink(file_path string) (is_symbol_link bool, err error) {
	file_info, err := os.Lstat(file_path)
	if err != nil {
		err_msg := fmt.Sprintf("Fail to get file info of %s: %s", file_path, err.Error())
		return false, errors.New(err_msg)
	}
	return file_info.Mode()&os.ModeSymlink != 0, nil
}

// Untar takes a destination path and a reader; a tar reader loops over the tarfile
// creating the file structure at 'dst' along the way, and writing any files
func Untar(src string, dst string) error {
	r, _ := os.Open(src)
	gzr, err := gzip.NewReader(r)
	if err != nil {
		return err
	}
	defer gzr.Close()

	tr := tar.NewReader(gzr)

	for {
		header, err := tr.Next()

		switch {
		// if no more files are found return
		case err == io.EOF:
			return nil

		// return any other error
		case err != nil:
			return err

		// if the header is nil, just skip it (not sure how this happens)
		case header == nil:
			continue
		}

		// the target location where the dir/file should be created
		target := filepath.Join(dst, header.Name)

		// the following switch could also be done using fi.Mode(), not sure if there
		// a benefit of using one vs. the other.
		// fi := header.FileInfo()

		// check the file type
		switch header.Typeflag {
		// if its a dir and it doesn't exist create it
		case tar.TypeDir:
			if _, err := os.Stat(target); err != nil {
				if err := os.MkdirAll(target, 0755); err != nil {
					return err
				}
			}

		// if it's a file create it
		case tar.TypeReg, 50:
			f, err := os.OpenFile(target, os.O_CREATE|os.O_RDWR, os.FileMode(header.Mode))
			if err != nil {
				return err
			}

			// copy over contents
			if _, err := io.Copy(f, tr); err != nil {
				return err
			}

			// manually close here after each file operation; defering would cause each file close
			// to wait until all operations have completed.
			f.Close()
		}
	}
}

// FindFileByExt 找出指定后缀的文件
// param root: 根目录
// param exit: 后缀名
// param exclude: 排除目录，支持通配符，例如 **/test_resources/**，多个以逗号分割
func FindFileByExt(root string, ext string, excludeDir string) ([]string, error) {
	files := make([]string, 0)
	var excludes []string
	if excludeDir != "" {
		excludes = strings.Split(excludeDir, ",")
	}
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断后缀是否一样
		if !info.IsDir() && filepath.Ext(path) == ext {
			var isValid = true
			for _, e := range excludes {
				if InSlice(strings.Split(path, string(filepath.Separator)), e) {
					isValid = false
				}
			}
			if isValid {
				files = append(files, path)
			}
		}
		return nil
	})
	return files, _err
}

// FindFileBySuffix 找出指定后缀的文件
// param root: 根目录
// param exit: 后缀名
// param exclude: 排除目录，支持通配符，例如 **/test_resources/**，多个以逗号分割
func FindFileBySuffix(root string, suffix string, excludeDir string) ([]string, error) {
	files := make([]string, 0)
	var excludes []string
	if excludeDir != "" {
		excludes = strings.Split(excludeDir, ",")
	}
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断后缀是否一样
		if !info.IsDir() && strings.HasSuffix(path, suffix) {
			var isValid = true
			for _, e := range excludes {
				if strings.Contains(path, e) {
					isValid = false
				}
			}
			if isValid {
				files = append(files, path)
			}
		}
		return nil
	})
	return files, _err
}
func FindDirBySuffix(root string, suffix string) []string {
	files := make([]string, 0)

	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断后缀是否一样
		if info.IsDir() && strings.HasSuffix(path, suffix) {
			files = append(files, path)
		}
		return nil
	})
	if _err != nil {
		return nil
	}
	return files
}

// FindFileByName 找出指定名字的文件
func FindFileByName(root string, name string) ([]string, error) {
	files := make([]string, 0)
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断后缀是否一样
		if !info.IsDir() && info.Name() == name {
			files = append(files, path)
		}
		return nil
	})
	return files, _err
}

// FindOneOuterFile 从一个路径数组中获取层级最浅的路径
func FindOneOuterFile(pList []string) string {
	depth := -1
	var result string
	if len(pList) == 0 {
		return ""
	} else if len(pList) == 1 {
		return pList[0]
	} else {
		for _, value := range pList {
			pathLen := len(strings.Split(value, string(os.PathSeparator)))
			if depth == -1 {
				depth = pathLen
				result = value
			} else if depth > pathLen {
				depth = pathLen
				result = value
			}
		}
	}
	return result
}

// FindFileByNamePattern 通过正则在查找文件
func FindFileByNamePattern(root string, pattern string, excludes []string) ([]string, error) {
	return findByNamePattern(root, pattern, excludes, "file")
}

// FindDirByNamePattern 通过正则在查找文件
func FindDirByNamePattern(root string, pattern string, excludes []string) ([]string, error) {
	return findByNamePattern(root, pattern, excludes, "dir")
}

func findByNamePattern(root string, pattern string, excludes []string, filetype string) ([]string, error) {
	files := make([]string, 0)
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断后缀是否一样
		if info.IsDir() {
			if InSlice(excludes, info.Name()) {
				return filepath.SkipDir
			}
			if subFiles, _ := filepath.Glob(filepath.Join(path, pattern)); subFiles != nil {
				for _, f := range subFiles {
					if filetype == "file" {
						if file.IsFile(f) {
							files = append(files, f)
						}
					} else if filetype == "dir" {
						if !file.IsFile(f) {
							files = append(files, f)
						}
					} else {
						files = append(files, f)
					}
				}
			}
		}
		return nil
	})
	return files, _err
}

// FindDirByName 找出指定子路径的文件路径
func FindDirByName(root string, name string) ([]string, error) {
	dirs := make([]string, 0)
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断后缀是否一样
		if info.IsDir() && strings.HasSuffix(path, name) {
			dirs = append(dirs, path)
		}
		return nil
	})
	return dirs, _err
}

type AntMatcher struct {
	Includes []string
	Excludes []string

	matcher *antpath.AntPathMatcher
}

func NewAntMatcher(includes []string, excludes []string) *AntMatcher {
	return &AntMatcher{Includes: includes, Excludes: excludes, matcher: antpath.New()}
}

func (antMatcher *AntMatcher) Match(path string) bool {
	path = handleAntMatcherPath(path)
	for _, ex := range antMatcher.Excludes {
		ex = handleAntMatcherPath(ex)
		if antMatcher.matcher.Match(ex, path) {
			return false
		}
	}

	if len(antMatcher.Includes) > 0 {
		for _, in := range antMatcher.Includes {
			in = handleAntMatcherPath(in)
			if antMatcher.matcher.Match(in, path) {
				return true
			}
		}

		return false
	}
	return true
}

func handleAntMatcherPath(path string) string {
	path = filepath.ToSlash(path)
	path = strconv.QuoteToASCII(path)
	path = strings.Trim(path, "\"") // unicode转义后，字符串首尾多出了双引号，导致无法正常匹配路径
	return path
}

func CollectFilesMatchAnt(root string, antMatcher AntMatcher) ([]string, error) {
	files := make([]string, 0)
	matcher := antpath.New()
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		for _, ex := range antMatcher.Excludes {
			if matcher.Match(ex, path) {
				if info.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}
		}
		// 判断是否为match路径
		if len(antMatcher.Includes) > 0 {
			for _, in := range antMatcher.Includes {
				if matcher.Match(in, path) {
					files = append(files, path)
					break
				}
			}
		}
		return nil
	})
	return files, _err
}

func GetAbsPath(workdir string, root string) string {
	if utils.IsAbsolutePath(workdir) {
		return workdir
	} else if root != "" {
		dir, _ := filepath.Abs(filepath.Join(root, workdir))
		return dir
	} else {
		dir, _ := filepath.Abs(workdir)
		return dir
	}
}

func FileMatchContent(filePath string, re *regexp.Regexp) (bool, string) {
	if fi, err := os.Open(filePath); err == nil {
		defer fi.Close()
		reader := bufio.NewReader(fi)
		for {
			line, err := reader.ReadBytes('\n')
			if err == io.EOF {
				return false, ""
			}
			if re.Match(line) {
				return true, string(line)
			}
		}
	}
	return false, ""
}

func IsValidHTMLDir(dir string) bool {
	if dir != "" && IsFileExist(filepath.Join(dir, "index.html")) {
		return true
	}
	return false
}

func FilterFilesByExt(files []string, ext string) []string {
	var result []string
	for _, f := range files {
		if filepath.Ext(f) != ext {
			continue
		}
		result = append(result, f)
	}
	return result
}

type FileDirType struct {
	FilePaths []string
	DirPaths  []string
}

func CollectFilesAndDirMatchAnt(root string, excludes []string, includes []string) (FileDirType, error) {
	fileDirType := FileDirType{}
	_err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if root == path {
			return nil
		}
		if info.IsDir() {
			fileDirType.DirPaths = append(fileDirType.DirPaths, path)
			return nil
		}
		for _, ex := range includes {
			if !strings.HasSuffix(path, ex) {
				return nil
			}
		}
		for _, ex := range excludes {
			if strings.HasSuffix(path, ex) {
				return nil
			}
		}

		fileDirType.FilePaths = append(fileDirType.FilePaths, path)
		return nil
	})
	return fileDirType, _err
}
func PrefixPath(filePath string) string {
	// 使用path包提取文件名
	_, file := path.Split(filePath)

	return filePath[:len(filePath)-len(file)]
}

func ReadFile(filePath string) string {
	if !IsFileExist(filePath) {
		return ""
	}
	// 读取配置文件，并映射成指定的TestData数据结构
	content, err := os.ReadFile(filePath)
	if err != nil {
		logger.Info("read file error", err)
		return ""
	}
	return string(content)
}

// WriteFileAppend 将数据追加到指定文件中
// filename: 文件名字符串类型，不能为空
// data: 待写入的字节切片类型，不能为空
func WriteFileAppend(filename string, data []byte) {
	// 定义文件名

	// 打开文件，使用O_APPEND | O_CREATE | O_WRONLY标志
	// O_APPEND 表示追加写入
	// O_CREATE 表示如果文件不存在则创建
	// O_WRONLY 表示只写模式
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {

	}
	defer file.Close() // 确保文件在使用后被关闭

	// 定义要写入的数据

	// 写入数据
	if _, err := file.Write(data); err != nil {

	}
}

func WriteContent(filename, content string) error {
	if IsFileExist(filename) {
		os.Remove(filename)
	}

	if err := os.MkdirAll(filepath.Dir(filename), os.ModePerm); err != nil {
		return err
	}

	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close() // 确保文件被关闭

	// 向文件写入一些文本
	_, err = file.WriteString(content)
	if err != nil {
		return err
	}

	return nil
}

func EmptyFile(filename string) {
	emptyData := []byte{}
	// 将空的字节切片写入文件，覆盖原有内容
	err := ioutil.WriteFile(filename, emptyData, 0644)
	if err != nil {
		fmt.Println("写入文件时出错:", err)
		return
	}

}

func DownloadFile(url string, dirPath string, fileName string) error {
	// 完整的目标文件路径
	targetPath := filepath.Join(dirPath, fileName)
	// DownloadFile 下载文件并保存到本地。
	// 获取已下载的文件大小（用于断点续传）
	var offset int64 = 0
	if fileInfo, err := os.Stat(targetPath); err == nil {
		offset = fileInfo.Size()
	}

	// 创建HTTP请求，支持断点续传
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 如果已下载部分内容，设置Range头
	if offset > 0 {
		req.Header.Set("Range", fmt.Sprintf("bytes=%d-", offset))
	}

	// 执行请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP响应状态
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusPartialContent {
		return fmt.Errorf("bad status: %s", resp.Status)
	}

	// 打开文件并追加内容
	out, err := os.OpenFile(targetPath, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer out.Close()

	// 移动到文件末尾（用于追加内容）
	if _, err = out.Seek(offset, io.SeekStart); err != nil {
		return fmt.Errorf("failed to seek file: %w", err)
	}

	// 将HTTP响应内容写入文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("failed to copy content: %w", err)
	}

	return nil

}

func SaveTmpFile(filename string, content string) {
	if !config.IsDebugEnv() {
		// 仅在调试模式下保存临时文件
		return
	}
	tmpFile := filepath.Join("tmp", filename)
	if !file.IsExist(filepath.Dir(tmpFile)) {
		os.MkdirAll(filepath.Dir(tmpFile), os.ModePerm)
	}
	f, _ := os.Create(tmpFile)
	writeString, err := f.WriteString(content)
	if err != nil {
		fmt.Print("write failed: ", writeString, err)
	}
	f.Close()
}

func RenameTmpFile(oldPath string, newPath string) {
	if !config.IsDebugEnv() {
		// 仅在调试模式下保存临时文件
		return
	}

	if file.IsExist(oldPath) {
		if file.IsExist(newPath) {
			os.Remove(newPath)
		}
		os.Rename(oldPath, newPath)
	}
}

func Unzip(src string, dest string) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	for _, f := range r.File {
		fpath := filepath.Join(dest, f.Name)

		// Check for ZipSlip vulnerability: https://snyk.io/research/zip-slip-vulnerability
		if !strings.HasPrefix(fpath, filepath.Clean(dest)+string(os.PathSeparator)) {
			return fmt.Errorf("illegal file path: %s", fpath)
		}

		if f.FileInfo().IsDir() {
			// Make Folder
			os.MkdirAll(fpath, os.ModePerm)
			continue
		}

		// Make File
		if err = os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
			return err
		}

		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return err
		}
		defer outFile.Close()

		rc, err := f.Open()
		if err != nil {
			return err
		}

		_, err = io.Copy(outFile, rc)

		// Close the file without defer to ensure it's closed after each iteration
		outFile.Close()
		rc.Close()

		if err != nil {
			return err
		}
	}
	return nil
}

// IsValidSourceFile 判断给定的文件路径是否是一个有效的源代码文件。
func IsValidSourceFile(filePath string, language string) bool {
	switch language {
	case constant.LangJava:
		return strings.HasSuffix(filePath, ".java") && !strings.Contains(filePath, filepath.Join("src", "test", "java"))
	case constant.LangGo:
		return strings.HasSuffix(filePath, ".go") && !strings.HasSuffix(filePath, "_test.go")
	default:
		return false
	}
}
