package helper

import (
	"encoding/json"
	"fmt"
	"reflect"
)

func DeepCopy(src, dst interface{}) error {
	srcVal := reflect.ValueOf(src).Elem()
	dstVal := reflect.ValueOf(dst).Elem()
	if srcVal.Type() != dstVal.Type() {
		return fmt.Errorf("deep copy: type mismatch, source: %T, destination: %T", src, dst)
	}
	for i := 0; i < srcVal.NumField(); i++ {
		srcField := srcVal.Field(i)
		dstField := dstVal.Field(i)
		dstField.Set(srcField)
	}
	return nil
}

func MarshalToObject(data any, object any) error {
	resContent, err := json.Marshal(data)
	if err != nil {
		return err
	}
	err = json.Unmarshal(resContent, object)
	if err != nil {
		return err
	}

	return nil
}
