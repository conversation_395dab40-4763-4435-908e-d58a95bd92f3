package helper

func Pop[T string | struct{}](list []T) (T, []T) {
	listLen := len(list)
	if listLen == 0 {
		//
		panic("cannot pop from empty list!")
	} else {
		return list[listLen-1], list[0 : listLen-1]
	}
}

func InSlice[T string | int](slice []T, elem T) bool {
	for _, e := range slice {
		//
		if e == elem {
			return true
		}
	}
	return false
}

func Intersect[T comparable](slice1, slice2 []T) []T {
	intersection := make([]T, 0)
	if len(slice1) == 0 || len(slice2) == 0 {
		return intersection
	}

	// 使用 map 记录第一个 slice 中的元素
	elementMap := make(map[T]bool)
	for _, v := range slice1 {
		elementMap[v] = true
	}

	// 查找在第二个 slice 中也存在的元素
	for _, v := range slice2 {
		if elementMap[v] {
			intersection = append(intersection, v)
			// 可选：如果每个元素只应出现一次，在找到匹配项后删除map中的条目
			delete(elementMap, v)
		}
	}

	return intersection
}

// Union returns the union of two slices without duplicates.
// It supports any comparable type.
func Union[T comparable](a, b []T) []T {
	m := make(map[T]struct{}) // 使用空结构体作为值，节省内存
	var union []T

	// Add elements from slice a to the map
	for _, item := range a {
		if _, ok := m[item]; !ok {
			m[item] = struct{}{}
			union = append(union, item)
		}
	}

	// Add elements from slice b to the map, skipping duplicates
	for _, item := range b {
		if _, ok := m[item]; !ok {
			m[item] = struct{}{}
			union = append(union, item)
		}
	}

	return union
}

func GenerateSlice(n int) []int {
	result := make([]int, n)
	for i := 0; i < n; i++ {
		result[i] = i + 1
	}
	return result
}

//func Shuffle[T any](list []T) {
//	rand.Seed(time.Now().UnixNano())
//	for i := len(list) - 1; i > 0; i-- {
//		j := rand.Intn(i + 1)
//		list[i], list[j] = list[j], list[i]
//	}
//}
