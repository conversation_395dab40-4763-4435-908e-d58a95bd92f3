package helper

import "testing"

func TestGetRelativePath(t *testing.T) {
	testCases := []struct {
		name     string
		abspath  string
		gitPath  string
		expected string
	}{
		{
			name:     "Basic test case",
			abspath:  "/home/<USER>/project/main.go",
			gitPath:  "/home/<USER>/project/",
			expected: "main.go",
		},
		{
			name:     "Test with no common prefix",
			abspath:  "/home/<USER>/project/main.go",
			gitPath:  "/home/<USER>/other/",
			expected: "/home/<USER>/project/main.go",
		},
		{
			name:     "Test with no common prefix and different directory ending",
			abspath:  "/home/<USER>/project/src/main.go",
			gitPath:  "/home/<USER>/project/src/",
			expected: "main.go",
		},
		{
			name:     "Test with no common prefix and no directory ending",
			abspath:  "/home/<USER>/project/src/main.go",
			gitPath:  "/home/<USER>/project/src",
			expected: "main.go",
		},
		{
			name:     "Test with no common prefix and no directory ending and different file",
			abspath:  "/home/<USER>/project/src/main.go",
			gitPath:  "/home/<USER>/project/main.go",
			expected: "/home/<USER>/project/src/main.go",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			relativePath := GetRelativePath(tc.abspath, tc.gitPath)
			if relativePath != tc.expected {
				t.Errorf("Got unexpected result: %s, expected: %s", relativePath, tc.expected)
			}
		})
	}
}
