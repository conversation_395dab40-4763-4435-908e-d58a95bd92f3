package httputil

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/logger"
)

type Option func(option *Options)

// Options http 请求的配置项
type Options struct {
	Timeout    time.Duration     // 超时时间
	RetryCount int               // 最大重试次数
	Headers    map[string]string // 请求头
}

func WithTimeout(timeout time.Duration) Option {
	return func(option *Options) {
		option.Timeout = timeout
	}
}

func WithRetryCount(retryCount int) Option {
	return func(option *Options) {
		option.RetryCount = retryCount
	}
}

func WithHeader(key string, value string) Option {
	return func(option *Options) {
		if option.Headers == nil {
			option.Headers = make(map[string]string)
		}
		option.Headers[key] = value
	}
}

// 将多个 Option 转换成最终的 Options 对象
func makeHTTPOptions(req *http.Request, options ...Option) *Options {
	option := Options{}
	for _, opt := range options {
		opt(&option)
	}
	// 默认值
	if option.RetryCount == 0 {
		option.RetryCount = 1
	}
	if option.Timeout == 0 {
		option.Timeout = time.Second * 60
	}

	if option.Headers != nil {
		for k, v := range option.Headers {
			req.Header.Set(k, v)
		}
	}
	return &option
}

func HTTPGet(url string, params map[string]interface{}, optionList ...Option) (map[string]interface{}, error) {
	req, _ := http.NewRequest("GET", url, nil)
	q := req.URL.Query()
	for k, v := range params {
		q.Add(k, v.(string))
	}
	req.URL.RawQuery = q.Encode()
	options := makeHTTPOptions(req, optionList...)

	return doRequest(req, options.Timeout, options.RetryCount)
}

func Get(mURL string) (any, error) {
	resp, err := http.Get(mURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	return reflectToRealType(resp)
}

func reflectToRealType(resp *http.Response) (any, error) {
	body, err := io.ReadAll(resp.Body)

	if err != nil {
		return nil, err
	} else if strings.HasPrefix(string(body), "{") {
		var result map[string]any
		err = json.Unmarshal(body, &result)
		return result, err
	} else if strings.HasPrefix(string(body), "[") {
		var result []string
		err = json.Unmarshal(body, &result)
		return result, err
	} else if string(body) == "true" {
		return true, nil
	} else if string(body) == "false" {
		return false, nil
	}
	return string(body), nil

}

// HTTPPostBody post body，json格式
func HTTPPostBody(URL string, body any, optionList ...Option) (map[string]any, error) {
	var err error
	b, _ := json.Marshal(body)
	// log.Println(string(b))
	req, err := http.NewRequest("POST", URL, bytes.NewReader(b))
	if err != nil {
		return nil, err
	}

	options := makeHTTPOptions(req, optionList...)

	resp, err := doRequest(req, options.Timeout, options.RetryCount)

	return resp, err
}

func PostForm(url string, params map[string]any, optionList ...Option) (map[string]any, error) {
	var body string
	for k, v := range params {
		body += fmt.Sprintf("&%s=%v", k, v)
	}
	req, err := http.NewRequest("POST", url, strings.NewReader(body[1:]))
	options := makeHTTPOptions(req, optionList...)

	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := doRequest(req, options.Timeout, options.RetryCount)
	return resp, err
}

// doRequest 发起HTTP请求，并返回响应结果和错误信息
//
// req: 待发送的HTTP请求
// timeout: 请求的超时时间
// retryTimes: 最大重试次数
//
// 返回值:
// map[string]any: 解析后的响应体数据
// error: 请求过程中产生的错误
func doRequest(req *http.Request, timeout time.Duration, retryTimes int) (map[string]any, error) {

	var httpDo = func() (map[string]any, error) {
		client := &http.Client{
			Timeout: timeout,
		}
		resp, err := client.Do(req)
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		if resp.StatusCode != 200 && resp.StatusCode != 201 {
			errorMsg := fmt.Sprintf("resp code %d, response body: %s", resp.StatusCode, respBody)
			return nil, errors.New(errorMsg)
		}
		var result map[string]any
		err = json.Unmarshal(respBody, &result)

		return result, err
	}

	resp, err := httpDo()
	if err == nil {
		// 请求成功，直接返回
		return resp, nil
	}

	// 请求失败，重试
	if retryTimes > 0 {
		logger.Debug("request failed: %v, start retrying...", err)
		for retry := 1; ; retry++ {
			if retry == retryTimes+1 {
				logger.Debug("request failed: %v，max retries [%d] reached", err, retryTimes)
				return nil, err
			}
			resp, err = httpDo()
			if err != nil {
				logger.Debug("request failed: %v，retrying %d...", err, retry)
				time.Sleep(3 * time.Second)
				continue
			}

			return resp, nil
		}
	}

	return nil, err

}
