package httputil

import (
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
)

func TestHTTPPostBody(t *testing.T) {
	url := "http://10.11.58.93:8080/generate"
	utfunc := "func Test_genECDataAndAccept(t *testing.T) {\n    type args struct {\n        path  string\n        fileLang string\n        functionTag *astree.SrcFunctionTag\n        param  *params.GenParam\n    }\n    tests := []struct {\n        name string\n        args args\n        want *helper.GenerateUTResponseData\n    }{\n        {\n            name: \"normal\",\n            args: args{\n                path:  \"./testdata/test.ut\",\n                fileLang: \"go\",\n                functionTag: &astree.SrcFunctionTag{},\n                param:  &params.GenParam{},\n            },\n            want: &helper.GenerateUTResponseData{\n                Data: []map[string]any{\n                    {\n                        \"method\":   \"genECDataAndAccept\",\n                        \"path\":     \"./testdata/test.ut\",\n                        \"repo\":     \"baidu/smart-iut\",\n                        \"row\":      13,\n                        \"col\":      1,\n                        \"ide\":      \"SMARTUT_IUT\",\n                        \"model\":    \"SMARTUT_IUT\",\n                        \"generatedContent\": \"func genECDataAndAccept(path string, fileLang string, functionTag *astree.SrcFunctionTag, param *params.GenParam) (*helper.GenerateUTResponseData, error) {\\n\treturn nil, nil\\n}\",\n                        \"originGeneratedContent\": \"func genECDataAndAccept(path string, fileLang string, functionTag *astree.SrcFunctionTag, param *params.GenParam) (*helper.GenerateUTResponseData, error) {\\n\treturn nil, nil\\n}\",\n                        \"multiline\":              true,\n                        \"accepted\":               true,\n                    },\n                },\n                Status: \"OK\",\n            },\n        },\n    }\n    for _, tt := range tests {\n        t.Run(tt.name, func(t *testing.T) {\n            got, _ := genECDataAndAccept(tt.args.path, tt.args.fileLang, tt.args.functionTag, tt.args.param)\n            if!reflect.DeepEqual(got, tt.want) {\n                t.Errorf(\"genECDataAndAccept() = %v, want %v\", got, tt.want)\n            }\n        })\n    }\n}\n"
	// utfunc = url.QueryEscape(utfunc)
	generate := map[string]any{
		"path":                   "file.go",
		"repo":                   "baidu/cov/inc",
		"username":               "tiantian19",
		"content":                "if header != nil {", // 被测函数函数体
		"row":                    13,
		"col":                    1,
		"ide":                    "SMARTUT_IUT",
		"model":                  "SMARTUT_IUT", // 分开采纳
		"generatedContent":       utfunc,
		"originGeneratedContent": "if header != nil {",
		"multiline":              true,
		"accepted":               true,
	}
	type args struct {
		URL        string
		header     map[string]string
		body       map[string]any
		retry      int
		err        error
		errMessage string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]any
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				URL:        url,
				header:     nil,
				body:       generate,
				retry:      0,
				err:        nil,
				errMessage: "",
			},
			want:    map[string]any{"a": 1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			PostForm(tt.args.URL, tt.args.body)
			//if err != nil {
			//	t.Errorf("HTTPPostBody() error = %v, wantErr %v", err, tt.wantErr)
			//	return
			//}
			// if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("HTTPPostBody() got = %v, want %v", got, tt.want)
			// }
		})
	}
}

// TestHttpGet 是用于测试 HTTPGet
// generated by Comate
func TestHttpGet(t *testing.T) {
	type args struct {
		url    string
		params map[string]interface{}
	}
	tests := []struct {
		name     string
		args     args
		wantResp map[string]interface{}
		wantErr  bool
	}{
		{
			name: "test1",
			args: args{
				url: "http://127.0.0.1:56834/api/v1/user/login",
			},
			wantResp: nil,
			wantErr:  true,
		},
		{
			name: "test2",
			args: args{
				url: "http://127.0.0.1:56834/api/v1/user/login",
				params: map[string]interface{}{
					"username": "admin",
					"password": "admin",
				},
			},
			wantResp: nil,
			wantErr:  true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotResp, err := HTTPGet(tt.args.url, tt.args.params, WithRetryCount(1))
			if (err != nil) != tt.wantErr {
				t.Errorf("HTTPGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("HTTPGet() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}

// NewMockServer 是用于测试 Get
// generated by Comate
func NewMockServer(response string) *http.Server {
	server := &http.Server{
		Addr: ":8080",
	}
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte(response))
	})
	go func() {
		server.ListenAndServe()
	}()
	return server
}

// TestHttpPostForm 是用于测试 PostForm
// generated by Comate
func TestHttpPostForm(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/post" {
			t.Errorf("Expected request to '/post', got '%s'", r.URL.Path)
		}
		err := r.ParseForm()
		if err != nil {
			t.Fatal(err)
		}
		want := map[string]any{"a": "1", "b": "2", "c": "3"}
		got := make(map[string]any)
		for k, v := range r.PostForm {
			got[k] = v[0]
		}
		if !reflect.DeepEqual(got, want) {
			t.Errorf("expected post form %v, got %v", want, got)
			w.WriteHeader(http.StatusInternalServerError)
		} else {
			w.WriteHeader(http.StatusOK)
		}
	}))
	defer ts.Close()
	_, err := PostForm(ts.URL+"/post", map[string]any{"a": 1, "b": 2, "c": 3})
	if err != nil {
		t.Errorf("should not return error")
	}
}
