package httputil

import (
	"testing"
	"time"
)

func TestIsIPReachable(t *testing.T) {
	tests := []struct {
		name      string
		ip        string
		port      string
		timeout   time.Duration
		wantErr   bool
		wantReach bool
	}{
		{
			name:      "reachable",
			ip:        "*************",
			port:      "8500",
			timeout:   time.Second,
			wantErr:   false,
			wantReach: true,
		},
		{
			name:      "unreachable",
			ip:        "127.0.0.1",
			port:      "99999",
			timeout:   time.Second,
			wantErr:   true,
			wantReach: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotReach := IsIPReachable(tt.ip, tt.port, tt.timeout)
			if gotReach != tt.wantReach {
				t.Errorf("IsIPReachable() = %v, want %v", gotReach, tt.wantReach)
			}

			if (gotReach && tt.wantErr) || (!gotReach && !tt.wantErr) {
				t.<PERSON>rrorf("IsIPReachable() = %v, wantErr %v", gotReach, tt.wantErr)
			}
		})
	}
}
