package httputil

import (
	"fmt"
	"net"
	"strings"
	"time"
)

// IsIPReachable 判断指定IP地址和端口是否可达
//
// 参数：
// ip：string类型，待检测的IP地址
// port：string类型，待检测的端口号
// timeout：time.Duration类型，连接超时时间
//
// 返回值：
// bool类型，若连接成功，返回true；否则返回false
func IsIPReachable(ip string, port string, timeout time.Duration) bool {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%s", ip, port), timeout)
	if err != nil {
		// 如果连接失败，认为IP不可达
		return false
	}
	// 关闭连接
	conn.Close()
	return true
}

func IsBaiduInnerIp() bool {
	response, err := Get("http://************:8200/api/health")
	if err == nil && strings.Contains(response.(string), "success") {
		return true
	}
	return false
}

//func IsBaiduInnerIp() bool {
//	ip, err := getLocalIP()
//	if err != nil {
//		return false
//	}
//	params := map[string]interface{}{
//		"ip": ip,
//	}
//	appName := "comate_ut"
//	timeStamp := fmt.Sprint(time.Now().UnixMilli())
//	token := "e9b9965f7c8ded4050e28c11e1398420"
//	hash := md5.Sum([]byte(appName + timeStamp + token))
//	authorization := hex.EncodeToString(hash[:])
//	// 判断是否百度内网ip：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/9IaD11zSiz/C6O_YjHF9M/N_D2F5NiEXDB8n
//	ipCheckRes, err := httputil.HTTPGet(
//		"http://api.nsc.baidu.com/dunet/ip/isintranet",
//		params,
//		httputil.WithHeader("Content-Type", "application/json"),
//		httputil.WithHeader("Authorization", authorization),
//		httputil.WithHeader("app-name", appName),
//		httputil.WithHeader("time-stamp", timeStamp),
//	)
//	if err == nil {
//		if ipCheckRes["status"].(float64) == 0 {
//			message := ipCheckRes["message"].(map[string]interface{})
//			if message["isIntranet"].(float64) == 1 {
//				return true
//			}
//		}
//	}
//	return false
//}

func getLocalIP() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}
	for _, i := range interfaces {
		addrs, err := i.Addrs()
		if err != nil {
			return "", err
		}
		for _, addr := range addrs {
			if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
				if ipNet.IP.To4() != nil {
					return ipNet.IP.String(), nil
				}
			}
		}
	}
	return "", fmt.Errorf("no IPv4 address found")
}
