package helper

import (
	"fmt"
	"time"
)

type Timer struct {
	start    int64
	end      int64
	duration int64 // 累计时间
}

func NewTimer() *Timer {
	return &Timer{
		start: time.Now().UnixMilli(),
	}
}

func (t *Timer) Start() {
	t.start = time.Now().UnixMilli()
}

func (t *Timer) Pause() {
	t.end = time.Now().UnixMilli()
	t.duration += t.end - t.start
}

func (t *Timer) Stop() {
	t.end = time.Now().UnixMilli()
}

func (t *Timer) Reset() {
	t.start = time.Now().UnixMilli()
	t.end = 0
}

func (t *Timer) Elapsed() int64 {
	if t.end == 0 {
		t.Stop()
	}
	return t.end - t.start
}

func (t *Timer) ElapsedSecond() string {
	return fmt.Sprintf("%.02fs", float64(t.Elapsed())/1000)
}

func (t *Timer) Duration() int64 {
	return t.duration
}

// FormatMS 函数接收一个 int64 类型的毫秒数作为参数，返回格式化为字符串的时间表示。
//
//		如果小时数大于0，则返回格式为 "XhYmZs" 的字符串，其中 X 表示小时数，Y 表示分钟数（不包含小时部分），Z 表示秒数（不包含分钟部分）。
//		如果小时数为0且分钟数大于0，则返回格式为 "YmZs" 的字符串，其中 Y 表示分钟数，Z 表示秒数（不包含分钟部分）。
//	 	如果小时数和分钟数均为0，则返回格式为 "X.XXfs" 的字符串，其中 X.XX 表示毫秒数除以1000得到的浮点秒数，保留两位小数。
func FormatMS(ms int64) string {
	s := ms / 1000
	m := s / 60
	h := m / 60
	if h > 0 {
		return fmt.Sprintf("%dh%dmin%ds", h, m-h*60, s-m*60)
	} else if m > 0 {
		return fmt.Sprintf("%dmin%ds", m-h*60, s-m*60)
	} else {
		return fmt.Sprintf("%0.002fs", float64(ms)/1000)
	}
}
