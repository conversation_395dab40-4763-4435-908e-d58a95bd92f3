package helper

import (
	"crypto/sha256"
	"fmt"
	"path/filepath"
	"strings"
	"unicode"

	"github.com/vibrantbyte/go-antpath/antpath"
)

func CapitalizeFirstLetter(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(s[0:1]) + s[1:]
}

func JavaInterface(fileName string) bool {
	content := ReadFile(fileName)
	if content == "" {
		return false
	}
	flag := false
	for _, line := range StringSplit(content, "\n") {
		if strings.Contains(line, "public") {
			flag = true
		}
		if !flag {
			continue
		}
		if strings.Contains(line, "interface") || strings.Contains(line, "public interface") {
			return true
		}
		if strings.Contains(line, "{") {
			break
		}
	}
	return false
}
func StringAntMatch(str string, antMatcher *AntMatcher) bool {
	matcher := antpath.New()
	for _, ex := range antMatcher.Excludes {
		if matcher.Match(ex, str) {
			return false
		}
	}
	// 判断是否为match路径
	if len(antMatcher.Includes) > 0 {
		for _, in := range antMatcher.Includes {
			if matcher.Match(in, str) {
				return true
			}
		}
		return false
	}
	return true
}

func StringSplit(str string, sep string) []string {
	result := make([]string, 0)
	for _, s := range strings.Split(str, sep) {
		s = strings.TrimSpace(s)
		if s != "" {
			result = append(result, s)
		}
	}

	return result
}

func Strip(content string) string {
	content = strings.TrimSuffix(strings.TrimSuffix(content, "\n"), " ")
	return strings.TrimPrefix(content, " ")
}

func StringJoin(elems []string, sep string) string {
	validElems := make([]string, 0)
	for _, elem := range elems {
		e := strings.TrimSuffix(strings.TrimSpace(elem), sep)
		if e != "" {
			validElems = append(validElems, e)
		}
	}

	if len(validElems) > 0 {
		return strings.Join(validElems, sep)
	}
	return ""
}

func RemovePrefix(s, prefix string) string {
	// 首先检查字符串是否以指定的前缀开头
	if strings.HasPrefix(s, prefix) {
		// 如果是，则通过切片去掉前缀
		// len(prefix) 是前缀的长度，用于确定切片的起始位置
		return s[len(prefix):]
	}
	// 如果不是以指定的前缀开头，则原样返回字符串
	return s
}

func HashString(s string) string {
	h := sha256.New()
	h.Write([]byte(s))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func FindLongestCommonPrefix(inputStr string, strs []string) string {
	if len(strs) == 0 {
		return "" // 或者根据需求返回nil（但在Go中字符串通常以空字符串表示无值）
	}
	inputStr = filepath.Clean(inputStr)

	var longestMatch, longestPrefix string
	for _, str := range strs {
		str = filepath.Clean(str)
		// 逐字符比较以找到公共前缀
		i := 0
		for i < len(inputStr) && i < len(str) && inputStr[i] == str[i] {
			i++
		}

		if i > len(longestPrefix) {
			longestPrefix = str[:i]
			longestMatch = str
		}
	}
	return longestMatch
}

// StrLen 计算字符串长度
func StrLen(str string) int {
	return len([]rune(str))
}

// StrLenSum 计算两个字符串长度之和
func StrLenSum(str1, str2 string) int {
	return len([]rune(str1)) + len([]rune(str2))
}

// 检查字符串中是否包含大写字母
func IsContainUpperCase(str string) bool {
	for _, v := range str {
		if unicode.IsUpper(v) {
			return true
		}
	}
	return false
}

// ToCamelCase 将带下划线的字符串转换为驼峰格式
func ToCamelCase(input string) string {
	parts := strings.Split(input, "_")
	if len(parts) == 1 {
		return input
	}
	for i := range parts {
		if i == 0 {
			// 首个单词首字母小写，保持一致性（小驼峰）
			continue
		} else {
			// 其他单词首字母大写
			parts[i] = strings.Title(parts[i])
		}
	}
	return strings.Join(parts, "")
}
