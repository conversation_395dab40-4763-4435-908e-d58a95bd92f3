package helper

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTimer_Elapsed(t *testing.T) {
	type fields struct {
		start    int64
		end      int64
		duration int64 // 累计时间
	}
	tests := []struct {
		name   string
		fields fields
		want   int64
	}{
		{
			name: "test1",
			fields: fields{
				start: 100,
				end:   200,
			},
			want: 100,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ti := &Timer{
				start:    tt.fields.start,
				end:      tt.fields.end,
				duration: tt.fields.duration,
			}
			fmt.Printf("%0.2f\n", float64(ti.Elapsed())/1000)
			assert.Equalf(t, tt.want, ti.Elapsed(), "Elapsed()")
		})
	}
}

func TestFormatMS(t *testing.T) {
	ss := FormatMS(18356)
	fmt.Println(ss)

	ss1 := FormatMS(100000)
	fmt.Println(ss1)

	ss2 := FormatMS(2 * 60 * 60 * 1000)
	fmt.Println(ss2)
}
