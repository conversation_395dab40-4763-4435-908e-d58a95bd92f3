package helper

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestCapitalizeFirstLetter 是用于测试 CapitalizeFirstLetter
// generated by Comate
func TestCapitalizeFirstLetter(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Input is empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Input is a single word",
			input:    "hello",
			expected: "Hello",
		},
		{
			name:     "Input is already capitalized",
			input:    "Hello",
			expected: "Hello",
		},
		{
			name:     "Input is a sentence",
			input:    "this is a test",
			expected: "This is a test",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := CapitalizeFirstLetter(tc.input)
			assert.Equal(t, tc.expected, actual)
		})
	}
}

// TestStringAntMatch 是用于测试 StringAntMatch
// generated by Comate
func TestStringAntMatch(t *testing.T) {
	tests := []struct {
		name       string
		str        string
		antMatcher *AntMatcher
		want       bool
	}{
		{
			name:       "empty includes and excludes",
			str:        "abc",
			antMatcher: &AntMatcher{},
			want:       true,
		},
		{
			name: "exclude matches",
			str:  "abc",
			antMatcher: &AntMatcher{
				Excludes: []string{"abc"},
			},
			want: false,
		},
		{
			name: "exclude matches with other includes",
			str:  "abc",
			antMatcher: &AntMatcher{
				Excludes: []string{"abc"},
				Includes: []string{"def", "ghi"},
			},
			want: false,
		},
		{
			name: "exclude does not match",
			str:  "abc",
			antMatcher: &AntMatcher{
				Excludes: []string{"def"},
			},
			want: true,
		},
		{
			name: "include matches",
			str:  "abc",
			antMatcher: &AntMatcher{
				Includes: []string{"abc"},
			},
			want: true,
		},
		{
			name: "include matches with other excludes",
			str:  "abc",
			antMatcher: &AntMatcher{
				Excludes: []string{"def", "ghi"},
				Includes: []string{"abc"},
			},
			want: true,
		},
		{
			name: "include does not match",
			str:  "abc",
			antMatcher: &AntMatcher{
				Includes: []string{"def"},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StringAntMatch(tt.str, tt.antMatcher); got != tt.want {
				t.Errorf("StringAntMatch() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestStringSplit 是用于测试 StringSplit
// generated by Comate
func TestStringSplit(t *testing.T) {
	tests := []struct {
		name string
		str  string
		sep  string
		want []string
	}{
		{
			name: "Empty string and empty separator",
			str:  "",
			sep:  "",
			want: nil,
		},
		{
			name: "Empty string",
			str:  "",
			sep:  ",",
			want: nil,
		},
		{
			name: "Empty separator",
			str:  "hello,world",
			sep:  "",
			want: []string{"hello", "world"},
		},
		{
			name: "Whitespace separator",
			str:  "hello  world",
			sep:  " ",
			want: []string{"hello", "world"},
		},
		{
			name: "Multiple whitespace separator",
			str:  "hello  world  test",
			sep:  " ",
			want: []string{"hello", "world", "test"},
		},
		{
			name: "Mixed separator",
			str:  "hello, world.test",
			sep:  ",.",
			want: []string{"hello", "world", "test"},
		},
		{
			name: "Empty segments",
			str:  "hello,,world,",
			sep:  ",",
			want: []string{"hello", "world"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := StringSplit(tt.str, tt.sep)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StringSplit(%q, %q) = %v, want %v", tt.str, tt.sep, got, tt.want)
			}
		})
	}
}

// TestStrip 是用于测试 Strip
// generated by Comate
func TestStrip(t *testing.T) {
	testCases := []struct {
		input  string
		output string
	}{
		{"hello", "hello"},
		{" hello", "hello"},
		{"hello ", "hello"},
		{" hello ", "hello"},
		{"  hello  ", "hello"},
		{" \n hello \n ", "hello"},
	}
	for _, tc := range testCases {
		output := Strip(tc.input)
		if output != tc.output {
			t.Errorf("Strip(%q) = %q, want %q", tc.input, output, tc.output)
		}
	}
}

// TestStringJoin 是用于测试 StringJoin
// generated by Comate
func TestStringJoin(t *testing.T) {
	testCases := []struct {
		name     string
		elems    []string
		sep      string
		expected string
	}{
		{
			name:     "nil slice",
			elems:    nil,
			sep:      ",",
			expected: "",
		},
		{
			name:     "empty slice",
			elems:    []string{},
			sep:      ",",
			expected: "",
		},
		{
			name:     "one element",
			elems:    []string{"hello"},
			sep:      ",",
			expected: "hello",
		},
		{
			name:     "multiple elements",
			elems:    []string{"hello", "world", "foo", "bar"},
			sep:      ",",
			expected: "hello,world,foo,bar",
		},
		{
			name:     "trailing separator",
			elems:    []string{"hello", "world", "foo", "bar", ""},
			sep:      ",",
			expected: "hello,world,foo,bar",
		},
		{
			name:     "empty and whitespace elements",
			elems:    []string{"hello", "", "   ", "world", "foo", "bar"},
			sep:      ",",
			expected: "hello,world,foo,bar",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := StringJoin(tc.elems, tc.sep)
			if actual != tc.expected {
				t.Errorf("StringJoin(%v, %s) = %s, want %s", tc.elems, tc.sep, actual, tc.expected)
			}
		})
	}
}

// TestRemovePrefix 是用于测试 RemovePrefix
// generated by Comate
func TestRemovePrefix(t *testing.T) {
	tests := []struct {
		name   string
		s      string
		prefix string
		want   string
	}{
		{
			name:   "empty strings",
			s:      "",
			prefix: "",
			want:   "",
		},
		{
			name:   "not has prefix",
			s:      "abcdef",
			prefix: "xyz",
			want:   "abcdef",
		},
		{
			name:   "has prefix",
			s:      "abcdef",
			prefix: "abcde",
			want:   "f",
		},
		{
			name:   "has prefix with one char",
			s:      "abcdef",
			prefix: "a",
			want:   "bcdef",
		},
		{
			name:   "has prefix with multiple chars",
			s:      "abcdef",
			prefix: "abc",
			want:   "def",
		},
		{
			name:   "has prefix with multiple chars not aligned",
			s:      "abcdef",
			prefix: "bcdf",
			want:   "abcdef",
		},
		{
			name:   "has prefix with multiple chars of a very long prefix",
			s:      "abcdef",
			prefix: "12345678901234567890123456789012345678901234567890",
			want:   "abcdef",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := RemovePrefix(tt.s, tt.prefix)
			if got != tt.want {
				t.Errorf("RemovePrefix(%q, %q) = %q, want %q", tt.s, tt.prefix, got, tt.want)
			}
		})
	}
}

// TestFindLongestCommonPrefix 是用于测试 FindLongestCommonPrefix
// generated by Comate
func TestFindLongestCommonPrefix(t *testing.T) {
	testCases := []struct {
		inputStr string
		strs     []string
		want     string
	}{
		{"", []string{}, ""},
		{"/home/<USER>", []string{"/home/<USER>", "/home/<USER>/folder"}, "/home/<USER>"},
		{"/home/<USER>", []string{"/home/<USER>", "/home/<USER>/folder"}, "/home/"},
		{"/home/<USER>/", []string{"/home/<USER>", "/home/<USER>/folder"}, "/home/<USER>"},
	}
	for _, tc := range testCases {
		got := FindLongestCommonPrefix(tc.inputStr, tc.strs)
		if got != tc.want {
			t.Errorf("FindLongestCommonPrefix(%q, %v) = %q; want %q", tc.inputStr, tc.strs, got, tc.want)
		}
	}
}

// TestFindLongestCommonPrefixWithEmptyStrings 是用于测试 FindLongestCommonPrefix
// generated by Comate
func TestFindLongestCommonPrefixWithEmptyStrings(t *testing.T) {
	testCases := []struct {
		inputStr string
		strs     []string
		want     string
	}{
		{"/home/<USER>", []string{"", ""}, ""},
		{"/home/<USER>", []string{"/home/<USER>", ""}, "/home/<USER>"},
		{"/home/<USER>", []string{"", "/home/<USER>"}, "/home/<USER>"},
		{"/home/<USER>", []string{"/home/<USER>", "/home/<USER>/folder", ""}, "/home/<USER>"},
	}
	for _, tc := range testCases {
		got := FindLongestCommonPrefix(tc.inputStr, tc.strs)
		if got != tc.want {
			t.Errorf("FindLongestCommonPrefix(%q, %v) = %q; want %q", tc.inputStr, tc.strs, got, tc.want)
		}
	}
}

// TestFindLongestCommonPrefixWithEmptyArray 是用于测试 FindLongestCommonPrefix
// generated by Comate
func TestFindLongestCommonPrefixWithEmptyArray(t *testing.T) {
	got := FindLongestCommonPrefix("/home/<USER>", []string{})
	if got != "" {
		t.Errorf("FindLongestCommonPrefix('/home/<USER>', []) = %q; want empty string", got)
	}
}

// TestFindLongestCommonPrefixWithInvalidInput 是用于测试 FindLongestCommonPrefix
// generated by Comate
func TestFindLongestCommonPrefixWithInvalidInput(t *testing.T) {
	got := FindLongestCommonPrefix("C:\\path\\to\\file", []string{"C:\\path\\to\\file1", "C:\\path\\to\\file2"})
	if got != "C:\\path\\to\\file" {
		t.Errorf("FindLongestCommonPrefix('C:\\path\\to\\file', ...) = %q; want 'C:\\path\\to\\file'", got)
	}
}

// TestFindLongestCommonPrefixWithNil 是用于测试 FindLongestCommonPrefix
// generated by Comate
func TestFindLongestCommonPrefixWithNil(t *testing.T) {
	got := FindLongestCommonPrefix("/home/<USER>", nil)
	if got != "" {
		t.Errorf("FindLongestCommonPrefix('/home/<USER>', nil) = %q; want empty string", got)
	}
}

// TestStrLen 是用于测试 StrLen
// generated by Comate
func TestStrLen(t *testing.T) {
	tests := []struct {
		name    string
		str     string
		want    int
		wantErr bool
	}{
		{
			name:    "empty string",
			str:     "",
			want:    0,
			wantErr: false,
		},
		{
			name:    "non-empty string",
			str:     "Hello, World!",
			want:    13,
			wantErr: false,
		},
		{
			name:    "special characters",
			str:     "你好，世界！",
			want:    6,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := StrLen(tt.str)
			if got != tt.want {
				t.Errorf("StrLen() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestIsContainUpperCase 是用于测试 IsContainUpperCase
// generated by Comate
func TestIsContainUpperCase(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "字符串包含大写字母",
			input:    "Hello",
			expected: true,
		},
		{
			name:     "字符串不包含大写字母",
			input:    "hello",
			expected: false,
		},
		{
			name:     "字符串全为大写字母",
			input:    "HELLO",
			expected: true,
		},
		{
			name:     "字符串全为小写字母",
			input:    "hello",
			expected: false,
		},
		{
			name:     "字符串为空",
			input:    "",
			expected: false,
		},
		{
			name:     "字符串仅包含特殊字符",
			input:    "!@#$%^&*()",
			expected: false,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := IsContainUpperCase(tc.input)
			if actual != tc.expected {
				t.Errorf("expected %v, got %v", tc.expected, actual)
			}
		})
	}
}
