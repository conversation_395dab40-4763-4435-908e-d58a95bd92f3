package helper

import (
	"bufio"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"math"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/cov/iUT/helper/constant"
)

var CodeRegex = regexp.MustCompile("(?s)```[a-zA-Z]*\n(.*?)```")
var CodeRegexExecption = regexp.MustCompile("(?s)```[a-zA-Z]*\n(.*?)<cls>")
var CodeRegexForSingle = regexp.MustCompile(".*```[a-zA-Z]*\n([\\s\\S]*)\n```")

func ArrayContain(arr []string, target string) bool {
	for _, v := range arr {
		if v == target {
			return true
		}
	}
	return false
}

func ArrayContainSuffixMatch(arr []string, target string) string {
	for _, v := range arr {
		if strings.HasSuffix(v, target) {
			return v
		}
	}
	return ""
}

func RunCommandWithTerminal(cmd string) (err error) {
	_cmdprg := "/bin/sh"
	_cmdarg := "-c"
	_cmd := exec.Command(_cmdprg, _cmdarg, cmd+" 2>&1")

	_cmd.Stdin = os.Stdin
	stdout, err := _cmd.StdoutPipe()
	if err != nil {
		return
	}

	stderr, err := _cmd.StderrPipe()
	if err != nil {
		return
	}

	// to start cmd
	if err = _cmd.Start(); err != nil {
		return
	}

	// read stdout
	scanner := bufio.NewScanner(stdout)
	for scanner.Scan() {
		fmt.Println(scanner.Text())
	}

	// read stderr
	scanner = bufio.NewScanner(stderr)
	for scanner.Scan() {
		fmt.Println(scanner.Text())
		ok := strings.Contains(scanner.Text(), "Switched")
		if !ok {
			return errors.New(scanner.Text())
		}
	}

	// wait cmd to exit
	err = _cmd.Wait()
	if err != nil {
		return
	}
	return
}

func GenerateTimestampString() string {
	return time.Now().Format("20060102150405")
}

// RemoveLineNoAndEmptyLines removes line numbers and empty lines from a string
func RemoveLineNoAndEmptyLines(content string) string {
	var result strings.Builder
	scanner := bufio.NewScanner(strings.NewReader(content))

	for scanner.Scan() {
		line := scanner.Text()
		// Split the line by spaces and check if it has a number at the start
		parts := strings.Fields(line)
		if len(parts) > 0 {
			// Check if the first part is a number (line number)
			if _, err := strconv.Atoi(strings.TrimRight(parts[0], ".")); err == nil {
				// Remove the line number
				line = strings.TrimPrefix(line, parts[0]+" ")
				if line == "" {
					continue
				}
			}
			// Add the line to the result if it's not empty
			result.WriteString(line + "\n")
		}
	}

	return result.String()
}

func GetUTCodeFromString(content string, language string) string {
	if !strings.Contains(content, "```") {
		// java 和 go
		flag := strings.Contains(content, "package") && strings.Contains(content, "import")
		if !flag {
			// c++
			flag = strings.Contains(content, "include")
		}
		if !flag {
			return ""
		}

	}

	codeBlockCount := strings.Count(content, "```"+language+"\n")
	if codeBlockCount <= 1 {
		CodeRegex = CodeRegexForSingle
	}

	// 查找所有匹配项
	matches := CodeRegex.FindAllStringSubmatch(content, -1)
	for _, matcheChild := range matches {
		if strings.Contains(matcheChild[0], "```"+language) {
			content = matcheChild[1]
			// 处理模型返回有多个代码段的情况，确保取完整的代码段
			flag := strings.Contains(content, "@Test")
			if language == "java" && !flag {
				continue
			}
			return content
		}
	}
	matches = CodeRegexExecption.FindAllStringSubmatch(content, -1)
	for _, matcheChild := range matches {
		if strings.Contains(matcheChild[0], "```"+language) {
			return matcheChild[1]
		}
	}
	if strings.HasPrefix(content, "```") {
		return ""
	}
	return content
}

// 从字符串内容中提取被``` ```包含的代码块
func GetMarkdownBlockFromContent(content string) string {
	matches := CodeRegex.FindAllStringSubmatch(content, -1)
	for _, matcheChild := range matches {
		if len(matcheChild) > 1 {
			return matcheChild[1]
		}
	}

	return ""
}

/**
 * 判断代码文件的完整度
 */
func IntegrityJudgment(content string) bool {
	if !strings.Contains(content, "}") {
		return false
	}

	stack := make([]rune, 0)
	for _, char := range content {
		switch char {
		case '{':
			stack = append(stack, char)
		case '}':
			if len(stack) == 0 || stack[len(stack)-1] != '{' {
				return false // 没有左大括号匹配
			}
			stack = stack[:len(stack)-1] // 弹出左大括号
		}
	}

	return len(stack) == 0 // 如果堆栈为空，表示所有大括号都匹配
}

func add(a, b int) int {
	fmt.Println("a  b = ", a+b)
	return a + b
}

func add1(a, b int) int {
	fmt.Println("a  b = ", a+b)
	return a + b
}

func GetCommonProgrammingLanguageExt() []string {
	return []string{
		".go",                                                             //go
		".java",                                                           //java
		".cxx", ".cpp", ".c", ".hpp", ".h", ".cc", ".c++", ".tpp", ".txx", //cpp
		".js", ".ts", ".tsx", ".jsx", ".javascript", "._js", ".es", ".es6", ".gs", ".jake", ".jslib", ".jsm", ".jss",
		".mjs", ".njs", ".sjs", //js ts
		".py",         //python
		".scala",      //scala
		".kt", ".kts", //kotlin
	}
}

func GetLangExts(lang string) []string {
	switch lang {
	case constant.LangGo:
		return []string{".go"}
	case constant.LangJava:
		return []string{".java"}
	default:
		return []string{}
	}
}

// CheckFileIsTarget 检测文件是否是目标被测文件(返回文件所属语言)
func CheckFileIsTarget(path string, info fs.FileInfo) (bool, string) {
	if info == nil {
		return false, ""
	}
	ext := filepath.Ext(path)
	fileName := filepath.Base(path)

	// java被测文件识别
	if ext == ".java" {
		if strings.HasPrefix(fileName, "Test") || strings.HasSuffix(fileName, "Test.java") {
			return false, ""
		}
		if JavaInterface(path) {
			return false, ""
		}
		return true, "java"
	}

	// go被测文件识别
	//	if ext == ".go" {
	//		if strings.HasSuffix(fileName, "_test.go") {
	//			return false, ""
	//		}
	//		return true, "go"
	//	}
	//
	//	// cpp被测文件识别
	//	srcRe := regexp.MustCompile(`.*\.c$|.*\.cxx$|.*\.hpp$|.*\.h$|.*\.cpp$|.*\.cc$|.*\.c\+\+|.*\.tpp$|.*\.txx$`)
	//	if srcRe.MatchString(info.Name()) {
	//		testRe := regexp.MustCompile(`^[T,t]est(\w+)|^[U,u]nittest(\w+)|(\w+)[T,t]est$|(\w+)[U,u]nittest$`)
	//		if matches := testRe.FindStringSubmatch(info.Name()); len(matches) > 4 {
	//			return false, ""
	//		}
	//		return true, "cpp"
	//	}
	//
	//	// js被测文件识别
	//	srcRe = regexp.MustCompile(`.*\.ts$|.*\.js$|.*\.tsx$|.*\.jsx$|.*\.javascript$|.*\._js$|.*\.es$|.*\.es6$|
	//.*\.gs$|.*\.jake$|.*\.jslib$|.*\.jsm$|.*\.jss$|.*\.mjs$|.*\.njs$|.*\.sjs$`)
	//	if srcRe.MatchString(info.Name()) {
	//		testRe := regexp.MustCompile(`(\w+)[.-](test|spec)[.-](\w+)`)
	//		if matches := testRe.FindStringSubmatch(info.Name()); len(matches) > 3 {
	//			return false, ""
	//		}
	//		return true, "js"
	//	}

	return false, ""
}

func FileMd5(filename string) string {
	// 文件全路径名
	pFile, err := os.Open(filename)
	if err != nil {
		return ""
	}
	defer pFile.Close()
	md5h := md5.New()

	if _, err := io.Copy(md5h, pFile); err != nil {
		return ""
	}
	return hex.EncodeToString(md5h.Sum(nil))
}

func StringMd5(str string) string {
	h := md5.New()

	if _, err := io.WriteString(h, str); err != nil {
		return ""
	}

	md5Bytes := h.Sum(nil)

	// 将字节切片转换为16进制表示的字符串
	return hex.EncodeToString(md5Bytes)
}

func FindRangeByValues(sortedList []int, startValue int, endValue int) []int {
	startIndex, endIndex := -1, -1

	// Iterate over the sorted list to find the indices of startValue and endValue
	for i, v := range sortedList {
		if v >= startValue && startIndex == -1 {
			startIndex = i
		}
		if v <= endValue {
			endIndex = i
		}
	}

	// Check if valid startIndex and endIndex are found
	if startIndex == -1 || endIndex == -1 || startIndex > endIndex {
		return []int{}
	}

	return sortedList[startIndex : endIndex+1]
}

func Divide(numerator, denominator, decimalPlaces int) float64 {
	if denominator == 0 {
		return 0
	}
	// 使用 math.Round 来四舍五入保留两位小数
	result := float64(numerator) / float64(denominator)
	factor := math.Pow(10, float64(decimalPlaces))
	roundedResult := math.Round(result*factor) / factor
	if roundedResult > 100 {
		return float64(100)
	}
	return roundedResult
}

func GetUserHome() string {
	// 或者使用环境变量
	homeDirEnv, err := os.UserHomeDir()
	if err != nil {
		return ""
	}

	return homeDirEnv
}

func MergeAndSort(sortedSliceA, sortedSliceB []int) []int {
	i, j := 0, 0
	result := make([]int, 0, len(sortedSliceA)+len(sortedSliceB))

	for i < len(sortedSliceA) && j < len(sortedSliceB) {
		if sortedSliceA[i] < sortedSliceB[j] {
			if len(result) == 0 || result[len(result)-1] != sortedSliceA[i] {
				result = append(result, sortedSliceA[i])
			}
			i++
		} else if sortedSliceA[i] > sortedSliceB[j] {
			if len(result) == 0 || result[len(result)-1] != sortedSliceB[j] {
				result = append(result, sortedSliceB[j])
			}
			j++
		} else {
			if len(result) == 0 || result[len(result)-1] != sortedSliceA[i] {
				result = append(result, sortedSliceA[i])
			}
			i++
			j++
		}
	}

	for i < len(sortedSliceA) {
		if len(result) == 0 || result[len(result)-1] != sortedSliceA[i] {
			result = append(result, sortedSliceA[i])
		}
		i++
	}

	for j < len(sortedSliceB) {
		if len(result) == 0 || result[len(result)-1] != sortedSliceB[j] {
			result = append(result, sortedSliceB[j])
		}
		j++
	}

	return result
}

// 数组必须有序，自增
func RemoveAndSort(sortedSliceA, sortedSliceB []int) []int {
	// 获取两个切片中不重复的元素
	var result = make([]int, 0)

	if len(sortedSliceA) == 0 || len(sortedSliceB) == 0 {
		return sortedSliceA
	}

	i, j := 0, 0
	for i < len(sortedSliceA) {
		if sortedSliceA[i] < sortedSliceB[j] {
			result = append(result, sortedSliceA[i])
			i++
		} else if sortedSliceA[i] > sortedSliceB[j] {
			j++
		} else {
			i++
			j++
		}

		if j == len(sortedSliceB) {
			if i < len(sortedSliceA) {
				result = append(result, sortedSliceA[i:]...)
			}
			break
		}
	}

	return result
}
func RemoveDuplicates(arr []int) []int {
	uniqueMap := make(map[int]struct{}) // 用 map 存储唯一元素
	var result []int

	for _, v := range arr {
		if _, exists := uniqueMap[v]; !exists {
			uniqueMap[v] = struct{}{}  // 添加到 map
			result = append(result, v) // 添加到结果数组
		}
	}

	return result
}

func RemoveElementsWithInAnotherArr(srcArr, anotherArr []int) []int {
	// 将 arr2 的元素存入 map
	excludeMap := make(map[int]struct{})
	for _, v := range anotherArr {
		excludeMap[v] = struct{}{}
	}

	// 遍历 arr1，添加不在 map 中的元素
	var result []int
	for _, v := range srcArr {
		if _, exists := excludeMap[v]; !exists {
			result = append(result, v)
		}
	}

	return result
}

func FindAvailablePort() (int, error) {
	addr, err := net.ResolveTCPAddr("tcp", "localhost:0")
	if err != nil {
		return 0, err
	}

	listener, err := net.ListenTCP("tcp", addr)
	if err != nil {
		return 0, err
	}
	defer listener.Close()

	return listener.Addr().(*net.TCPAddr).Port, nil
}
