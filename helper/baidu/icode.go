package baidu

import (
	"path/filepath"
	"strings"
)

func GetBaiduRepo(gitRoot string) string {
	paths := strings.Split(gitRoot, string(filepath.Separator))
	baiduIndex := -1
	for index, path := range paths {
		if path == "baidu" {
			baiduIndex = index
			break
		}
	}
	// 如果找到 "baidu"，则提取从该索引开始的子路径
	var resultPath string
	if baiduIndex != -1 {
		subPaths := paths[baiduIndex:]
		// 重新构建路径
		resultPath = filepath.Join(subPaths...)
	}

	return resultPath
}
