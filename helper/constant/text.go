package constant

const TaskExpectedTextForDiff = "好的，任务已接收。我将扫描项目中的`git`变更文件，为变更的方法批量生成单测用例，并对用例进行验证、优化，尝试发现潜在业务逻辑问题，提升代码覆盖。"
const TaskExpectedTextForGen = "好的，任务已收到。我将为你选中的目标文件批量生成单测用例，并对用例进行验证、优化，尝试发现潜在业务逻辑问题，提升代码覆盖。"
const TaskExpectedTextForMethod = "好的，已收到任务。我将为`%s`方法生成单元测试，并对其进行验证、优化，确保生成有效的单测代码，提升代码覆盖。"

const TargetScanTableHeaderForDiff = "变更文件列表"
const TargetScanTableHeaderForGen = "被测文件列表"
const TargetScanTableHeaderForMethod = "被测方法列表"

const TargetScanSummaryForDiff = "已完成变更文件分析。接下来我将针对%d个已变更文件批量生成单测用例。"
const TargetScanSummaryForGen = "已完成目标文件分析。接下来我将针对%d个被测文件批量生成单测用例。"
const TargetScanSummaryForMethod = "已完成目标被测方法分析。接下来我将针对%d个被测方法生成单测用例。"

const NoSrcFilesFoundForDiff = "已完成变更文件分析。"
const NoSrcFilesFoundForGen = "已完成目标文件分析。"
const FirstGenerateForMethodSummary = "已完成单测用例生成。接下来我将对生成的单测用例进行验证、优化，以确保单测用例可靠有效。"

const SKIPPEDForMethodForJava = "被测方法已100%覆盖或junit配置有误，跳过生成单测用例。"
const SKIPPEDForMethod = "被测方法已100%覆盖，跳过生成单测用例。"
const SKIPPEDForDiff = "本次变更无有效代码变更，或变更行已经达到100%覆盖，无需生成单测用例。"
const SKIPPEDForGen = "目标文件中未发现有效的被测方法，或被测方法均已达到100%覆盖，无需生成单测用例。"

const LoadingMsgForGen = "正在为您生成单测用例"
const LoadingMsgForParse = "正在为您分析文件"
const LoadingMsgForOptimize = "正在对生成用例进行验证、优化"
const LoadingMsgForVerify = "正在为`%s`执行并验证用例"

const FailedMessageForSaas = "任务异常退出，请发送邮件至****************，联系文心快码帮您解决问题"
const FailedMessageForInner = "任务异常退出，请加入 Comate 用户群 8323187反馈问题"

func GetFailMessage(isSaas bool) string {
	if isSaas {
		return FailedMessageForSaas
	}
	return FailedMessageForInner
}
