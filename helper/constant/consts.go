package constant

import (
	"os"
	"strconv"
	"time"
)

// return code
const (
	SUCCESS           int = 0   // success
	UPDATE_FAILED     int = 1   // fail to update
	RUN_SUB_CMD_ERROR int = 3   // run sub comamnd error
	INVALID_ARGS      int = 4   // invalid args
	INTERNAL_ERROR    int = 254 // internal error

	// cmd type
	VALID_CMD       int = 10 // valid cmd
	UNSUPPORTED_CMD int = 11 // cmd that supported in Linux, but not supported on Mac
	INVALID_CMD     int = 12 // invalid cmd
)

const PercentZero = "0.00%"

// case status
const (
	RunPass    string = "PASS"
	AssertFail string = "ASSERT_FAIL"
	RunFail    string = "FAIL"
	Abandon    string = "ABANDON"
)

const (
	LangGo   = "go"
	LangJava = "java"
)

const (
	JUNIT4 = "junit4"
	JUNIT5 = "junit5"
)

// trigger type
const (
	IDETrigger   = "IDE"
	ICODETrigger = "ICODE"
	TOOLTrigger  = "TOOL"
)

const (
	GenerateForMethod = "method"
	GenerateForDiff   = "diff"
	GenerateForGen    = "gen"
)

const SUCCEED = "SUCCEED"
const FIXED = "FIXED"
const PROCESSING = "PROCESSING"

const FAILED = "FAILED"
const RUNNING = "RUNNING"
const SKIPPED = "SKIPPED"
const ABORT = "ABORT"

// envCheck
const USABLE = "USABLE"
const UNUSABLE = "UNUSABLE"

const CurrentCoverage = "current"
const OriginCoverage = "origin"

var TaskID = ""
var RepoName = ""
var WorkDir = ""

func init() {
	TaskID = os.Getenv("taskId")
	// 打印环境变量
	if TaskID == "" {
		TaskID = strconv.Itoa(int(time.Now().Unix()))
	}
}
