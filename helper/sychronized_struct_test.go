package helper

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

func TestSafeMapConcurrentAddDelete(t *testing.T) {
	sm := NewSafeMap()
	var wg sync.WaitGroup

	numGoroutines := 100
	key := "key"
	value := "value"

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			fmt.Println("adding key: " + key)
			sm.Add(key, value)
		}()
	}

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			fmt.Println("deleting key: " + key)
			sm.Delete(key)
		}()
	}

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(n int) {
			defer wg.Done()
			k := key + fmt.Sprintf("%d", n)
			fmt.Println("adding key: " + key)
			sm.Add(k, value)
			time.Sleep(time.Millisecond)
			fmt.Println("deleting key: " + key)
			sm.Delete(k)
		}(i)
	}

	wg.Wait()

	finalLen := sm.Len()
	if finalLen > 1 {
		t.<PERSON><PERSON><PERSON>("SafeMap should be empty or contain one element, but got length: %d", finalLen)
	}

	if _, exists := sm.Get(key); exists {
		t.Errorf("SafeMap should not contain the key %q, but it does", key)
	}

	sm.Add("non-existent key", "value")
	sm.Add("non-existent key", "value")
	sm.Delete("non-existent key")
	sm.Delete("non-existent key")
	sm.Delete("non-existent key")
	fmt.Println(sm.Get("non-existent key"))
}

func TestSafeMap_Inc(t *testing.T) {
	sm := NewSafeMap()
	key := "key"
	value := 1
	sm.Add(key, value)
	result := sm.Inc(key)
	if result != 2 {
		t.Errorf("Expected 2, got %d", result)
	}
}

func TestSafeMap_Inc_Concurrent(t *testing.T) {
	sm := NewSafeMap()
	key := "key"
	value := 1
	sm.Add(key, value)
	var wg sync.WaitGroup

	numGoroutines := 100
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			sm.Inc(key)
		}()
	}

	wg.Wait()

	result, _ := sm.Get(key)
	if result != 101 {
		t.Errorf("Expected 101, got %d", result)
	}
}
