package helper

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"reflect"
	"testing"
)

func TestRunCommandWithTerminal(t *testing.T) {
	type args struct {
		cmd  string
		want string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Run command and check output",
			args: args{
				cmd: "ls",
			},
			wantErr: false,
		},
		{
			name: "Run command and check output",
			args: args{
				cmd: "ls -1 /home1/",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := RunCommandWithTerminal(tt.args.cmd); (err != nil) != tt.wantErr {
				t.Errorf("RunCommandWithTerminal() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestArrayContain 是用于测试 ArrayContain
// generated by Comate
func TestArrayContain(t *testing.T) {
	type args struct {
		arr      []string
		target   string
		expected bool
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test-1",
			args: args{
				arr:      []string{"1", "2", "3", "4"},
				target:   "3",
				expected: true,
			},
		},
		{
			name: "test-2",
			args: args{
				arr:      []string{"1", "2", "3", "4"},
				target:   "5",
				expected: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ArrayContain(tt.args.arr, tt.args.target); got != tt.args.expected {
				t.Errorf("ArrayContain() = %v, expected %v", got, tt.args.expected)
			}
		})
	}
}

// TestArrayContainSuffixMatch 是用于测试 ArrayContainSuffixMatch
// generated by Comate
func TestArrayContainSuffixMatch(t *testing.T) {
	testCases := []struct {
		Name        string
		Array       []string
		Target      string
		ExpectValue string
	}{
		{
			Name:        "test-1",
			Array:       []string{"a.md", "b.md", "c.py"},
			Target:      ".md",
			ExpectValue: "a.md",
		},
		{
			Name:        "test-2",
			Array:       []string{"a.md", "b.md", "c.py"},
			Target:      ".py",
			ExpectValue: "c.py",
		},
		{
			Name:        "test-3",
			Array:       []string{"a.md", "b.md", "c.py"},
			Target:      ".go",
			ExpectValue: "",
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.Name, func(t *testing.T) {
			result := ArrayContainSuffixMatch(testCase.Array, testCase.Target)
			if result != testCase.ExpectValue {
				t.Errorf("Got %s, expect %s", result, testCase.ExpectValue)
			}
		})
	}
}

// TestIntegrityJudgment 是用于测试 IntegrityJudgment
// generated by Comate
func TestIntegrityJudgment(t *testing.T) {
	assert.Equal(t, true, IntegrityJudgment("{test}"))
	assert.Equal(t, false, IntegrityJudgment("{{test}"))
	assert.Equal(t, false, IntegrityJudgment("{test"))
	assert.Equal(t, false, IntegrityJudgment("}"))
}

// TestGetCodeFromString 是用于测试 GetCodeFromString
// generated by Comate
func TestGetCodeFromString(t *testing.T) {
	type args struct {
		content  string
		language string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test-1",
			args: args{
				content:  "```go\ntest\n```",
				language: "go",
			},
			want: "",
		},
		{
			name: "test-2",
			args: args{
				content:  "```python\ntest\n```",
				language: "",
			},
			want: "",
		},
		{
			name: "test-3",
			args: args{
				content:  "根据报错信息，问题出在测试用例的这一行：\n\n\n```java\nverify(response).addCookie(any(Cookie.class));\n```\n错误提示表示找不到符号 `Cookie`。\n\n这个问题可能是由于测试用例中没有导入 `Cookie` 类导致的。要解决这个问题，你需要在测试类中导入 `Cookie` 类。\n\n在 `LoginServiceTest.java` 文件的顶部，添加以下导入语句：\n\n\n```java\nimport javax.servlet.http.Cookie;\n```\n修改后的完整测试用例代码：\n\n\n```java\npackage com.xxl.job.admin.service;\n\nimport com.xxl.job.admin.core.model.XxlJobUser;\nimport com.xxl.job.admin.core.util.CookieUtil;\nimport com.xxl.job.admin.dao.XxlJobUserDao;\nimport com.xxl.job.core.biz.model.ReturnT;\nimport org.junit.jupiter.api.BeforeEach;\nimport org.junit.jupiter.api.Test;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.MockitoAnnotations;\nimport org.springframework.boot.test.context.SpringBootTest;\nimport org.springframework.test.util.ReflectionTestUtils;\n\nimport javax.servlet.http.Cookie;\nimport javax.servlet.http.HttpServletRequest;\nimport javax.servlet.http.HttpServletResponse;\n\nimport static org.junit.jupiter.api.Assertions.assertEquals;\nimport static org.mockito.Mockito.*;\n\n@SpringBootTest\npublic class LoginServiceTest {\n\n    @Mock\n    private HttpServletRequest request;\n\n    @Mock\n    private HttpServletResponse response;\n\n    @Mock\n    private XxlJobUserDao xxlJobUserDao;\n\n    @InjectMocks\n    private LoginService loginService;\n\n    @BeforeEach\n    public void setUp() {\n        MockitoAnnotations.openMocks(this);\n    }\n\n    @Test\n    public void testLogout() {\n        // Setup\n        ReflectionTestUtils.setField(loginService, \"LOGIN_IDENTITY_KEY\", \"XXL_JOB_LOGIN_IDENTITY\");\n\n        // Execute\n        ReturnT<String> result = loginService.logout(request, response);\n\n        // Verify\n        verify(response).addCookie(any(Cookie.class));\n\n        // Assert\n        assertEquals(ReturnT.SUCCESS.getCode(), result.getCode());\n        assertEquals(ReturnT.SUCCESS.getMsg(), result.getMsg());\n    }\n}\n```\n请注意，我在 `setUp` 方法中添加了 `ReflectionTestUtils.setField` 来设置 `LOGIN_IDENTITY_KEY` 的值，这是因为测试类中使用了静态字段 `LOGIN_IDENTITY_KEY`，我们需要在测试开始前手动设置它的值。\n\n修复后的测试代码应该能够成功编译和执行，并且能够通过测试。<cls>",
				language: "java",
			},
			want: "",
		},
		{
			name: "test-4",
			args: args{
				content:  "```java\npackage com.baidu.coding.suggestion.gateway.suggest.service.chain;\n\nimport com.baidu.coding.suggestion.gateway.model.bean.ModelInvokerBeanFactory;\nimport org.junit.Before;\nimport org.junit.Test;\nimport org.mockito.InjectMocks;\nimport org.mockito.Mock;\nimport org.mockito.MockitoAnnotations;\n\nimport static org.junit.Assert.assertEquals;\nimport static org.mockito.Mockito.*;\n\npublic class MidRewriteRequestChainTest {\n\n    @InjectMocks\n    private MidRewriteRequestChain midRewriteRequestChain;\n\n    @Mock\n    private ModelInvokerBeanFactory modelInvokerBeanFactory;\n\n    @Before\n    public void setUp() {\n        MockitoAnnotations.initMocks(this);\n    }\n\n    @Test\n    public void testRemoveMarkdownCodeLang_endsWithCodeBlock() {\n        String markdown = \"```code block```\";\n        String expected = \"\";\n        String result = midRewriteRequestChain.removeMarkdownCodeLang(markdown);\n        assertEquals(expected, result);\n    }\n\n    @Test\n    public void testRemoveMarkdownCodeLang_startsWithCodeBlock() {\n        String markdown = \"```\\ncode block\\n```\";\n        String expected = \"code block\";\n        String result = midRewriteRequestChain.removeMarkdownCodeLang(markdown);\n        assertEquals(expected, result);\n    }\n\n    @Test\n    public void testRemoveMarkdownCodeLang_endsAndStartsWithCodeBlock() {\n        String markdown = \"```\\ncode block\\n```\";\n        String expected = \"code block\";\n        String result = midRewriteRequestChain.removeMarkdownCodeLang(markdown);\n        assertEquals(expected, result);\n    }\n\n    @Test\n    public void testRemoveMarkdownCodeLang_noCodeBlock() {\n        String markdown = \"normal text\";\n        String expected = \"\";\n        String result = midRewriteRequestChain.removeMarkdownCodeLang(markdown);\n        assertEquals(expected, result);\n    }\n\n    @Test\n    public void testRemoveMarkdownCodeLang_emptyString() {\n        String markdown = \"\";\n        String expected = \"\";\n        String result = midRewriteRequestChain.removeMarkdownCodeLang(markdown);\n        assertEquals(expected, result);\n    }\n\n    @Test\n    public void testRemoveMarkdownCodeLang_multipleCodeBlocks() {\n        String markdown = \"```\\ncode block 1\\n```\\n```\\ncode block 2\\n```\";\n        String expected = \"code block 1\\ncode block 2\";\n        String result = midRewriteRequestChain.removeMarkdownCodeLang(markdown);\n        assertEquals(expected, result);\n    }\n}\n```",
				language: "java",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetUTCodeFromString(tt.args.content, tt.args.language)
			fmt.Println(got)
			if got == tt.want {
				t.Errorf("GetCodeFromString() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestGenerateTimestampString 是用于测试 GenerateTimestampString
// generated by Comate
func TestGenerateTimestampString(t *testing.T) {
	type args struct {
	}
	tests := []struct {
		name string
		want string
	}{
		{
			"", GenerateTimestampString(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GenerateTimestampString(); got != tt.want {
				t.Errorf("GenerateTimestampString() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Test_add 是用于测试 add
// generated by Comate
func Test_add(t *testing.T) {
	type args struct {
		a int
		b int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{"test-1", args{1, 2}, 3},
		{"test-2", args{1, -2}, -1},
		{"test-3", args{0, 0}, 0},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := add(tt.args.a, tt.args.b); got != tt.want {
				t.Errorf("add() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Test_add1 是用于测试 add1
// generated by Comate
func Test_add1(t *testing.T) {
	type args struct {
		a int
		b int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{"test-1", args{1, 2}, 3},
		{"test-2", args{-1, -2}, -3},
		{"test-3", args{1, -2}, -1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := add1(tt.args.a, tt.args.b); got != tt.want {
				t.Errorf("add() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFileMd5(t *testing.T) {
	testCases := []struct {
		Name        string
		Filename    string
		ExpectValue string
	}{
		{
			Name:        "test-1",
			Filename:    "/var/folders/1d/v04c58y55173cp86_hmkpf980000gn/T/runAgent.jar",
			ExpectValue: "9546d17060c37f73e13a8f7d86a31c9a",
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.Name, func(t *testing.T) {
			result := FileMd5(testCase.Filename)
			if result != testCase.ExpectValue {
				t.Errorf("Got %s, expect %s", result, testCase.ExpectValue)
			}
		})
	}
}

func TestFindRangeByValues(t *testing.T) {
	type args struct {
		sortedList []int
		startValue int
		endValue   int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			name: "test-1",
			args: args{
				sortedList: []int{1, 2, 4, 5, 6, 7, 8, 9},
				startValue: 4,
				endValue:   8,
			},
			want: []int{4, 5, 6, 7, 8},
		},
		{
			name: "test-2",
			args: args{
				sortedList: []int{1, 2, 4, 5, 6, 7, 8, 9},
				startValue: 1,
				endValue:   10,
			},
			want: []int{1, 2, 4, 5, 6, 7, 8, 9},
		},
		{
			name: "test-3",
			args: args{
				sortedList: []int{1, 2, 4, 5, 6, 7, 9},
				startValue: 3,
				endValue:   8,
			},
			want: []int{4, 5, 6, 7},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FindRangeByValues(tt.args.sortedList, tt.args.startValue, tt.args.endValue); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FindRangeByValues() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDivide(t *testing.T) {
	type args struct {
		a int
		b int
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "Test_Divide_1",
			args: args{10, 2},
			want: float64(500),
		},
		{
			name: "Test_Divide_2",
			args: args{10, 0},
			want: 0,
		},
		{
			name: "Test_Divide_3",
			args: args{1, 3},
			want: 33.33,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Divide(tt.args.a, tt.args.b, 2); got != tt.want {
				t.Errorf("Divide() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRemoveAndSort(t *testing.T) {
	type args struct {
		sortedSliceA []int
		sortedSliceB []int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			name: "Test_RemoveAndSort",
			args: args{[]int{19, 36, 83, 84, 85, 87, 88, 90, 93}, []int{19, 36, 40, 42, 87, 88}},
			want: []int{83, 84, 85, 90, 93},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveAndSort(tt.args.sortedSliceA, tt.args.sortedSliceB); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemoveAndSort() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMergeAndSort(t *testing.T) {
	type args struct {
		sortedSliceA []int
		sortedSliceB []int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			"empty input",
			args{
				[]int{},
				[]int{},
			},
			[]int{},
		},
		{
			"one empty slice",
			args{
				[]int{1, 2, 3, 4, 5},
				[]int{},
			},
			[]int{1, 2, 3, 4, 5},
		},
		{
			"one empty slice",
			args{
				[]int{},
				[]int{1, 2, 3, 4, 5},
			},
			[]int{1, 2, 3, 4, 5},
		},
		{
			"two non-empty slices",
			args{
				[]int{1, 2, 3, 4, 5, 6},
				[]int{6, 7, 8, 9, 10},
			},
			[]int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MergeAndSort(tt.args.sortedSliceA, tt.args.sortedSliceB); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MergeAndSort() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMd5(t *testing.T) {
	md5 := FileMd5("/var/folders/1d/v04c58y55173cp86_hmkpf980000gn/T/javaagent-1.1-client-SNAPSHOT.jar")
	fmt.Println(md5)
	md5 = FileMd5("/var/folders/1d/v04c58y55173cp86_hmkpf980000gn/T/javaagent-1.1-client-SNAPSHOT.jar")
	fmt.Println(md5)
}
func TestFindAvailablePort(t *testing.T) {
	tests := []struct {
		name    string
		want    int
		wantErr bool
	}{
		{
			name:    "test-1",
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FindAvailablePort()
			if (err != nil) != tt.wantErr {
				t.Errorf("FindAvailablePort() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got < tt.want {
				t.Errorf("FindAvailablePort() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetMarkdownBlockFromContent(t *testing.T) {
	testCases := []struct {
		Name        string
		Content     string
		ExpectValue string
	}{
		{
			Name:        "test-1",
			Content:     "根据您的要求，以下是更新后的完整pom文件内容，其中包含了您指定的测试依赖（`org.junit.jupiter:junit-jupiter:5.10.0:test`、`org.mockito:mockito-core:5.5.0`、`org.mockito:mockito-inline:5.5.0`），并且由于原始pom文件中没有直接显示Spring Boot的依赖，因此没有添加`spring-boot-starter-test`。如果后续发现Spring Boot的依赖，请相应添加。\n\n```xml\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\n         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n    <modelVersion>4.0.0</modelVersion>\n    <parent>\n        <groupId>com.xuxueli</groupId>\n        <artifactId>xxl-job-executor-samples</artifactId>\n        <version>2.4.1-SNAPSHOT</version>\n    </parent>\n    <artifactId>xxl-job-executor-sample-frameless</artifactId>\n    <packaging>jar</packaging>\n\n    <name>${project.artifactId}</name>\n    <description>Example executor project for spring boot.</description>\n    <url>https://www.xuxueli.com/</url>\n\n    <dependencies>\n\n        <!-- slf4j -->\n        <dependency>\n            <groupId>org.slf4j</groupId>\n            <artifactId>slf4j-log4j12</artifactId>\n            <version>${slf4j-api.version}</version>\n        </dependency>\n\n        <!-- xxl-job-core -->\n        <dependency>\n            <groupId>com.xuxueli</groupId>\n            <artifactId>xxl-job-core</artifactId>\n            <version>${project.parent.version}</version>\n        </dependency>\n\n        <!-- Test Dependencies -->\n        <dependency>\n            <groupId>org.junit.jupiter</groupId>\n            <artifactId>junit-jupiter</artifactId>\n            <version>5.10.0</version>\n            <scope>test</scope>\n        </dependency>\n        <dependency>\n            <groupId>org.mockito</groupId>\n            <artifactId>mockito-core</artifactId>\n            <version>5.5.0</version>\n            <scope>test</scope>\n        </dependency>\n        <dependency>\n            <groupId>org.mockito</groupId>\n            <artifactId>mockito-inline</artifactId>\n            <version>5.5.0</version>\n            <scope>test</scope>\n        </dependency>\n\n    </dependencies>\n\n</project>\n```\n\n请注意，由于`slf4j-api.version`属性在父POM中定义，因此这里保留了`${slf4j-api.version}`的占位符。如果在实际项目中遇到任何问题，请确保父POM中已正确定义了此属性。",
			ExpectValue: "```go\ncode\n```",
		},
	}
	for _, testCase := range testCases {
		t.Run(testCase.Name, func(t *testing.T) {
			result := GetMarkdownBlockFromContent(testCase.Content)
			if result == "" {
				t.Errorf("Got %s", result)
			} else {
				fmt.Println(result)
			}
		})
	}
}

func TestRemoveLineNoAndEmptyLines(t *testing.T) {
	type args struct {
		content string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			"Test with normal string",
			args{`1. package com.xxl.job.admin.service.impl;
2. import com.xxl.job.admin.core.model.XxlJobGroup;
3. 
4. import org.junit.jupiter.api.Test;
5. import org.mockito.Mock;
6. import org.mockito.MockitoAnnotations;
7. import java.util.List;
8. import static org.mockito.Mockito.*;
9. import java.util.Map;
10. import static org.junit.jupiter.api.Assertions.assertEquals;
11. import com.xxl.job.admin.core.model.XxlJobInfo;
12. import com.xxl.job.admin.dao.XxlJobInfoDao;
13. import org.junit.jupiter.api.BeforeEach;
14. import org.mockito.InjectMocks;
15. import java.util.ArrayList;
16. import com.xxl.job.admin.dao.XxlJobLogDao;
17. import com.xxl.job.core.biz.model.ReturnT;
18. import com.xxl.job.admin.dao.XxlJobLogGlueDao;
19. import org.junit.jupiter.api.extension.ExtendWith;
20. import java.util.Date;
21. import com.xxl.job.admin.core.scheduler.MisfireStrategyEnum;
22. import com.xxl.job.admin.core.scheduler.ScheduleTypeEnum;
23. import org.mockito.junit.jupiter.MockitoExtension;
24. import com.xxl.job.admin.core.thread.JobScheduleHelper;
25. import com.xxl.job.admin.dao.XxlJobGroupDao;
26. 
27. @ExtendWith(MockitoExtension.class)
28. public class XxlJobServiceImplTest{
29. 
30.     @Mock
31.     private XxlJobGroupDao xxlJobGroupDao;
32. 
33.     private XxlJobInfo validJobInfo;
34. 
35.     @Mock
36.     private XxlJobLogDao xxlJobLogDao;
37. 
38.     @Mock
39.     private XxlJobLogGlueDao xxlJobLogGlueDao;
40. 
41.     @Mock
42.     private XxlJobInfoDao xxlJobInfoDao;
43. 
44.     @InjectMocks
45.     private XxlJobServiceImpl xxlJobService;
46. 
47.     @BeforeEach
48.     public void setUp() {
49.         MockitoAnnotations.openMocks(this);
50.     }
51. 
52. 
53. 
54. 
55. 
56. 
57. 
58. 
59. 
60. 
61. 
62. 
63. 
64. 
65. 
66. 
67. 
68. 
69. 
70. 
71. 
72. 
73. 
74. 
75. 
76. 
77. 
78. 
79. 
80. 
81. 
82. 
83. 
84. 
85. 
86. 
87. 
88. 
89. 
90. 
91. 
92. 
93. 
94. 
95. 
96. 
97. 
98. 
99. 
100. 
101. 
102. 
103. 
104. 
105. 
106. 
107. 
108. 
109. 
110. 
111. 
112. 
113. 
114. 
115. 
116. 
117. 
118. 
119. 
120. 
121. 
122. 
123. 
124.     @Test
125.     public void testUpdateSuccess() {
126.         validJobInfo = new XxlJobInfo();
127.         validJobInfo.setId(1);
128.         validJobInfo.setJobGroup(1);
129.         validJobInfo.setJobDesc("Test Job");
130.         validJobInfo.setAuthor("Test Author");
131.         validJobInfo.setScheduleType(ScheduleTypeEnum.CRON.ordinal());
132.         validJobInfo.setScheduleConf("0 0 12 * * ?");
133.         validJobInfo.setMisfireStrategy(MisfireStrategyEnum.DO_NOTHING.ordinal());
134.         validJobInfo.setExecutorRouteStrategy("RANDOM");
135.         validJobInfo.setExecutorHandler("testHandler");
136.         validJobInfo.setExecutorParam("testParam");
137.         validJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
138.         validJobInfo.setExecutorTimeout(1000);
139.         validJobInfo.setExecutorFailRetryCount(3);
140.         validJobInfo.setChildJobId("1,2,3");
141.         validJobInfo.setTriggerNextTime(new Date().getTime());
142.     
143.         when(xxlJobInfoDao.loadById(1)).thenReturn(validJobInfo);
144.         when(xxlJobGroupDao.load(1)).thenReturn(new XxlJobGroup());
145.         when(JobScheduleHelper.generateNextValidTime(any(), any())).thenReturn(new Date());
146.     
147.         ReturnT<String> result = xxlJobService.update(validJobInfo);
148.     
149.         assertEquals(ReturnT.SUCCESS.getCode(), result.getCode());
150.         verify(xxlJobInfoDao, times(1)).update(validJobInfo);
151.     }
152. 
153. }`},
			"",
		},
		{
			args: args{`package com.xxl.job.core.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertTrue;
import java.io.FileWriter;

public class ReaderWriterUtilTest {

    @Test
    public void testWriteSampleData(@TempDir Path tempDir) throws IOException {
        File tempFile = tempDir.resolve("testFile.txt").toFile();
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile))) {
            ReaderWriterUtil.writeSampleData(writer);
        }

        List<String> fileContent = Files.readAllLines(tempFile.toPath());
        assertTrue(fileContent.stream().anyMatch(line -> line.contains("Hello, this is a sample text data."));
        assertTrue(fileContent.stream().anyMatch(line -> line.contains("Java IO is powerful for handling file operations!")));
    }
}`},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveLineNoAndEmptyLines(tt.args.content); got == tt.want {
				t.Errorf("RemoveLineNoAndEmptyLines() = %v, want %v", got, tt.want)
			} else {
				fmt.Println(got)
			}
		})
	}
}
