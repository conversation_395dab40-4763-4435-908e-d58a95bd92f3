package helper

import (
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsSymbolLink(t *testing.T) {
	type args struct {
		file_path string
	}
	tests := []struct {
		name          string
		args          args
		wantIsSymlink bool
		wantErr       bool
	}{
		{
			name: "test not symbol link",
			args: args{
				file_path: "file.go",
			},
			wantIsSymlink: false,
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotIsSymlink, err := IsSymbolLink(tt.args.file_path)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsSymbolLink() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotIsSymlink != tt.wantIsSymlink {
				t.Errorf("IsSymbolLink() gotIsSymlink = %v, want %v", gotIsSymlink, tt.wantIsSymlink)
			}
		})
	}
}

func TestCollectFilesAndDirMatchAnt(t *testing.T) {
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("获取当前工作目录失败：%v\n", err)
		return
	}
	fileDirType, _ := CollectFilesAndDirMatchAnt(currentDir, []string{"_test.go"}, []string{".go"})
	fmt.Println(fileDirType)
}

func TestIsFileExist(t *testing.T) {
	fmt.Println(IsFileExist("/Users/<USER>/baidu/cov/iUT"))
	fmt.Println(IsFileExist("/Users/<USER>/baidu/cov/iUT1"))
}

// TestFindOneOuterFile 是用于测试 FindOneOuterFile
// generated by Comate
func TestFindOneOuterFile(t *testing.T) {
	pList := []string{
		"/home/<USER>/a.txt",
		"/home/<USER>/test/a.txt",
		"/home/<USER>/test/b/a.txt",
	}
	assert.Equal(t, FindOneOuterFile(pList), "/home/<USER>/a.txt")
}

// TestGetAbsPath3 是用于测试 GetAbsPath3
// generated by Comate
func TestGetAbsPath3(t *testing.T) {
	wd, _ := filepath.Abs("testdata/dir1")
	assert.Equal(t, GetAbsPath("dir1", "testdata"), wd)
}

// TestGetAbsPath4 是用于测试 GetAbsPath4
// generated by Comate
func TestGetAbsPath4(t *testing.T) {
	assert.Equal(t, GetAbsPath("/root", "testdata"), "/root")
}

// TestGetAbsPath1 是用于测试 GetAbsPath1
// generated by Comate
func TestGetAbsPath1(t *testing.T) {
	assert.Equal(t, GetAbsPath("/root", ""), "/root")
}

// TestGetAbsPath2 是用于测试 GetAbsPath2
// generated by Comate
func TestGetAbsPath2(t *testing.T) {
	wd, _ := filepath.Abs("testdata/dir1")
	assert.Equal(t, GetAbsPath("dir1", ""), wd)
}

// TestFilterFilesByExt 是用于测试 FilterFilesByExt
// generated by Comate
func TestFilterFilesByExt(t *testing.T) {
	files := []string{
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/iUT.go",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/iUT_test.go",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/main.go",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/README.md",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/go.mod",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/LICENSE",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/go.sum",
	}
	ext := ".go"
	actual := FilterFilesByExt(files, ext)
	expected := []string{
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/iUT.go",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/iUT_test.go",
		"/home/<USER>/go/src/github.com/vibrantbyte/iut/main.go",
	}
	for i := range expected {
		if expected[i] != actual[i] {
			t.Errorf("expected %s actual %s", expected[i], actual[i])
		}
	}
}

// Test_findByNamePattern 是用于测试 _findByNamePattern
// generated by Comate
func Test_findByNamePattern(t *testing.T) {
	files, err := findByNamePattern("../helper", "*_test.go", []string{}, "file")
	assert.Nil(t, err)
	for _, v := range files {
		t.Log(v)
	}
	assert.NotEqual(t, len(files), 0)
}

// TestCollectFilesMatchAnt_2 是用于测试 CollectFilesMatchAnt_2
// generated by Comate
func TestCollectFilesMatchAnt_2(t *testing.T) {
	includes := []string{"**/*.yml", "**/test/*"}
	excludes := []string{}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_3 是用于测试 CollectFilesMatchAnt_3
// generated by Comate
func TestCollectFilesMatchAnt_3(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/*"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_4 是用于测试 CollectFilesMatchAnt_4
// generated by Comate
func TestCollectFilesMatchAnt_4(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/*", "**/*.yml"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_5 是用于测试 CollectFilesMatchAnt_5
// generated by Comate
func TestCollectFilesMatchAnt_5(t *testing.T) {
	includes := []string{}
	excludes := []string{}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_7 是用于测试 CollectFilesMatchAnt_7
// generated by Comate
func TestCollectFilesMatchAnt_7(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/**/file-test.yml"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_9 是用于测试 CollectFilesMatchAnt_9
// generated by Comate
func TestCollectFilesMatchAnt_9(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/dir-test/**"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_10 是用于测试 CollectFilesMatchAnt_10
// generated by Comate
func TestCollectFilesMatchAnt_10(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/dir-test/dir-1/**"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_1 是用于测试 CollectFilesMatchAnt_1
// generated by Comate
func TestCollectFilesMatchAnt_1(t *testing.T) {
	includes := []string{"**/*.yml"}
	excludes := []string{"**/test/*.yml"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_11 是用于测试 CollectFilesMatchAnt_11
// generated by Comate
func TestCollectFilesMatchAnt_11(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/dir-test/dir-1/file-1.txt"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_8 是用于测试 CollectFilesMatchAnt_8
// generated by Comate
func TestCollectFilesMatchAnt_8(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/test/file-test.yml"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestCollectFilesMatchAnt_6 是用于测试 CollectFilesMatchAnt_6
// generated by Comate
func TestCollectFilesMatchAnt_6(t *testing.T) {
	includes := []string{"**/*"}
	excludes := []string{"**/*"}
	antMatcher := AntMatcher{
		Includes: includes,
		Excludes: excludes,
	}
	files, _ := CollectFilesMatchAnt("../../testdata", antMatcher)
	for _, file := range files {
		t.Log(file)
	}
}

// TestPrefixPath 是用于测试 PrefixPath
// generated by Comate
func TestPrefixPath(t *testing.T) {
	assert.Equal(t, "test.txt", PrefixPath("test.txt/test.txt"))
	assert.Equal(t, "test", PrefixPath("test/test.txt"))
	assert.Equal(t, "test/", PrefixPath("test/test/test.txt"))
	assert.Equal(t, "test/", PrefixPath("test/test/"))
	assert.Equal(t, "", PrefixPath("test.txt"))
}

// TestFindFileByExt 是用于测试 FindFileByExt
// generated by Comate
func TestFindFileByExt(t *testing.T) {
	type args struct {
		root     string
		ext      string
		excludes string
	}
	dir, err := ioutil.TempDir("", "TestFindFileByExt")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(dir)
	os.Mkdir(path.Join(dir, "test1"), 0755)
	os.Mkdir(path.Join(dir, "test2"), 0755)
	os.Mkdir(path.Join(dir, "test2/test_resources"), 0755)
	os.Mkdir(path.Join(dir, "test2/test_resources/test3"), 0755)
	os.Create(path.Join(dir, "test1/a.xml"))
	os.Create(path.Join(dir, "test2/a.xml"))
	os.Create(path.Join(dir, "test2/b.xml"))
	os.Create(path.Join(dir, "test2/test_resources/a.xml"))
	os.Create(path.Join(dir, "test2/test_resources/test3/a.xml"))
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				root:     dir,
				ext:      ".xml",
				excludes: "",
			},
			want: []string{
				path.Join(dir, "test1/a.xml"),
				path.Join(dir, "test2/a.xml"),
				path.Join(dir, "test2/b.xml"),
				path.Join(dir, "test2/test_resources/a.xml"),
				path.Join(dir, "test2/test_resources/test3/a.xml"),
			},
			wantErr: false,
		},
		{
			name: "test",
			args: args{
				root:     dir,
				ext:      ".xml",
				excludes: "**/test_resources/**",
			},
			want: []string{
				path.Join(dir, "test1/a.xml"),
				path.Join(dir, "test2/a.xml"),
				path.Join(dir, "test2/b.xml"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FindFileByExt(tt.args.root, tt.args.ext, tt.args.excludes)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindFileByExt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !InSlice(got, tt.want[i]) {
					t.Errorf("FindFileByExt() = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

// TestFindFileByNamePattern 是用于测试 FindFileByNamePattern
// generated by Comate
func TestFindFileByNamePattern(t *testing.T) {
	type args struct {
		root    string
		pattern string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "test-1",
			args: args{
				root:    "/tmp/test",
				pattern: ".*",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "test-2",
			args: args{
				root:    "./testdata",
				pattern: ".*\\.txt",
			},
			want: []string{
				"./testdata/dir1/a.txt",
				"./testdata/dir1/dir11/a.txt",
				"./testdata/dir2/a.txt",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FindFileByNamePattern(tt.args.root, tt.args.pattern, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindFileByNamePattern() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			gotPath := make([]string, 0)
			for _, path := range got {
				gotPath = append(gotPath, filepath.Clean(path))
			}
			wantPath := make([]string, 0)
			for _, path := range tt.want {
				wantPath = append(wantPath, filepath.Clean(path))
			}
			if !equalSliceString(gotPath, wantPath) {
				t.Errorf("FindFileByNamePattern() = %v, want %v", got, tt.want)
			}
		})
	}
}

// equalSliceString 是用于测试 FindFileByNamePattern
// generated by Comate
func equalSliceString(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

// TestIsDir 是用于测试 IsDir
// generated by Comate
func TestIsDir(t *testing.T) {
	tmpFile, err := ioutil.TempFile("", "test")
	if err != nil {
		t.Errorf("create tmp file failed, err=%v", err)
		return
	}
	defer os.Remove(tmpFile.Name())
	if IsDir(tmpFile.Name()) {
		t.Errorf("expect false, got true")
	}
	tmpDir, err := ioutil.TempDir("", "test")
	if err != nil {
		t.Errorf("create tmp dir failed, err=%v", err)
		return
	}
	defer os.Remove(tmpDir)
	if !IsDir(tmpDir) {
		t.Errorf("expect true, got false")
	}
	if IsDir(filepath.Join(tmpDir, "not_exist_dir")) {
		t.Errorf("expect false, got true")
	}
}

// TestGetFileInfo 是用于测试 GetFileInfo
// generated by Comate
func TestGetFileInfo(t *testing.T) {
	type args struct {
		src string
	}
	tests := []struct {
		name string
		args args
		want os.FileInfo
	}{
		{
			name: "file not exist",
			args: args{src: "./not_exist_file"},
			want: nil,
		},
		{
			name: "file exist",
			args: args{src: "helper.go"},
			want: GetFileInfo("./helper.go"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetFileInfo(tt.args.src); got != tt.want {
				t.Errorf("GetFileInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestGetFileContent 是用于测试 GetFileContent
// generated by Comate
func TestGetFileContent(t *testing.T) {
	type args struct {
		path string
	}
	dir, _ := ioutil.TempDir("", "TestGetFileContent")
	defer os.RemoveAll(dir) // clean up
	filename := path.Join(dir, "test")
	ioutil.WriteFile(filename, []byte("hello"), 0644)
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{"file-exist", args{filename}, "hello", false},
		{"file-not-exist", args{filename + "-not-exist"}, "", true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetFileContent(tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFileContent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if string(got) != tt.want {
				t.Errorf("GetFileContent() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestFindDirByName 是用于测试 FindDirByName
// generated by Comate
func TestFindDirByName(t *testing.T) {
	tmpDir, err := ioutil.TempDir("", "TestFindDirByName")
	assert.Nil(t, err)
	defer os.RemoveAll(tmpDir)
	dir1 := path.Join(tmpDir, "dir1")
	dir2 := path.Join(tmpDir, "dir2")
	dir3 := path.Join(tmpDir, "dir3")
	err = os.MkdirAll(dir1, 0755)
	assert.Nil(t, err)
	err = os.MkdirAll(dir2, 0755)
	assert.Nil(t, err)
	err = os.MkdirAll(dir3, 0755)
	assert.Nil(t, err)
	dirs, err := FindDirByName(tmpDir, "dir1")
	assert.Nil(t, err)
	assert.Equal(t, 1, len(dirs))
	assert.Equal(t, dir1, dirs[0])
	dirs, err = FindDirByName(tmpDir, "dir2")
	assert.Nil(t, err)
	assert.Equal(t, 1, len(dirs))
	assert.Equal(t, dir2, dirs[0])
	dirs, err = FindDirByName(tmpDir, "dir3")
	assert.Nil(t, err)
	assert.Equal(t, 1, len(dirs))
	assert.Equal(t, dir3, dirs[0])
}

// TestIsValidHTMLDir_1 是用于测试 IsValidHTMLDir
// generated by Comate
func TestIsValidHTMLDir_1(t *testing.T) {
	assert.Equal(t, true, IsValidHTMLDir("/tmp/testdata/helper/html"))
}

// TestIsValidHTMLDir_2 是用于测试 IsValidHTMLDir
// generated by Comate
func TestIsValidHTMLDir_2(t *testing.T) {
	assert.Equal(t, false, IsValidHTMLDir("/tmp/testdata/helper/not_exist"))
}

// TestFindDirByNamePattern 是用于测试 FindDirByNamePattern
// generated by Comate
func TestFindDirByNamePattern(t *testing.T) {
	root := "./testdata/find"
	tests := []struct {
		pattern string
		want    []string
	}{
		{
			"**/*.txt",
			[]string{
				path.Join(root, "a.txt"),
				path.Join(root, "dir1", "a.txt"),
				path.Join(root, "dir1", "dir11", "a.txt"),
				path.Join(root, "dir2", "a.txt"),
			},
		},
		{
			"**/*",
			[]string{
				path.Join(root, "a.txt"),
				path.Join(root, "dir1", "a.txt"),
				path.Join(root, "dir1", "dir11", "a.txt"),
				path.Join(root, "dir1", "b.png"),
				path.Join(root, "dir2", "a.txt"),
				path.Join(root, "dir2", "b.png"),
				path.Join(root, "dir2", "dir22", "b.png"),
				path.Join(root, "c.png"),
			},
		},
		{
			"*",
			[]string{
				path.Join(root, "a.txt"),
				path.Join(root, "dir1"),
				path.Join(root, "dir2"),
				path.Join(root, "c.png"),
			},
		},
		{
			"dir*",
			[]string{
				path.Join(root, "dir1"),
				path.Join(root, "dir2"),
			},
		},
		{
			"*.txt",
			[]string{
				path.Join(root, "a.txt"),
			},
		},
		{
			"dir1/**",
			[]string{
				path.Join(root, "dir1", "a.txt"),
				path.Join(root, "dir1", "dir11", "a.txt"),
				path.Join(root, "dir1", "b.png"),
			},
		},
		{
			"dir1/**/*.txt",
			[]string{
				path.Join(root, "dir1", "a.txt"),
			},
		},
	}
	for _, test := range tests {
		t.Run(test.pattern, func(t *testing.T) {
			antMatcher := AntMatcher{
				Includes: []string{test.pattern},
				Excludes: []string{".**/*.png"},
			}
			if result, _ := CollectFilesMatchAnt(root, antMatcher); !stringSliceEqual(result, test.want) {
				t.Errorf("CollectFilesMatchAnt(%s) = %v, want %v", test.pattern, result, test.want)
			}
			if result, _ := FindDirByNamePattern(root, test.pattern, []string{".**/*.png"}); !stringSliceEqual(result, test.want) {
				t.Errorf("FindDirByNamePattern(%s) = %v, want %v", test.pattern, result, test.want)
			}
		})
	}
}

// stringSliceEqual 是用于测试 FindDirByNamePattern
// generated by Comate
func stringSliceEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}

	return true
}

// TestReadFile 是用于测试 ReadFile
// generated by Comate
func TestReadFile(t *testing.T) {
	type args struct {
		configFile string
	}
	testDir := path.Join(os.TempDir(), "test_read_file")
	os.MkdirAll(testDir, 0755)
	testFile := path.Join(testDir, "test.txt")
	os.WriteFile(testFile, []byte("hello world"), 0644)
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name:    "test read file",
			args:    args{configFile: testFile},
			want:    "hello world",
			wantErr: assert.NoError,
		},
		{
			name:    "test read file not exist",
			args:    args{configFile: path.Join(testDir, "not_exist")},
			want:    "",
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ReadFile(tt.args.configFile)
			tt.wantErr(t, nil)
			assert.Equalf(t, tt.want, got, "ReadFile(%v)", tt.args.configFile)
		})
	}
}
func TestDownloadFile(t *testing.T) {
	workdir := os.TempDir()
	jar := filepath.Join(workdir, "javaagent-1.1-client-SNAPSHOT.jar")
	os.Remove(jar)
	DownloadFile("https://cov-file.bj.bcebos.com/cov/javaagent/javaagent-1.1-client-SNAPSHOT.jar", workdir, "javaagent-1.1-client-SNAPSHOT.jar")
	md5 := FileMd5(jar)
	fmt.Println(md5)
}

// generated by Comate
func TestUnzip(t *testing.T) {
	zipFile := path.Join(os.TempDir(), "javaagent-1.1-client-SNAPSHOT.zip")
	DownloadFile("https://baidu-coverage.bj.bcebos.com/tools/javaagent/javaagent-1.1-client-SNAPSHOT.zip", os.TempDir(), "javaagent-1.1-client-SNAPSHOT.zip")

	err := Unzip(zipFile, os.TempDir())
	fmt.Println(err)
}

// TestEmptyFile 是用于测试 EmptyFile
// generated by Comate
func TestEmptyFile(t *testing.T) {
	testCases := []struct {
		Name     string
		Filename string
		Content  string
	}{
		{
			Name:     "empty file",
			Filename: path.Join(t.TempDir(), "empty"),
			Content:  "",
		},
		{
			Name:     "non-empty file",
			Filename: path.Join(t.TempDir(), "non-empty"),
			Content:  "hello world",
		},
	}
	for _, tc := range testCases {
		t.Run(tc.Name, func(t *testing.T) {
			err := ioutil.WriteFile(tc.Filename, []byte(tc.Content), 0644)
			assert.Nil(t, err)
			EmptyFile(tc.Filename)
			fi, err := os.Stat(tc.Filename)
			assert.Nil(t, err)
			assert.Equal(t, int64(0), fi.Size())
		})
	}
}
