package helper

import (
	"icode.baidu.com/baidu/cov/iUT/logger"
	"sync"
)

type Counter struct {
	mu    sync.Mutex
	value int
}

func (c *Counter) Increment() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value++
}

func (c *Counter) Decrement() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value--
}

func (c *Counter) Value() int {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.value
}

func (c *Counter) SetValue(value int) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value = value
}

type SafeMap struct {
	mu sync.RWMutex
	m  map[string]interface{}
}

func NewSafeMap() *SafeMap {
	return &SafeMap{
		m: make(map[string]interface{}),
	}
}

func (sm *SafeMap) Add(key string, value interface{}) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	sm.m[key] = value
}

func (sm *SafeMap) Delete(key string) {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	delete(sm.m, key)
	logger.Info("DELETE: key:%s", key)
}

func (sm *SafeMap) Get(key string) (value interface{}, exists bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	value, exists = sm.m[key]
	return
}

func (sm *SafeMap) GetAll() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	copy := make(map[string]interface{})
	for key, value := range sm.m {
		copy[key] = value
	}
	return copy
}

func (sm *SafeMap) Len() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return len(sm.m)
}

func (sm *SafeMap) Inc(key string) int {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	if value, exists := sm.m[key]; exists {
		sm.m[key] = value.(int) + 1
	} else {
		sm.m[key] = 1
	}

	logger.Info("INC: key:%s, value:%d", key, sm.m[key].(int))
	return sm.m[key].(int)
}

// Dec 返回该被测是否完成
func (sm *SafeMap) Dec(key string) bool {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	var val interface{}
	val = 0
	if value, exists := sm.m[key]; exists {
		if value.(int) == 1 {
			//delete(sm.m, key)
			return true
		} else {
			sm.m[key] = value.(int) - 1
			val = sm.m[key].(int)
		}
	}

	logger.Info("DEC: key:%s, value:%d", key, val)
	return false
}

// Exists 判断某个key是否存在
func (sm *SafeMap) Exists(key string) bool {
	_, ok := sm.m[key]
	return ok
}
