#!/bin/bash

# for UTAgent在windows下的exe二进制编译产出
# 流水线执行，镜像：iregistry.baidu-int.com/hub-official/go-mingw:1.21  [已同步hub]

set -x
HOMEDIR=`pwd`
OUTDIR=$HOMEDIR/output

go version
# 设置环境
go env -w GOPRIVATE=\*.baidu.com                        # 厂内 icode 等是私有库，不需要走代理和验证 sumdb
go env -w GOPROXY=https://goproxy.baidu-int.com,direct 	# 可以下载 github 等外部代码
go env -w CGO_ENABLED=1
go env

go mod tidy && go mod download

# for x86_64编译
GOARCH=amd64 GOOS=windows CXX=x86_64-w64-mingw32-g++ CC=x86_64-w64-mingw32-gcc go build -o iUT.exe
if [ $? -ne 0 ]; then
    echo "build failed!"
    exit 1
fi

# 打包
mkdir -p $OUTDIR/windows-amd64
mv iUT.exe $OUTDIR/windows-amd64/UTAgent.exe

# for x86编译
GOARCH=386 GOOS=windows CXX=i686-w64-mingw32-g++ CC=i686-w64-mingw32-gcc go build -o iUT.exe
if [ $? -ne 0 ]; then
    echo "build failed!"
    exit 1
fi

mkdir -p $OUTDIR/windows-386
mv iUT.exe $OUTDIR/windows-386/UTAgent.exe

tar -czvf output.tar.gz output
ls -lh

echo "build success"