# iUT (intelligent Unit Test) 技术参考文档

## 项目概述

iUT是百度开发的基于大模型能力的智能单元测试生成工具，通过AI技术自动生成单元测试并通过覆盖率分析提升代码质量。支持Java和Go两种编程语言。

## 核心架构

### 1. 整体架构层次
```
命令行接口层 (cmd/) 
    ↓
核心引擎层 (utagent/)
    ↓
AI推理层 (llm/) + 代码分析层 (tools/coverage/) + 执行验证层
    ↓
数据存储层 (mongo/, cache, memory)
```

### 2. 主要组件

#### UTGenerator - 主控制器
- 管理整个测试生成流程
- 协调三个核心队列：
  - `QueueForLLMCall`: 待发送模型推理的队列
  - `QueueForValidation`: 待验证的队列  
  - `QueueForRepair`: 待修复的队列
- 支持Java和Go语言的统一调度

#### UTAgent接口 - 语言抽象层
定义统一接口，支持多语言扩展：
```go
type UTAgent interface {
    ValidEnv() (bool, error)                    // 验证环境
    RunValid(caseInfo *models.CaseInfo) *models.RunResult  // 执行验证
    Repair(repairInfo *models.RepairInfo) (*models.RepairResult, string, error) // 修复
    CollectCoverageAndIsUp(newCoverage map[string]*coverage.CoverageInfo, cls string) (bool, *coverage.CoverageInfo)
    GetFileCoverage(classFullPath string) *coverage.CoverageInfo
    Call(genQueue chan<- any)  // 主要生成流程
}
```

## Java语言支持模块

### 1. Java UTAgent结构
```go
type UTAgent struct {
    Parser *java.Parser                    // Java代码解析器
    Environment *environment.Environment   // 环境管理
    Memory *base.Memory                    // 运行时内存
    JavaMemory *Memory                     // Java特定内存
    RepairAct *repair.Agent               // 修复代理
    
    WorkDir string                        // 工作目录
    RunCmd string                         // 运行命令
    FileMethods map[string]map[int]int    // 文件方法映射
    FileDiffLines map[string][]int        // 文件差异行
    
    MaxIterations int                     // 最大迭代次数
    RepairMaxIters int                    // 最大修复次数
    DesiredCoverage int                   // 目标覆盖率
}
```

### 2. Java测试生成流程

#### 2.1 环境检查与初始化
- 检查Java环境变量：`JAVA_HOME`, `MAVEN_HOME`, `M2_HOME`
- 验证构建工具：Maven/Gradle
- 检测JUnit版本和测试框架

#### 2.2 代码解析
- 使用Java Parser解析源代码结构
- 提取类信息、方法信息、依赖关系
- 识别测试框架和注解

#### 2.3 测试生成策略
支持三种生成模式：
- **FirstGen**: 首次生成，从零开始
- **CoverageRegen**: 基于覆盖率缺失的重新生成
- **GenWithScenario**: 基于场景的生成

#### 2.4 测试执行与验证
- 使用JavaAgent运行测试
- 集成Jacoco进行覆盖率收集
- 验证测试用例的正确性

### 3. Java环境管理

#### 3.1 JavaAgent执行器
```go
type JavaAgentRunner struct {
    ProjectRoot string      // 项目根目录
    SrcFilePath string      // 源文件路径
    UTFullFilePath string   // 测试文件完整路径
    UTClassName string      // 测试类名
    UTMethodName string     // 测试方法名
    JUnitVersion string     // JUnit版本
}
```

#### 3.2 覆盖率收集
- 使用Jacoco Agent收集覆盖率
- 生成jacoco.exec文件
- 解析XML格式的覆盖率报告

## Go语言支持模块

### 1. Go UTAgent结构
```go
type UTAgent struct {
    Parser *golang.Parser                 // Go代码解析器
    Environment *environment.Environment  // 环境管理
    Memory *base.Memory                   // 运行时内存
    goMemory *GoMemory                    // Go特定内存
    RepairAct *repair.Agent              // 修复代理
    
    WorkDir string                       // 工作目录
    RunCmd string                        // 运行命令
    FileMethods map[string]map[int]int   // 文件方法映射
    FileDiffLines map[string][]int       // 文件差异行
    
    MaxIterations int                    // 最大迭代次数
    RepairMaxIterations int              // 最大修复次数
    DesiredCoverage int                  // 目标覆盖率
}
```

### 2. Go测试生成流程

#### 2.1 环境检查
- 检查Go版本
- 验证Go modules配置
- 无需额外构建脚本修改

#### 2.2 代码解析
- 使用Go Parser解析源代码
- 提取包信息、函数信息、类型定义
- 识别Go标准testing包

#### 2.3 测试生成
支持两种生成模式：
- **FirstGen**: 首次生成
- **CoverageRegen**: 覆盖率驱动的重新生成

#### 2.4 测试执行
- 使用`go test`命令执行测试
- 通过`-coverprofile`参数收集覆盖率
- 支持overlay文件机制

### 3. Go覆盖率处理
```go
// Go覆盖率文件解析
func parseGoCoverageFile(filePath string) (map[string]*CoverageInfo, error) {
    // 解析go test -coverprofile生成的覆盖率文件
    // 格式：filename:startLine.startCol,endLine.endCol numStmt count
}
```

## 覆盖率分析模块

### 1. 核心数据结构
```go
type CoverageInfo struct {
    Coverage float64 `json:"coverage"`           // 覆盖率百分比
    MissedLines []int `json:"missed_lines"`      // 未覆盖行号
    CoveredLines []int `json:"covered_lines"`    // 已覆盖行号
    MissedBranches []int `json:"missed_branches"` // 未覆盖分支
    CoveredBranches []int `json:"covered_branches"` // 已覆盖分支
}
```

### 2. 覆盖率计算
```go
func (c *CoverageInfo) GetCoveragePercent() float64 {
    if len(c.MissedLines)+len(c.CoveredLines) > 0 {
        return helper.Divide(len(c.CoveredLines), 
                           len(c.MissedLines)+len(c.CoveredLines), 4) * 100.00
    }
    return 0
}
```

### 3. 覆盖率提升检测
```go
func IsCoverageUp(newCover, oldCover *CoverageInfo) bool {
    // 检查新覆盖率是否比旧覆盖率有提升
    // 比较覆盖行数、分支覆盖等指标
}
```

### 4. 覆盖率合并
```go
func (c *CoverageInfo) Merge(other *CoverageInfo) {
    // 将新的覆盖率信息合并到现有覆盖率中
    // 更新已覆盖行，移除已覆盖的未覆盖行
}
```

## AI推理模块

### 1. 支持的模型
```go
const (
    DC         = "java_llm"      // DeepCode模型
    EB4        = "ERNIE-4.0-8K"  // 文心一言4.0
    EB35       = "ERNIE-Bot"     // 文心一言3.5
    ERNIESpeed = "ERNIE-Speed-8K" // 文心一言高速版
    DS25       = "ds25"          // 内部模型
)
```

### 2. 模型调用接口
```go
type BaseLLMService interface {
    Inference(prompt string) (string, error)  // 推理接口
    IsAccess() error                          // 访问检查
}
```

### 3. 推理流程
- 构建Prompt（包含源代码、测试框架、覆盖率信息）
- 调用大模型进行推理
- 解析返回结果提取测试代码
- 支持重试机制

## 核心工作流程

### 1. 主流程
```
1. 命令行解析参数
2. 初始化UTGenerator
3. 创建语言特定的UTAgent
4. 解析源代码获取方法列表
5. 启动三个后台队列处理器
6. 遍历方法发起生成请求
7. 验证生成的测试用例
8. 收集覆盖率并检查提升
9. 必要时进行修复
10. 生成最终报告
```

### 2. 迭代优化流程
```
for each method:
    1. 获取当前覆盖率
    2. 判断生成策略（首次/覆盖率驱动）
    3. 调用LLM生成测试
    4. 验证测试用例
    5. 收集新覆盖率
    6. 检查是否达到目标覆盖率
    7. 如未达标且未超过最大迭代次数，继续迭代
```

### 3. 修复机制
```
if test validation failed:
    1. 分析失败原因（编译错误/运行时错误）
    2. 构建修复Prompt
    3. 调用LLM进行修复
    4. 重新验证修复后的测试
    5. 更新覆盖率信息
```

## 关键配置参数

### 1. 生成参数
- `MaxIterations`: 最大迭代次数（默认3次）
- `RepairMaxIterations`: 最大修复次数（默认2次）
- `DesiredCoverage`: 目标覆盖率（默认80%）

### 2. 模型参数
- `DefaultGenModel`: 默认生成模型
- `DefaultRegenModel`: 默认重新生成模型
- `DefaultRepairModel`: 默认修复模型

### 3. 超时设置
- `UTRunTimeout`: 单测运行超时时间
- Token限制：不同模型有不同的Token限制

## 使用建议

### 1. 环境准备
- Java项目：确保JAVA_HOME、MAVEN_HOME正确配置
- Go项目：确保Go版本>=1.19，启用Go modules

### 2. 最佳实践
- 设置合理的目标覆盖率（建议70-85%）
- 对于复杂方法，适当增加最大迭代次数
- 定期清理生成的临时文件

### 3. 故障排查
- 检查环境变量配置
- 验证网络连接（AI模型调用）
- 查看详细日志定位问题

## 详细实现细节

### 1. Prompt构建策略

#### Java Prompt构建
```go
// 首次生成Prompt包含：
// 1. 源代码上下文
// 2. 目标方法信息
// 3. 依赖类信息
// 4. 测试框架规范
// 5. Mock框架使用指南

// 覆盖率驱动Prompt额外包含：
// 1. 当前覆盖率信息
// 2. 未覆盖的具体行号
// 3. 分支覆盖情况
// 4. 已有测试用例
```

#### Go Prompt构建
```go
// Go语言Prompt特点：
// 1. 包级别的上下文
// 2. 接口和结构体定义
// 3. 错误处理模式
// 4. 并发安全考虑
// 5. 标准testing包规范
```

### 2. 测试验证机制

#### Java测试验证
```go
type JavaAgentRunner struct {
    ProjectRoot string      // 项目根目录
    SrcFilePath string      // 源文件路径
    UTFullFilePath string   // 测试文件完整路径
    UTClassName string      // 测试类名
    UTMethodName string     // 测试方法名
    JUnitVersion string     // JUnit版本
    LocalRepoPath string    // 本地仓库路径
}

// 验证流程：
// 1. 生成临时测试文件
// 2. 注释掉其他测试方法（避免干扰）
// 3. 使用JavaAgent + Jacoco执行测试
// 4. 收集覆盖率和执行结果
// 5. 解析错误信息用于修复
```

#### Go测试验证
```go
// Go测试验证流程：
// 1. 创建overlay文件（临时文件系统）
// 2. 使用go test -overlay执行
// 3. 通过-coverprofile收集覆盖率
// 4. 解析测试输出和覆盖率文件
// 5. 支持特定函数测试（-run参数）
```

### 3. 错误检测与修复

#### 源代码Bug检测
```go
func DetectSrcBug(caseInfo models.CaseInfo, results *models.RunResult) (bool, string) {
    // 检测是否为源代码问题：
    // 1. 编译错误分析
    // 2. 运行时异常分析
    // 3. 逻辑错误识别
    // 4. 返回是否为源码Bug及原因
}
```

#### 自动修复策略
```go
// 修复类型：
// 1. 编译错误修复（导入缺失、语法错误）
// 2. 运行时错误修复（空指针、类型转换）
// 3. 断言错误修复（期望值调整）
// 4. Mock配置修复（依赖注入问题）

// 修复Prompt包含：
// 1. 原始测试代码
// 2. 错误信息详情
// 3. 源代码上下文
// 4. 修复指导原则
```

### 4. 内存管理与缓存

#### Memory结构
```go
type Memory struct {
    Coverages map[string]*CoverageRecord    // 覆盖率记录
    FileProcess map[string]*Process         // 文件处理进度
    TotalTimer *helper.Timer               // 总计时器
    FileProcessInQueue float64             // 文件处理队列进度
    TmpValidTestFile map[string]*models.TestFile // 临时有效测试文件
    Diffs map[string][]int                 // 代码差异信息
    GeneratedUnitTest map[string][]models.GenUnitTest // 生成的单测
}
```

#### 缓存策略
```go
// SafeMap用于线程安全的缓存：
// 1. 方法级别的生成状态
// 2. 迭代次数控制
// 3. 覆盖率变化追踪
// 4. 临时文件管理
```

### 5. 并发处理机制

#### 队列处理
```go
// 三个核心队列的并发处理：
// 1. inferenceInBackground(): 处理LLM推理请求
// 2. validationInBackground(): 处理测试验证
// 3. repairInBackground(): 处理错误修复

// 队列大小配置：
const (
    GenerateQueueSize = 10000  // 生成队列大小
    ValidQueueSize = 50        // 验证队列大小
    RepairQueueSize = 10000    // 修复队列大小
)
```

#### 并发安全
```go
// 使用sync.WaitGroup协调并发任务
// 使用context.Context支持优雅退出
// 使用SafeMap保证线程安全的状态管理
```

### 6. 配置管理

#### 环境配置
```toml
# env.toml.template示例
[llm]
model = "ds25"
endpoint = ""
api_key = ""

[coverage]
desired_coverage = 80
max_iterations = 3
repair_max_iterations = 2

[timeout]
ut_run_timeout = 60
llm_timeout = 30
```

#### 动态配置
```go
// 支持环境变量覆盖：
// DEFAULT_GEN_MODEL: 默认生成模型
// DEFAULT_REGEN_MODEL: 默认重新生成模型
// DEFAULT_REPAIR_MODEL: 默认修复模型
```

### 7. 日志与监控

#### 日志级别
```go
// 支持多级别日志：
// 1. Debug: 详细调试信息
// 2. Info: 一般信息
// 3. Warn: 警告信息
// 4. Error: 错误信息

// 特殊日志功能：
// 1. 缩进日志（InfoT, DebugT）
// 2. 进度追踪
// 3. 性能计时
// 4. 临时文件保存（调试用）
```

#### 状态通知
```go
// IDE集成通知：
// 1. 任务状态更新
// 2. 覆盖率变化通知
// 3. 错误信息反馈
// 4. 进度百分比更新
```

### 8. 扩展性设计

#### 新语言支持
```go
// 添加新语言支持的步骤：
// 1. 实现UTAgent接口
// 2. 创建语言特定的Parser
// 3. 实现Environment管理
// 4. 添加测试执行器
// 5. 配置覆盖率收集
// 6. 更新主流程调度
```

#### 新模型集成
```go
// 添加新AI模型的步骤：
// 1. 实现BaseLLMService接口
// 2. 添加模型配置
// 3. 实现Prompt适配
// 4. 配置Token限制
// 5. 添加错误处理
```

## 性能优化建议

### 1. 生成效率优化
- 合理设置并发队列大小
- 使用缓存避免重复解析
- 优化Prompt长度减少Token消耗
- 实现智能重试机制

### 2. 覆盖率收集优化
- 增量覆盖率计算
- 并行执行测试用例
- 覆盖率缓存机制
- 差异化覆盖率分析

### 3. 内存使用优化
- 及时清理临时文件
- 使用对象池减少GC压力
- 流式处理大文件
- 合理的缓存淘汰策略

## 实际应用场景与案例

### 1. 增量测试生成（基于Git Diff）
```go
// 支持基于代码变更的增量测试生成
// 1. 解析git diff获取变更行
// 2. 识别影响的方法和类
// 3. 针对性生成测试用例
// 4. 计算增量覆盖率

// 使用场景：
// - CI/CD流水线中的自动测试生成
// - 代码审查阶段的测试补充
// - 重构后的测试验证
```

### 2. 方法级精准生成
```go
// 支持为特定方法生成测试
// 1. 精确定位目标方法
// 2. 分析方法依赖关系
// 3. 生成针对性测试用例
// 4. 计算方法级覆盖率

// 使用场景：
// - 新增方法的测试补充
// - 复杂方法的深度测试
// - 边界条件的测试覆盖
```

### 3. 批量文件处理
```go
// 支持批量处理多个文件
// 1. 并行解析多个源文件
// 2. 智能调度生成任务
// 3. 统一收集覆盖率
// 4. 生成汇总报告

// 使用场景：
// - 项目级测试覆盖率提升
// - 遗留代码的测试补充
// - 新项目的测试初始化
```

## 错误处理与故障排查

### 1. 常见错误类型

#### 环境配置错误
```bash
# Java环境问题
JAVA_HOME not set
Maven/Gradle not found
JUnit version mismatch

# Go环境问题
Go version too old
Module not initialized
Build constraints not met
```

#### 代码解析错误
```go
// 解析失败的常见原因：
// 1. 语法错误导致解析失败
// 2. 依赖缺失无法解析类型
// 3. 编码格式不支持
// 4. 文件路径包含特殊字符
```

#### 模型调用错误
```go
// LLM调用失败的处理：
// 1. 网络连接问题 -> 重试机制
// 2. Token限制超出 -> Prompt优化
// 3. 模型服务异常 -> 降级策略
// 4. 认证失败 -> 配置检查
```

### 2. 调试技巧

#### 日志分析
```go
// 关键日志信息：
// 1. 文件解析状态
// 2. 方法生成进度
// 3. 覆盖率变化
// 4. 错误详细信息
// 5. 性能计时数据
```

#### 临时文件检查
```go
// 保存的调试文件：
// 1. Prompt内容（*-src.txt）
// 2. 模型响应（*-tgt.txt）
// 3. 生成的测试代码
// 4. 覆盖率报告
// 5. 错误日志
```

## 最佳实践指南

### 1. 项目配置最佳实践

#### Java项目配置
```xml
<!-- Maven项目建议配置 -->
<properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <junit.version>4.13.2</junit.version>
    <jacoco.version>0.8.7</jacoco.version>
</properties>

<dependencies>
    <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>3.12.4</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

#### Go项目配置
```go
// go.mod建议配置
module your-project

go 1.19

require (
    github.com/stretchr/testify v1.8.0
    github.com/golang/mock v1.6.0
)
```

### 2. 代码编写最佳实践

#### 便于测试的代码结构
```java
// Java: 避免静态依赖，使用依赖注入
public class UserService {
    private final UserRepository userRepository;

    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    public User findUser(Long id) {
        return userRepository.findById(id);
    }
}
```

```go
// Go: 使用接口抽象依赖
type UserService struct {
    repo UserRepository
}

type UserRepository interface {
    FindByID(id int64) (*User, error)
}

func (s *UserService) FindUser(id int64) (*User, error) {
    return s.repo.FindByID(id)
}
```

### 3. 覆盖率目标设置

#### 不同类型代码的覆盖率建议
```go
// 业务逻辑代码：80-90%
// 工具类代码：90-95%
// 配置类代码：60-70%
// 接口适配代码：70-80%
// 异常处理代码：85-95%
```

### 4. 性能调优建议

#### 并发参数调优
```go
// 根据机器配置调整队列大小
// CPU密集型：队列大小 = CPU核心数
// IO密集型：队列大小 = CPU核心数 * 2-4
// 内存限制：适当减小队列大小避免OOM
```

#### 模型调用优化
```go
// 1. 合理设置Token限制
// 2. 使用缓存避免重复调用
// 3. 批量处理相似请求
// 4. 实现智能重试策略
```

## 扩展开发指南

### 1. 自定义生成策略
```go
// 实现自定义UtGen接口
type CustomUtGen struct {
    Model *llm.ModelConf
    Input *basegen.Input
}

func (g *CustomUtGen) Predict() (string, error) {
    // 自定义生成逻辑
    prompt := g.buildCustomPrompt()
    return llm.Inference(prompt, g.Model)
}

func (g *CustomUtGen) GetName() string {
    return "自定义生成策略"
}
```

### 2. 自定义覆盖率收集器
```go
// 实现自定义覆盖率收集
type CustomCoverageCollector struct {
    workDir string
}

func (c *CustomCoverageCollector) CollectCoverage(testFile string) (*coverage.CoverageInfo, error) {
    // 自定义覆盖率收集逻辑
    return &coverage.CoverageInfo{}, nil
}
```

### 3. 自定义修复策略
```go
// 实现自定义修复逻辑
type CustomRepairAgent struct {
    language string
    model *llm.ModelConf
}

func (r *CustomRepairAgent) Execute(input models.RepairInput) (*models.RepairResult, string, error) {
    // 自定义修复逻辑
    return &models.RepairResult{}, "", nil
}
```

## 总结

iUT项目是一个完整的AI驱动的单元测试生成解决方案，具有以下特点：

1. **多语言支持**：Java和Go的完整支持，易于扩展到其他语言
2. **智能生成**：基于大模型的代码理解和测试生成能力
3. **覆盖率驱动**：以覆盖率为目标的迭代优化机制
4. **自动修复**：智能的错误检测和修复能力
5. **高度可配置**：灵活的参数配置和扩展机制
6. **生产就绪**：完善的错误处理、日志记录和监控能力

这个技术参考文档详细总结了iUT项目的核心逻辑和实现细节，为其他大模型执行单测生成提供了全面的技术参考和最佳实践指导。
