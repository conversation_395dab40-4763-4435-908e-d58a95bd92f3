package conf_parser

import (
	"fmt"
	"github.com/BurntSushi/toml"
	"github.com/toolkits/file"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"icode.baidu.com/baidu/cov/iUT/utagent/models"
	"os"
	"path"
	"sync"
)

var userConfig *models.UserConfig
var once sync.Once

// getConfPath 函数返回配置文件env.toml的路径
//
// P0:如果用户主目录下存在.UTAgent/env.toml文件，则返回该文件路径
// P1:如果存在UTAgent_HOME环境变量，则返回该目录下的conf/env.toml文件路径
// P2:否则返回程序所在目录下的conf/env.toml文件路径
//
// 返回值：
// string类型，表示配置文件env.toml的路径
func getConfPath() string {
	home, _ := os.UserHomeDir()
	if home != "" && file.IsExist(path.Join(home, ".UTAgent", "env.toml")) {
		return path.Join(home, ".UTAgent", "env.toml")
	}

	// 存在UTAgent_HOME环境变量时，使用该目录下的conf/env.toml
	if os.Getenv("UTAgent_HOME") != "" {
		return path.Join(os.Getenv("UTAgent_HOME"), "conf/env.toml")
	}
	// 否则使用程序所在目录下的conf/env.toml
	binPath, _ := os.Executable()
	configPath := path.Join(path.Dir(binPath), "conf/env.toml")
	return configPath
}

// GetUserConfig 函数返回一个指向models.UserConfig结构体的指针
func GetUserConfig() *models.UserConfig {
	once.Do(func() {
		configPath := getConfPath()
		if !file.IsExist(configPath) {
			return
		}
		var c = new(models.UserConfig)
		_, _ = toml.DecodeFile(configPath, c)
		userConfig = c
	})
	return userConfig
}

func SetUserConfig(c *models.UserConfig) {
	configPath := getConfPath()
	if !file.IsExist(file.Dir(configPath)) {
		os.MkdirAll(file.Dir(configPath), 0755)
	}
	if file.IsExist(configPath) {
		err := os.Remove(configPath)
		if err != nil {
			fmt.Println(err)
			return
		}
	}
	f, err := os.OpenFile(configPath, os.O_CREATE|os.O_WRONLY, 0644)
	err = toml.NewEncoder(f).Encode(c)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func PrintNewConf() {
	filePath := getConfPath()
	logger.Info("成功更新配置文件！\n配置文件路径: %s\n", filePath)
	if !file.IsExist(filePath) {
		return
	}
	data, _ := os.ReadFile(filePath)
	logger.Info("内容: \n%s\n", string(data))
}
func GetLicense() string {
	conf := GetUserConfig()
	if conf == nil {
		return ""
	}
	return conf.License
}

func SetLicenseToConfFile(license string) {
	old := GetUserConfig()
	if old != nil {
		old.License = license
		SetUserConfig(old)
	} else {
		newConf := &models.UserConfig{
			License: license,
		}
		SetUserConfig(newConf)
	}
}
