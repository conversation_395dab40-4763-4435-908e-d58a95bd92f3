package authentication

import (
	"reflect"
	"testing"
)

func TestSkipLicense(t *testing.T) {
	tests := []struct {
		name string
		want bool
	}{
		{
			name: "case1",
			want: false,
		},
		{
			name: "case2",
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SkipLicense(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rrorf("SkipLicense() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCheckUnitTestIntelligenceApplyStatus(t *testing.T) {
	type args struct {
		license string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "case1",
			args:    args{license: "a46abb9c-535e-4070-9d15-d635a4cdc199"}, // 企业已开通智能体账户
			want:    true,
			wantErr: false,
		},
		{
			name:    "case2",
			args:    args{license: "3dc06253-60da-4caf-9a51-5ebb595f6e89"}, // 个人账户
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CheckUnitTestIntelligenceApplyStatus(tt.args.license)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckUnitTestIntelligenceApplyStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckUnitTestIntelligenceApplyStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
