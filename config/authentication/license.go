package authentication

import (
	"encoding/json"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/cov/iUT/config/conf_parser"
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"icode.baidu.com/baidu/cov/iUT/logger"
	"time"
)

func CheckLicenseValid(license string) (bool, error) {
	params := make(map[string]interface{})
	params["recordUsed"] = "false"
	dataMap, err := httputil.HTTPGet("http://comate.baidu.com/api/key/valid/"+license, params)
	if err != nil {
		return false, err
	}
	// license合法时，status==200
	if dataMap["status"].(string) == "OK" {
		return true, nil
	}
	// 返回鉴权接口的错误提示
	return false, fmt.Errorf(dataMap["message"].(string)) // KeyDisabledMsg
}

func CheckUnitTestIntelligenceApplyStatus(license string) (bool, error) {

	dataMap, err := httputil.Get("http://comate.baidu.com/api/config/" + license)
	// 检测返回response中data字段中的applyStatus中unitTestIntelligenceApplyStatus是否为apply
	if err != nil {
		return false, err
	}
	if response, ok := dataMap.(map[string]interface{}); ok {
		res, err := json.Marshal(response)
		jsonData := make(map[string]interface{})
		err = json.Unmarshal(res, &jsonData)
		if err == nil {
			data := jsonData["data"].(map[string]interface{})
			if data != nil && data["applyStatus"] != nil {
				applyStatus := data["applyStatus"].(map[string]interface{})["unitTestIntelligenceApplyStatus"].(string)
				if applyStatus == "approved" {
					return true, nil
				}
			}
		}
	}
	return false, errors.New("当前企业未开通智能体功能，请到官网申请开通。")

}

func SkipLicense() bool {
	data, err := httputil.Get("https://comate.baidu.com/api/plugin/ut/license/skip")
	if err != nil {
		return false
	}
	if typedData, ok := data.(map[string]interface{}); ok {
		if dataValue, ok := typedData["data"].(bool); ok {
			if dataValue {
				logger.Info("临时跳过license")
				return true
			}
		}
	} else {
		return timeAllowed()
	}

	return false

}
func timeAllowed() bool {
	// 获取当前时间
	now := time.Now()

	// 提取当前日期的年、月、日
	_, month, day := now.Date()

	// 判断是否是10月20日
	if month == time.October && day <= 20 {
		return true
	} else {
		return false
	}
}

func IsAccessValid(license string) error {
	if httputil.IsBaiduInnerIp() {
		// 内部用户，不校验license
		return nil
	}
	// 非内网环境
	// 先判断是否指定了license
	// 如果指定了license，直接使用指定的license
	// 否则从配置文件中读取
	if license == "" {
		// 未指定license，从配置文件中读取
		license = conf_parser.GetLicense()
		if SkipLicense() {
			return nil
		}
	}

	if license == "" {
		return fmt.Errorf("license校验失败：未检测到任何license授权！")
	}

	if isValid, err := CheckLicenseValid(license); !isValid {
		logger.Error("license校验失败：%s", err.Error())
		return err
	}
	if isApproved, err := CheckUnitTestIntelligenceApplyStatus(license); !isApproved {
		logger.Error("license校验失败：%s", err.Error())
		return err
	}
	logger.Info("license校验成功")
	return nil
}
