package config

import (
	"icode.baidu.com/baidu/cov/iUT/helper/httputil"
	"os"
)

// IsDebugEnv 判断当前环境是否为测试环境
//
// 返回值：
// bool - 如果当前环境为测试环境，则返回true；否则返回false
func IsDebugEnv() bool {
	if os.Getenv("UTAgent_DEBUG") != "" {
		return true
	}
	return false
}

func SetDebug() {
	os.Setenv("UTAgent_DEBUG", "true")
}

func SetServerEndpoint(endpoint string) {
	os.Setenv("UT_SERVER_ENDPOINT", endpoint)
}

const OnlineHost = "http://************:8200"
const TestHost = "http://smart-ut.test.cov.appspace.baidu.com"
const ComateHost = "https://comate.baidu.com"

const GenerateAPIPath = "/api/plugin/ut/llm/generate"
const GetSensitiveWordsAPIPath = "/api/plugin/ut/getSensitiveWords"
const FormatAPIPath = "/api/plugin/ut/format"

// GetHost 返回当前环境下的主机地址
func GetHost() string {
	if os.Getenv("UT_SERVER_ENDPOINT") != "" {
		return os.Getenv("UT_SERVER_ENDPOINT")
	}
	if IsDebugEnv() {
		return TestHost
	}
	var host = OnlineHost
	if !httputil.IsBaiduInnerIp() {
		// 外网环境需要通过comate来转发
		host = ComateHost
	}
	return host
}
