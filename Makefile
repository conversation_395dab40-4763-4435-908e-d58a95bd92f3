# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# 设置编译时所需要的 go 环境
export GOENV = $(HOMEDIR)/go.env

GO      := go
GOMOD   := $(GO) mod
GOPATH  := $(shell $(GO) env GOPATH)
GOBUILD := $(GO) build
GOTEST  := $(GO) test -race -timeout 30s -gcflags="-N -l"
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")

# make, make all
all: prepare compile package

set-env:
	$(GO) env

#make prepare, download dependencies
prepare: gomod

gomod: set-env
	$(GOMOD) tidy
	$(GOMOD) download -x || $(GOMOD) download -x

#make compile
compile: build

build:
	$(GOBUILD) -o $(HOMEDIR)/UTAgent
	chmod a+x $(HOMEDIR)/UTAgent

# make test, test your code
test: prepare test-case
test-case:
	$(GOTEST) -v -cover $(GOPKGS)

# make package
package: package-bin
package-bin:
	export
	rm -rf $(OUTDIR)
	mkdir -p $(OUTDIR)/$(AGENT_OS)-$(AGENT_ARCH)
	mv UTAgent $(OUTDIR)/$(AGENT_OS)-$(AGENT_ARCH)/
	mv conf $(OUTDIR)/$(AGENT_OS)-$(AGENT_ARCH)/
	mv scripts $(OUTDIR)/

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/UTAgent

# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build